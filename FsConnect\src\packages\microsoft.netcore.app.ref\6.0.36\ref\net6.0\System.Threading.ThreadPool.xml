﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading.ThreadPool</name>
  </assembly>
  <members>
    <member name="T:System.Threading.IThreadPoolWorkItem">
      <summary>Represents a work item that can be executed by the <see cref="T:System.Threading.ThreadPool" />.</summary>
    </member>
    <member name="M:System.Threading.IThreadPoolWorkItem.Execute">
      <summary>Executes the work item on the thread pool.</summary>
    </member>
    <member name="T:System.Threading.RegisteredWaitHandle">
      <summary>Represents a handle that has been registered when calling <see cref="M:System.Threading.ThreadPool.RegisterWaitForSingleObject(System.Threading.WaitHandle,System.Threading.WaitOrTimerCallback,System.Object,System.UInt32,System.Boolean)" />. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Threading.RegisteredWaitHandle.Unregister(System.Threading.WaitHandle)">
      <summary>Cancels a registered wait operation issued by the <see cref="M:System.Threading.ThreadPool.RegisterWaitForSingleObject(System.Threading.WaitHandle,System.Threading.WaitOrTimerCallback,System.Object,System.UInt32,System.Boolean)" /> method.</summary>
      <param name="waitObject">The <see cref="T:System.Threading.WaitHandle" /> to be signaled.</param>
      <returns>
        <see langword="true" /> if the function succeeds; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Threading.ThreadPool">
      <summary>Provides a pool of threads that can be used to execute tasks, post work items, process asynchronous I/O, wait on behalf of other threads, and process timers.</summary>
    </member>
    <member name="M:System.Threading.ThreadPool.BindHandle(System.IntPtr)">
      <summary>Binds an operating system handle to the <see cref="T:System.Threading.ThreadPool" />.</summary>
      <param name="osHandle">An <see cref="T:System.IntPtr" /> that holds the handle. The handle must have been opened for overlapped I/O on the unmanaged side.</param>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <returns>
        <see langword="true" /> if the handle is bound; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Threading.ThreadPool.BindHandle(System.Runtime.InteropServices.SafeHandle)">
      <summary>Binds an operating system handle to the <see cref="T:System.Threading.ThreadPool" />.</summary>
      <param name="osHandle">A <see cref="T:System.Runtime.InteropServices.SafeHandle" /> that holds the operating system handle. The handle must have been opened for overlapped I/O on the unmanaged side.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="osHandle" /> is <see langword="null" />.</exception>
      <returns>
        <see langword="true" /> if the handle is bound; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Threading.ThreadPool.GetAvailableThreads(System.Int32@,System.Int32@)">
      <summary>Retrieves the difference between the maximum number of thread pool threads returned by the <see cref="M:System.Threading.ThreadPool.GetMaxThreads(System.Int32@,System.Int32@)" /> method, and the number currently active.</summary>
      <param name="workerThreads">The number of available worker threads.</param>
      <param name="completionPortThreads">The number of available asynchronous I/O threads.</param>
    </member>
    <member name="M:System.Threading.ThreadPool.GetMaxThreads(System.Int32@,System.Int32@)">
      <summary>Retrieves the number of requests to the thread pool that can be active concurrently. All requests above that number remain queued until thread pool threads become available.</summary>
      <param name="workerThreads">The maximum number of worker threads in the thread pool.</param>
      <param name="completionPortThreads">The maximum number of asynchronous I/O threads in the thread pool.</param>
    </member>
    <member name="M:System.Threading.ThreadPool.GetMinThreads(System.Int32@,System.Int32@)">
      <summary>Retrieves the minimum number of threads the thread pool creates on demand, as new requests are made, before switching to an algorithm for managing thread creation and destruction.</summary>
      <param name="workerThreads">When this method returns, contains the minimum number of worker threads that the thread pool creates on demand.</param>
      <param name="completionPortThreads">When this method returns, contains the minimum number of asynchronous I/O threads that the thread pool creates on demand.</param>
    </member>
    <member name="M:System.Threading.ThreadPool.QueueUserWorkItem(System.Threading.WaitCallback)">
      <summary>Queues a method for execution. The method executes when a thread pool thread becomes available.</summary>
      <param name="callBack">A <see cref="T:System.Threading.WaitCallback" /> that represents the method to be executed.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callBack" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">The common language runtime (CLR) is hosted, and the host does not support this action.</exception>
      <returns>
        <see langword="true" /> if the method is successfully queued; <see cref="T:System.NotSupportedException" /> is thrown if the work item could not be queued.</returns>
    </member>
    <member name="M:System.Threading.ThreadPool.QueueUserWorkItem(System.Threading.WaitCallback,System.Object)">
      <summary>Queues a method for execution, and specifies an object containing data to be used by the method. The method executes when a thread pool thread becomes available.</summary>
      <param name="callBack">A <see cref="T:System.Threading.WaitCallback" /> representing the method to execute.</param>
      <param name="state">An object containing data to be used by the method.</param>
      <exception cref="T:System.NotSupportedException">The common language runtime (CLR) is hosted, and the host does not support this action.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callBack" /> is <see langword="null" />.</exception>
      <returns>
        <see langword="true" /> if the method is successfully queued; <see cref="T:System.NotSupportedException" /> is thrown if the work item could not be queued.</returns>
    </member>
    <member name="M:System.Threading.ThreadPool.QueueUserWorkItem``1(System.Action{``0},``0,System.Boolean)">
      <summary>Queues a method specified by an <see cref="T:System.Action`1" /> delegate for execution, and provides data to be used by the method. The method executes when a thread pool thread becomes available.</summary>
      <param name="callBack">An <see cref="T:System.Action`1" /> representing the method to execute.</param>
      <param name="state">An object containing data to be used by the method.</param>
      <param name="preferLocal">
        <see langword="true" /> to prefer queueing the work item in a queue close to the current thread; <see langword="false" /> to prefer queueing the work item to the thread pool's shared queue.</param>
      <typeparam name="TState">The type of elements of <paramref name="state" />.</typeparam>
      <returns>
        <see langword="true" /> if the method is successfully queued; <see cref="T:System.NotSupportedException" /> is thrown if the work item could not be queued.</returns>
    </member>
    <member name="M:System.Threading.ThreadPool.RegisterWaitForSingleObject(System.Threading.WaitHandle,System.Threading.WaitOrTimerCallback,System.Object,System.Int32,System.Boolean)">
      <summary>Registers a delegate to wait for a <see cref="T:System.Threading.WaitHandle" />, specifying a 32-bit signed integer for the time-out in milliseconds.</summary>
      <param name="waitObject">The <see cref="T:System.Threading.WaitHandle" /> to register. Use a <see cref="T:System.Threading.WaitHandle" /> other than <see cref="T:System.Threading.Mutex" />.</param>
      <param name="callBack">The <see cref="T:System.Threading.WaitOrTimerCallback" /> delegate to call when the <paramref name="waitObject" /> parameter is signaled.</param>
      <param name="state">The object that is passed to the delegate.</param>
      <param name="millisecondsTimeOutInterval">The time-out in milliseconds. If the <paramref name="millisecondsTimeOutInterval" /> parameter is 0 (zero), the function tests the object's state and returns immediately. If <paramref name="millisecondsTimeOutInterval" /> is -1, the function's time-out interval never elapses.</param>
      <param name="executeOnlyOnce">
        <see langword="true" /> to indicate that the thread will no longer wait on the <paramref name="waitObject" /> parameter after the delegate has been called; <see langword="false" /> to indicate that the timer is reset every time the wait operation completes until the wait is unregistered.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsTimeOutInterval" /> parameter is less than -1.</exception>
      <returns>The <see cref="T:System.Threading.RegisteredWaitHandle" /> that encapsulates the native handle.</returns>
    </member>
    <member name="M:System.Threading.ThreadPool.RegisterWaitForSingleObject(System.Threading.WaitHandle,System.Threading.WaitOrTimerCallback,System.Object,System.Int64,System.Boolean)">
      <summary>Registers a delegate to wait for a <see cref="T:System.Threading.WaitHandle" />, specifying a 64-bit signed integer for the time-out in milliseconds.</summary>
      <param name="waitObject">The <see cref="T:System.Threading.WaitHandle" /> to register. Use a <see cref="T:System.Threading.WaitHandle" /> other than <see cref="T:System.Threading.Mutex" />.</param>
      <param name="callBack">The <see cref="T:System.Threading.WaitOrTimerCallback" /> delegate to call when the <paramref name="waitObject" /> parameter is signaled.</param>
      <param name="state">The object passed to the delegate.</param>
      <param name="millisecondsTimeOutInterval">The time-out in milliseconds. If the <paramref name="millisecondsTimeOutInterval" /> parameter is 0 (zero), the function tests the object's state and returns immediately. If <paramref name="millisecondsTimeOutInterval" /> is -1, the function's time-out interval never elapses.</param>
      <param name="executeOnlyOnce">
        <see langword="true" /> to indicate that the thread will no longer wait on the <paramref name="waitObject" /> parameter after the delegate has been called; <see langword="false" /> to indicate that the timer is reset every time the wait operation completes until the wait is unregistered.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsTimeOutInterval" /> parameter is less than -1.</exception>
      <returns>The <see cref="T:System.Threading.RegisteredWaitHandle" /> that encapsulates the native handle.</returns>
    </member>
    <member name="M:System.Threading.ThreadPool.RegisterWaitForSingleObject(System.Threading.WaitHandle,System.Threading.WaitOrTimerCallback,System.Object,System.TimeSpan,System.Boolean)">
      <summary>Registers a delegate to wait for a <see cref="T:System.Threading.WaitHandle" />, specifying a <see cref="T:System.TimeSpan" /> value for the time-out.</summary>
      <param name="waitObject">The <see cref="T:System.Threading.WaitHandle" /> to register. Use a <see cref="T:System.Threading.WaitHandle" /> other than <see cref="T:System.Threading.Mutex" />.</param>
      <param name="callBack">The <see cref="T:System.Threading.WaitOrTimerCallback" /> delegate to call when the <paramref name="waitObject" /> parameter is signaled.</param>
      <param name="state">The object passed to the delegate.</param>
      <param name="timeout">The time-out represented by a <see cref="T:System.TimeSpan" />. If <paramref name="timeout" /> is 0 (zero), the function tests the object's state and returns immediately. If <paramref name="timeout" /> is -1, the function's time-out interval never elapses.</param>
      <param name="executeOnlyOnce">
        <see langword="true" /> to indicate that the thread will no longer wait on the <paramref name="waitObject" /> parameter after the delegate has been called; <see langword="false" /> to indicate that the timer is reset every time the wait operation completes until the wait is unregistered.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="timeout" /> parameter is less than -1.</exception>
      <exception cref="T:System.NotSupportedException">The <paramref name="timeout" /> parameter is greater than <see cref="F:System.Int32.MaxValue" />.</exception>
      <returns>The <see cref="T:System.Threading.RegisteredWaitHandle" /> that encapsulates the native handle.</returns>
    </member>
    <member name="M:System.Threading.ThreadPool.RegisterWaitForSingleObject(System.Threading.WaitHandle,System.Threading.WaitOrTimerCallback,System.Object,System.UInt32,System.Boolean)">
      <summary>Registers a delegate to wait for a <see cref="T:System.Threading.WaitHandle" />, specifying a 32-bit unsigned integer for the time-out in milliseconds.</summary>
      <param name="waitObject">The <see cref="T:System.Threading.WaitHandle" /> to register. Use a <see cref="T:System.Threading.WaitHandle" /> other than <see cref="T:System.Threading.Mutex" />.</param>
      <param name="callBack">The <see cref="T:System.Threading.WaitOrTimerCallback" /> delegate to call when the <paramref name="waitObject" /> parameter is signaled.</param>
      <param name="state">The object passed to the delegate.</param>
      <param name="millisecondsTimeOutInterval">The time-out in milliseconds. If the <paramref name="millisecondsTimeOutInterval" /> parameter is 0 (zero), the function tests the object's state and returns immediately. If <paramref name="millisecondsTimeOutInterval" /> is -1, the function's time-out interval never elapses.</param>
      <param name="executeOnlyOnce">
        <see langword="true" /> to indicate that the thread will no longer wait on the <paramref name="waitObject" /> parameter after the delegate has been called; <see langword="false" /> to indicate that the timer is reset every time the wait operation completes until the wait is unregistered.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsTimeOutInterval" /> parameter is less than -1.</exception>
      <returns>The <see cref="T:System.Threading.RegisteredWaitHandle" /> that can be used to cancel the registered wait operation.</returns>
    </member>
    <member name="M:System.Threading.ThreadPool.SetMaxThreads(System.Int32,System.Int32)">
      <summary>Sets the number of requests to the thread pool that can be active concurrently. All requests above that number remain queued until thread pool threads become available.</summary>
      <param name="workerThreads">The maximum number of worker threads in the thread pool.</param>
      <param name="completionPortThreads">The maximum number of asynchronous I/O threads in the thread pool.</param>
      <returns>
        <see langword="true" /> if the change is successful; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Threading.ThreadPool.SetMinThreads(System.Int32,System.Int32)">
      <summary>Sets the minimum number of threads the thread pool creates on demand, as new requests are made, before switching to an algorithm for managing thread creation and destruction.</summary>
      <param name="workerThreads">The minimum number of worker threads that the thread pool creates on demand.</param>
      <param name="completionPortThreads">The minimum number of asynchronous I/O threads that the thread pool creates on demand.</param>
      <returns>
        <see langword="true" /> if the change is successful; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Threading.ThreadPool.UnsafeQueueNativeOverlapped(System.Threading.NativeOverlapped*)">
      <summary>Queues an overlapped I/O operation for execution.</summary>
      <param name="overlapped">The <see cref="T:System.Threading.NativeOverlapped" /> structure to queue.</param>
      <returns>
        <see langword="true" /> if the operation was successfully queued to an I/O completion port; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Threading.ThreadPool.UnsafeQueueUserWorkItem(System.Threading.IThreadPoolWorkItem,System.Boolean)">
      <summary>Queues the specified work item object to the thread pool.</summary>
      <param name="callBack">The work item to invoke when a thread in the thread pool picks up the work item.</param>
      <param name="preferLocal">
        <see langword="true" /> to prefer queueing the work item in a queue close to the current thread; <see langword="false" /> to prefer queueing the work item to the thread pool's shared queue.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The work item is a <see cref="T:System.Threading.Tasks.Task" />.</exception>
      <exception cref="T:System.OutOfMemoryException">The work item could not be queued.</exception>
      <returns>
        <see langword="true" /> if the method succeeds; <see cref="T:System.OutOfMemoryException" /> is thrown if the work item could not be queued.</returns>
    </member>
    <member name="M:System.Threading.ThreadPool.UnsafeQueueUserWorkItem(System.Threading.WaitCallback,System.Object)">
      <summary>Queues the specified delegate to the thread pool, but does not propagate the calling stack to the worker thread.</summary>
      <param name="callBack">A <see cref="T:System.Threading.WaitCallback" /> that represents the delegate to invoke when a thread in the thread pool picks up the work item.</param>
      <param name="state">The object that is passed to the delegate when serviced from the thread pool.</param>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ApplicationException">An out-of-memory condition was encountered.</exception>
      <exception cref="T:System.OutOfMemoryException">The work item could not be queued.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callBack" /> is <see langword="null" />.</exception>
      <returns>
        <see langword="true" /> if the method succeeds; <see cref="T:System.OutOfMemoryException" /> is thrown if the work item could not be queued.</returns>
    </member>
    <member name="M:System.Threading.ThreadPool.UnsafeQueueUserWorkItem``1(System.Action{``0},``0,System.Boolean)">
      <summary>Queues a method specified by an <see cref="T:System.Action`1" /> delegate for execution, and specifies an object containing data to be used by the method. The method executes when a thread pool thread becomes available.</summary>
      <param name="callBack">A delegate representing the method to execute.</param>
      <param name="state">An object containing data to be used by the method.</param>
      <param name="preferLocal">
        <see langword="true" /> to prefer queueing the work item in a queue close to the current thread; <see langword="false" /> to prefer queueing the work item to the thread pool's shared queue.</param>
      <typeparam name="TState">The type of elements of <paramref name="state" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">The work item could not be queued.</exception>
      <returns>
        <see langword="true" /> if the method is successfully queued; <see cref="T:System.NotSupportedException" /> is thrown if the work item could not be queued.</returns>
    </member>
    <member name="M:System.Threading.ThreadPool.UnsafeRegisterWaitForSingleObject(System.Threading.WaitHandle,System.Threading.WaitOrTimerCallback,System.Object,System.Int32,System.Boolean)">
      <summary>Registers a delegate to wait for a <see cref="T:System.Threading.WaitHandle" />, using a 32-bit signed integer for the time-out in milliseconds. This method does not propagate the calling stack to the worker thread.</summary>
      <param name="waitObject">The <see cref="T:System.Threading.WaitHandle" /> to register. Use a <see cref="T:System.Threading.WaitHandle" /> other than <see cref="T:System.Threading.Mutex" />.</param>
      <param name="callBack">The delegate to call when the <paramref name="waitObject" /> parameter is signaled.</param>
      <param name="state">The object that is passed to the delegate.</param>
      <param name="millisecondsTimeOutInterval">The time-out in milliseconds. If the <paramref name="millisecondsTimeOutInterval" /> parameter is 0 (zero), the function tests the object's state and returns immediately. If <paramref name="millisecondsTimeOutInterval" /> is -1, the function's time-out interval never elapses.</param>
      <param name="executeOnlyOnce">
        <see langword="true" /> to indicate that the thread will no longer wait on the <paramref name="waitObject" /> parameter after the delegate has been called; <see langword="false" /> to indicate that the timer is reset every time the wait operation completes until the wait is unregistered.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsTimeOutInterval" /> parameter is less than -1.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <returns>The <see cref="T:System.Threading.RegisteredWaitHandle" /> object that can be used to cancel the registered wait operation.</returns>
    </member>
    <member name="M:System.Threading.ThreadPool.UnsafeRegisterWaitForSingleObject(System.Threading.WaitHandle,System.Threading.WaitOrTimerCallback,System.Object,System.Int64,System.Boolean)">
      <summary>Registers a delegate to wait for a <see cref="T:System.Threading.WaitHandle" />, specifying a 64-bit signed integer for the time-out in milliseconds. This method does not propagate the calling stack to the worker thread.</summary>
      <param name="waitObject">The <see cref="T:System.Threading.WaitHandle" /> to register. Use a <see cref="T:System.Threading.WaitHandle" /> other than <see cref="T:System.Threading.Mutex" />.</param>
      <param name="callBack">The delegate to call when the <paramref name="waitObject" /> parameter is signaled.</param>
      <param name="state">The object that is passed to the delegate.</param>
      <param name="millisecondsTimeOutInterval">The time-out in milliseconds. If the <paramref name="millisecondsTimeOutInterval" /> parameter is 0 (zero), the function tests the object's state and returns immediately. If <paramref name="millisecondsTimeOutInterval" /> is -1, the function's time-out interval never elapses.</param>
      <param name="executeOnlyOnce">
        <see langword="true" /> to indicate that the thread will no longer wait on the <paramref name="waitObject" /> parameter after the delegate has been called; <see langword="false" /> to indicate that the timer is reset every time the wait operation completes until the wait is unregistered.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="millisecondsTimeOutInterval" /> parameter is less than -1.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <returns>The <see cref="T:System.Threading.RegisteredWaitHandle" /> object that can be used to cancel the registered wait operation.</returns>
    </member>
    <member name="M:System.Threading.ThreadPool.UnsafeRegisterWaitForSingleObject(System.Threading.WaitHandle,System.Threading.WaitOrTimerCallback,System.Object,System.TimeSpan,System.Boolean)">
      <summary>Registers a delegate to wait for a <see cref="T:System.Threading.WaitHandle" />, specifying a <see cref="T:System.TimeSpan" /> value for the time-out. This method does not propagate the calling stack to the worker thread.</summary>
      <param name="waitObject">The <see cref="T:System.Threading.WaitHandle" /> to register. Use a <see cref="T:System.Threading.WaitHandle" /> other than <see cref="T:System.Threading.Mutex" />.</param>
      <param name="callBack">The delegate to call when the <paramref name="waitObject" /> parameter is signaled.</param>
      <param name="state">The object that is passed to the delegate.</param>
      <param name="timeout">The time-out represented by a <see cref="T:System.TimeSpan" />. If <paramref name="timeout" /> is 0 (zero), the function tests the object's state and returns immediately. If <paramref name="timeout" /> is -1, the function's time-out interval never elapses.</param>
      <param name="executeOnlyOnce">
        <see langword="true" /> to indicate that the thread will no longer wait on the <paramref name="waitObject" /> parameter after the delegate has been called; <see langword="false" /> to indicate that the timer is reset every time the wait operation completes until the wait is unregistered.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="timeout" /> parameter is less than -1.</exception>
      <exception cref="T:System.NotSupportedException">The <paramref name="timeout" /> parameter is greater than <see cref="F:System.Int32.MaxValue" />.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <returns>The <see cref="T:System.Threading.RegisteredWaitHandle" /> object that can be used to cancel the registered wait operation.</returns>
    </member>
    <member name="M:System.Threading.ThreadPool.UnsafeRegisterWaitForSingleObject(System.Threading.WaitHandle,System.Threading.WaitOrTimerCallback,System.Object,System.UInt32,System.Boolean)">
      <summary>Registers a delegate to wait for a <see cref="T:System.Threading.WaitHandle" />, specifying a 32-bit unsigned integer for the time-out in milliseconds. This method does not propagate the calling stack to the worker thread.</summary>
      <param name="waitObject">The <see cref="T:System.Threading.WaitHandle" /> to register. Use a <see cref="T:System.Threading.WaitHandle" /> other than <see cref="T:System.Threading.Mutex" />.</param>
      <param name="callBack">The delegate to call when the <paramref name="waitObject" /> parameter is signaled.</param>
      <param name="state">The object that is passed to the delegate.</param>
      <param name="millisecondsTimeOutInterval">The time-out in milliseconds. If the <paramref name="millisecondsTimeOutInterval" /> parameter is 0 (zero), the function tests the object's state and returns immediately. If <paramref name="millisecondsTimeOutInterval" /> is -1, the function's time-out interval never elapses.</param>
      <param name="executeOnlyOnce">
        <see langword="true" /> to indicate that the thread will no longer wait on the <paramref name="waitObject" /> parameter after the delegate has been called; <see langword="false" /> to indicate that the timer is reset every time the wait operation completes until the wait is unregistered.</param>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <returns>The <see cref="T:System.Threading.RegisteredWaitHandle" /> object that can be used to cancel the registered wait operation.</returns>
    </member>
    <member name="P:System.Threading.ThreadPool.CompletedWorkItemCount">
      <summary>Gets the number of work items that have been processed so far.</summary>
      <returns>The number of work items that have been processed so far.</returns>
    </member>
    <member name="P:System.Threading.ThreadPool.PendingWorkItemCount">
      <summary>Gets the number of work items that are currently queued to be processed.</summary>
      <returns>The number of work items that are currently queued to be processed.</returns>
    </member>
    <member name="P:System.Threading.ThreadPool.ThreadCount">
      <summary>Gets the number of thread pool threads that currently exist.</summary>
      <returns>The number of thread pool threads that currently exist.</returns>
    </member>
    <member name="T:System.Threading.WaitCallback">
      <summary>Represents a callback method to be executed by a thread pool thread.</summary>
      <param name="state">An object containing information to be used by the callback method.</param>
    </member>
    <member name="T:System.Threading.WaitOrTimerCallback">
      <summary>Represents a method to be called when a <see cref="T:System.Threading.WaitHandle" /> is signaled or times out.</summary>
      <param name="state">An object containing information to be used by the callback method each time it executes.</param>
      <param name="timedOut">
        <see langword="true" /> if the <see cref="T:System.Threading.WaitHandle" /> timed out; <see langword="false" /> if it was signaled.</param>
    </member>
  </members>
</doc>