using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace FlightPig.Models
{
    /// <summary>
    /// Represents a landing challenge with scoring criteria
    /// </summary>
    public class LandingChallenge
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("title")]
        public string Title { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; }

        [JsonPropertyName("airport")]
        public Airport Airport { get; set; }

        [JsonPropertyName("targetRunway")]
        public Runway TargetRunway { get; set; }

        [JsonPropertyName("aircraftType")]
        public string AircraftType { get; set; }

        [JsonPropertyName("difficulty")]
        public LandingDifficulty Difficulty { get; set; }

        [JsonPropertyName("weatherConditions")]
        public string WeatherConditions { get; set; }

        [JsonPropertyName("approachType")]
        public ApproachType ApproachType { get; set; }

        [JsonPropertyName("isActive")]
        public bool IsActive { get; set; }

        [JsonPropertyName("startTime")]
        public DateTime? StartTime { get; set; }

        [JsonPropertyName("endTime")]
        public DateTime? EndTime { get; set; }

        [JsonPropertyName("score")]
        public LandingScore Score { get; set; }

        [JsonPropertyName("objectives")]
        public List<Objective> Objectives { get; set; } = new List<Objective>();

        /// <summary>
        /// Create objectives for the landing challenge
        /// </summary>
        public void CreateObjectives()
        {
            Objectives.Clear();

            // Approach objective
            var approachObjective = new Objective
            {
                Title = $"Approach {TargetRunway.Designation}",
                Description = $"Execute a proper approach to runway {TargetRunway.Designation} at {Airport.Name}",
                Type = ObjectiveType.LandAt,
                Latitude = TargetRunway.ThresholdLatitude ?? Airport.Latitude,
                Longitude = TargetRunway.ThresholdLongitude ?? Airport.Longitude,
                Altitude = TargetRunway.ThresholdElevation ?? Airport.ElevationFeet,
                Parameters = new Dictionary<string, object>
                {
                    ["isLandingChallenge"] = true,
                    ["runwayHeading"] = TargetRunway.Heading,
                    ["runwayLength"] = TargetRunway.LengthFeet,
                    ["approachType"] = ApproachType.ToString(),
                    ["proximityThresholdNm"] = 0.1 // Very precise for landing
                }
            };

            Objectives.Add(approachObjective);
        }

        /// <summary>
        /// Convert to a mission for the objective system
        /// </summary>
        public Mission ToMission()
        {
            CreateObjectives();

            return new Mission
            {
                Title = Title,
                Description = Description,
                Difficulty = Difficulty.ToString(),
                Region = Airport.Region,
                StartTime = DateTime.Now,
                EstimatedDuration = TimeSpan.FromMinutes(15),
                Objectives = Objectives
            };
        }
    }

    /// <summary>
    /// Comprehensive landing performance score
    /// </summary>
    public class LandingScore
    {
        [JsonPropertyName("overallScore")]
        public double OverallScore { get; set; }

        [JsonPropertyName("approachScore")]
        public double ApproachScore { get; set; }

        [JsonPropertyName("touchdownScore")]
        public double TouchdownScore { get; set; }

        [JsonPropertyName("rolloutScore")]
        public double RolloutScore { get; set; }

        [JsonPropertyName("stabilityScore")]
        public double StabilityScore { get; set; }

        [JsonPropertyName("precisionScore")]
        public double PrecisionScore { get; set; }

        [JsonPropertyName("grade")]
        public string Grade { get; set; }

        [JsonPropertyName("analysis")]
        public LandingAnalysis Analysis { get; set; } = new LandingAnalysis();

        [JsonPropertyName("achievements")]
        public List<string> Achievements { get; set; } = new List<string>();

        [JsonPropertyName("improvements")]
        public List<string> Improvements { get; set; } = new List<string>();

        /// <summary>
        /// Calculate overall score from component scores
        /// </summary>
        public void CalculateOverallScore()
        {
            OverallScore = (ApproachScore * 0.3 + TouchdownScore * 0.4 + 
                           RolloutScore * 0.2 + StabilityScore * 0.1);

            if (OverallScore >= 95) Grade = "A+";
            else if (OverallScore >= 90) Grade = "A";
            else if (OverallScore >= 85) Grade = "A-";
            else if (OverallScore >= 80) Grade = "B+";
            else if (OverallScore >= 75) Grade = "B";
            else if (OverallScore >= 70) Grade = "B-";
            else if (OverallScore >= 65) Grade = "C+";
            else if (OverallScore >= 60) Grade = "C";
            else if (OverallScore >= 55) Grade = "C-";
            else if (OverallScore >= 50) Grade = "D";
            else Grade = "F";
        }
    }

    /// <summary>
    /// Detailed analysis of landing performance
    /// </summary>
    public class LandingAnalysis
    {
        [JsonPropertyName("touchdownDistance")]
        public double TouchdownDistanceFeet { get; set; }

        [JsonPropertyName("touchdownSpeed")]
        public double TouchdownSpeedKnots { get; set; }

        [JsonPropertyName("verticalSpeed")]
        public double VerticalSpeedFpm { get; set; }

        [JsonPropertyName("lateralDeviation")]
        public double LateralDeviationFeet { get; set; }

        [JsonPropertyName("rolloutDistance")]
        public double RolloutDistanceFeet { get; set; }

        [JsonPropertyName("maxGForce")]
        public double MaxGForce { get; set; }

        [JsonPropertyName("approachStability")]
        public bool ApproachStability { get; set; }

        [JsonPropertyName("centerlineTracking")]
        public bool CenterlineTracking { get; set; }

        [JsonPropertyName("smoothTouchdown")]
        public bool SmoothTouchdown { get; set; }

        [JsonPropertyName("properFlare")]
        public bool ProperFlare { get; set; }

        [JsonPropertyName("crashed")]
        public bool Crashed { get; set; }

        [JsonPropertyName("hardLanding")]
        public bool HardLanding { get; set; }

        [JsonPropertyName("longLanding")]
        public bool LongLanding { get; set; }

        [JsonPropertyName("offRunway")]
        public bool OffRunway { get; set; }
    }

    /// <summary>
    /// Types of approaches for landing challenges
    /// </summary>
    public enum ApproachType
    {
        Visual,
        ILS,
        VOR,
        GPS,
        Circling,
        StraightIn
    }
}
