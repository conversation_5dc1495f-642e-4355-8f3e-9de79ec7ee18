﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata minClientVersion="2.12">
    <id>System.Xml.XDocument</id>
    <version>4.0.11</version>
    <title>System.Xml.XDocument</title>
    <authors>Microsoft</authors>
    <owners>microsoft,dotnetframework</owners>
    <requireLicenseAcceptance>true</requireLicenseAcceptance>
    <licenseUrl>http://go.microsoft.com/fwlink/?LinkId=329770</licenseUrl>
    <projectUrl>https://dot.net/</projectUrl>
    <iconUrl>http://go.microsoft.com/fwlink/?LinkID=288859</iconUrl>
    <description>Provides the classes for Language-Integrated Query (LINQ) to Extensible Markup Language (XML). LINQ to XML is an in-memory XML programming interface that enables you to modify XML documents efficiently and easily.

Commonly Used Types:
System.Xml.Linq.XElement
System.Xml.Linq.XAttribute
System.Xml.Linq.XDocument
System.Xml.Linq.XText
System.Xml.Linq.XNode
System.Xml.Linq.XContainer
System.Xml.Linq.XComment
System.Xml.Linq.XObject
System.Xml.Linq.XProcessingInstruction
System.Xml.Linq.XDocumentType
 
When using NuGet 3.x this package requires at least version 3.4.</description>
    <releaseNotes>https://go.microsoft.com/fwlink/?LinkID=799417</releaseNotes>
    <copyright>© Microsoft Corporation.  All rights reserved.</copyright>
    <serviceable>true</serviceable>
    <dependencies>
      <group targetFramework="MonoAndroid1.0" />
      <group targetFramework="MonoTouch1.0" />
      <group targetFramework=".NETFramework4.5" />
      <group targetFramework=".NETCore5.0">
        <dependency id="System.Collections" version="4.0.11" exclude="Compile" />
        <dependency id="System.Diagnostics.Debug" version="4.0.11" exclude="Compile" />
        <dependency id="System.Diagnostics.Tools" version="4.0.1" exclude="Compile" />
        <dependency id="System.Globalization" version="4.0.11" exclude="Compile" />
        <dependency id="System.IO" version="4.1.0" />
        <dependency id="System.Reflection" version="4.1.0" exclude="Compile" />
        <dependency id="System.Resources.ResourceManager" version="4.0.1" exclude="Compile" />
        <dependency id="System.Runtime" version="4.1.0" />
        <dependency id="System.Runtime.Extensions" version="4.1.0" exclude="Compile" />
        <dependency id="System.Text.Encoding" version="4.0.11" exclude="Compile" />
        <dependency id="System.Threading" version="4.0.11" exclude="Compile" />
        <dependency id="System.Xml.ReaderWriter" version="4.0.11" />
      </group>
      <group targetFramework=".NETStandard1.0">
        <dependency id="System.IO" version="4.1.0" />
        <dependency id="System.Runtime" version="4.1.0" />
        <dependency id="System.Xml.ReaderWriter" version="4.0.11" />
      </group>
      <group targetFramework=".NETStandard1.3">
        <dependency id="System.Collections" version="4.0.11" exclude="Compile" />
        <dependency id="System.Diagnostics.Debug" version="4.0.11" exclude="Compile" />
        <dependency id="System.Diagnostics.Tools" version="4.0.1" exclude="Compile" />
        <dependency id="System.Globalization" version="4.0.11" exclude="Compile" />
        <dependency id="System.IO" version="4.1.0" />
        <dependency id="System.Reflection" version="4.1.0" exclude="Compile" />
        <dependency id="System.Resources.ResourceManager" version="4.0.1" exclude="Compile" />
        <dependency id="System.Runtime" version="4.1.0" />
        <dependency id="System.Runtime.Extensions" version="4.1.0" exclude="Compile" />
        <dependency id="System.Text.Encoding" version="4.0.11" exclude="Compile" />
        <dependency id="System.Threading" version="4.0.11" exclude="Compile" />
        <dependency id="System.Xml.ReaderWriter" version="4.0.11" />
      </group>
      <group targetFramework=".NETPortable0.0-Profile259" />
      <group targetFramework="Windows8.0" />
      <group targetFramework="WindowsPhone8.0" />
      <group targetFramework="WindowsPhoneApp8.1" />
      <group targetFramework="Xamarin.iOS1.0" />
      <group targetFramework="Xamarin.Mac2.0" />
      <group targetFramework="Xamarin.TVOS1.0" />
      <group targetFramework="Xamarin.WatchOS1.0" />
    </dependencies>
    <frameworkAssemblies>
      <frameworkAssembly assemblyName="System.Xml.Linq" targetFramework=".NETFramework4.5" />
    </frameworkAssemblies>
  </metadata>
</package>