# PowerShell script to download Whisper tiny model
Write-Host "FlightPig - Whisper Model Downloader" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green
Write-Host ""

$modelUrl = "https://huggingface.co/ggerganov/whisper.cpp/resolve/main/ggml-tiny.bin"
$modelPath = "ggml-tiny.bin"

# Check if model already exists
if (Test-Path $modelPath) {
    Write-Host "Whisper model already exists at: $modelPath" -ForegroundColor Yellow
    $size = (Get-Item $modelPath).Length / 1MB
    Write-Host "Model size: $([math]::Round($size, 1)) MB" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "If you want to re-download, delete the existing file first." -ForegroundColor Yellow
    exit 0
}

Write-Host "Downloading Whisper tiny model..." -ForegroundColor Cyan
Write-Host "URL: $modelUrl" -ForegroundColor Gray
Write-Host "Destination: $modelPath" -ForegroundColor Gray
Write-Host ""
Write-Host "This will download approximately 39 MB and may take a few minutes..." -ForegroundColor Yellow
Write-Host ""

try {
    # Download with progress
    $webClient = New-Object System.Net.WebClient
    
    # Register progress event
    Register-ObjectEvent -InputObject $webClient -EventName DownloadProgressChanged -Action {
        $percent = $Event.SourceEventArgs.ProgressPercentage
        $received = $Event.SourceEventArgs.BytesReceived / 1MB
        $total = $Event.SourceEventArgs.TotalBytesToReceive / 1MB
        Write-Progress -Activity "Downloading Whisper Model" -Status "$([math]::Round($received, 1)) MB of $([math]::Round($total, 1)) MB" -PercentComplete $percent
    } | Out-Null
    
    # Download the file
    $webClient.DownloadFile($modelUrl, $modelPath)
    
    # Cleanup
    $webClient.Dispose()
    Write-Progress -Activity "Downloading Whisper Model" -Completed
    
    # Verify download
    if (Test-Path $modelPath) {
        $size = (Get-Item $modelPath).Length / 1MB
        Write-Host ""
        Write-Host "✓ Download completed successfully!" -ForegroundColor Green
        Write-Host "Model saved to: $modelPath" -ForegroundColor Green
        Write-Host "Model size: $([math]::Round($size, 1)) MB" -ForegroundColor Green
        Write-Host ""
        Write-Host "You can now run FlightPig with voice recognition enabled!" -ForegroundColor Green
    } else {
        throw "Downloaded file not found"
    }
}
catch {
    Write-Host ""
    Write-Host "❌ Download failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Manual download instructions:" -ForegroundColor Yellow
    Write-Host "1. Open your web browser" -ForegroundColor White
    Write-Host "2. Go to: $modelUrl" -ForegroundColor White
    Write-Host "3. Save the file as: $modelPath" -ForegroundColor White
    Write-Host "4. Place it in the FlightPig application directory" -ForegroundColor White
    Write-Host ""
    exit 1
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
