﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Threading.Overlapped</name>
  </assembly>
  <members>
    <member name="T:System.Threading.IOCompletionCallback">
      <summary>Receives the error code, number of bytes, and overlapped value type when an I/O operation completes on the thread pool.</summary>
      <param name="errorCode">The error code.</param>
      <param name="numBytes">The number of bytes that are transferred.</param>
      <param name="pOVERLAP">A <see cref="T:System.Threading.NativeOverlapped" /> representing an unmanaged pointer to the native overlapped value type.</param>
    </member>
    <member name="T:System.Threading.NativeOverlapped">
      <summary>Provides an explicit layout that is visible from unmanaged code and that will have the same layout as the Win32 OVERLAPPED structure with additional reserved fields at the end.</summary>
    </member>
    <member name="F:System.Threading.NativeOverlapped.EventHandle">
      <summary>Specifies the handle to an event set to the signaled state when the operation is complete. The calling process must set this member either to zero or to a valid event handle before calling any overlapped functions.</summary>
    </member>
    <member name="F:System.Threading.NativeOverlapped.InternalHigh">
      <summary>Specifies the length of the data transferred. Reserved for operating system use.</summary>
    </member>
    <member name="F:System.Threading.NativeOverlapped.InternalLow">
      <summary>Specifies a system-dependent status. Reserved for operating system use.</summary>
    </member>
    <member name="F:System.Threading.NativeOverlapped.OffsetHigh">
      <summary>Specifies the high word of the byte offset at which to start the transfer.</summary>
    </member>
    <member name="F:System.Threading.NativeOverlapped.OffsetLow">
      <summary>Specifies a file position at which to start the transfer.</summary>
    </member>
    <member name="T:System.Threading.Overlapped">
      <summary>Provides a managed representation of a Win32 <c>OVERLAPPED</c> structure, including methods to transfer information from an <see cref="T:System.Threading.Overlapped" /> instance to a <see cref="T:System.Threading.NativeOverlapped" /> structure.</summary>
    </member>
    <member name="M:System.Threading.Overlapped.#ctor">
      <summary>Initializes a new, empty instance of the <see cref="T:System.Threading.Overlapped" /> class.</summary>
    </member>
    <member name="M:System.Threading.Overlapped.#ctor(System.Int32,System.Int32,System.Int32,System.IAsyncResult)">
      <summary>Initializes a new instance of the <see cref="T:System.Threading.Overlapped" /> class with the specified file position, the 32-bit integer handle to an event that is signaled when the I/O operation is complete, and an interface through which to return the results of the operation.</summary>
      <param name="offsetLo">The low word of the file position at which to start the transfer.</param>
      <param name="offsetHi">The high word of the file position at which to start the transfer.</param>
      <param name="hEvent">The handle to an event that is signaled when the I/O operation is complete.</param>
      <param name="ar">An object that implements the <see cref="T:System.IAsyncResult" /> interface and provides status information on the I/O operation.</param>
    </member>
    <member name="M:System.Threading.Overlapped.#ctor(System.Int32,System.Int32,System.IntPtr,System.IAsyncResult)">
      <summary>Initializes a new instance of the <see cref="T:System.Threading.Overlapped" /> class with the specified file position, the handle to an event that is signaled when the I/O operation is complete, and an interface through which to return the results of the operation.</summary>
      <param name="offsetLo">The low word of the file position at which to start the transfer.</param>
      <param name="offsetHi">The high word of the file position at which to start the transfer.</param>
      <param name="hEvent">The handle to an event that is signaled when the I/O operation is complete.</param>
      <param name="ar">An object that implements the <see cref="T:System.IAsyncResult" /> interface and provides status information on the I/O operation.</param>
    </member>
    <member name="M:System.Threading.Overlapped.Free(System.Threading.NativeOverlapped*)">
      <summary>Frees the unmanaged memory associated with a native overlapped structure allocated by the <see cref="Overload:System.Threading.Overlapped.Pack" /> method.</summary>
      <param name="nativeOverlappedPtr">A pointer to the <see cref="T:System.Threading.NativeOverlapped" /> structure to be freed.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="nativeOverlappedPtr" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Threading.Overlapped.Pack(System.Threading.IOCompletionCallback)">
      <summary>Packs the current instance into a <see cref="T:System.Threading.NativeOverlapped" /> structure, specifying the delegate to be invoked when the asynchronous I/O operation is complete.</summary>
      <param name="iocb">An <see cref="T:System.Threading.IOCompletionCallback" /> delegate that represents the callback method invoked when the asynchronous I/O operation completes.</param>
      <exception cref="T:System.InvalidOperationException">The current <see cref="T:System.Threading.Overlapped" /> has already been packed.</exception>
      <returns>An unmanaged pointer to a <see cref="T:System.Threading.NativeOverlapped" /> structure.</returns>
    </member>
    <member name="M:System.Threading.Overlapped.Pack(System.Threading.IOCompletionCallback,System.Object)">
      <summary>Packs the current instance into a <see cref="T:System.Threading.NativeOverlapped" /> structure, specifying a delegate that is invoked when the asynchronous I/O operation is complete and a managed object that serves as a buffer.</summary>
      <param name="iocb">An <see cref="T:System.Threading.IOCompletionCallback" /> delegate that represents the callback method invoked when the asynchronous I/O operation completes.</param>
      <param name="userData">An object or array of objects representing the input or output buffer for the operation. Each object represents a buffer, for example an array of bytes.</param>
      <exception cref="T:System.InvalidOperationException">The current <see cref="T:System.Threading.Overlapped" /> has already been packed.</exception>
      <returns>An unmanaged pointer to a <see cref="T:System.Threading.NativeOverlapped" /> structure.</returns>
    </member>
    <member name="M:System.Threading.Overlapped.Unpack(System.Threading.NativeOverlapped*)">
      <summary>Unpacks the specified unmanaged <see cref="T:System.Threading.NativeOverlapped" /> structure into a managed <see cref="T:System.Threading.Overlapped" /> object.</summary>
      <param name="nativeOverlappedPtr">An unmanaged pointer to a <see cref="T:System.Threading.NativeOverlapped" /> structure.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="nativeOverlappedPtr" /> is <see langword="null" />.</exception>
      <returns>An <see cref="T:System.Threading.Overlapped" /> object containing the information unpacked from the native structure.</returns>
    </member>
    <member name="M:System.Threading.Overlapped.UnsafePack(System.Threading.IOCompletionCallback)">
      <summary>Packs the current instance into a <see cref="T:System.Threading.NativeOverlapped" /> structure specifying the delegate to invoke when the asynchronous I/O operation is complete. Does not propagate the calling stack.</summary>
      <param name="iocb">An <see cref="T:System.Threading.IOCompletionCallback" /> delegate that represents the callback method invoked when the asynchronous I/O operation completes.</param>
      <exception cref="T:System.InvalidOperationException">The current <see cref="T:System.Threading.Overlapped" /> has already been packed.</exception>
      <returns>An unmanaged pointer to a <see cref="T:System.Threading.NativeOverlapped" /> structure.</returns>
    </member>
    <member name="M:System.Threading.Overlapped.UnsafePack(System.Threading.IOCompletionCallback,System.Object)">
      <summary>Packs the current instance into a <see cref="T:System.Threading.NativeOverlapped" /> structure, specifying the delegate to invoke when the asynchronous I/O operation is complete and the managed object that serves as a buffer. Does not propagate the calling stack.</summary>
      <param name="iocb">An <see cref="T:System.Threading.IOCompletionCallback" /> delegate that represents the callback method invoked when the asynchronous I/O operation completes.</param>
      <param name="userData">An object or array of objects representing the input or output buffer for the operation. Each object represents a buffer, for example an array of bytes.</param>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.InvalidOperationException">The current <see cref="T:System.Threading.Overlapped" /> is already packed.</exception>
      <returns>An unmanaged pointer to a <see cref="T:System.Threading.NativeOverlapped" /> structure.</returns>
    </member>
    <member name="P:System.Threading.Overlapped.AsyncResult">
      <summary>Gets or sets the object that provides status information on the I/O operation.</summary>
      <returns>An object that implements the <see cref="T:System.IAsyncResult" /> interface.</returns>
    </member>
    <member name="P:System.Threading.Overlapped.EventHandle">
      <summary>Gets or sets the 32-bit integer handle to a synchronization event that is signaled when the I/O operation is complete.</summary>
      <returns>An <see cref="T:System.Int32" /> value representing the handle of the synchronization event.</returns>
    </member>
    <member name="P:System.Threading.Overlapped.EventHandleIntPtr">
      <summary>Gets or sets the handle to the synchronization event that is signaled when the I/O operation is complete.</summary>
      <returns>An <see cref="T:System.IntPtr" /> representing the handle of the event.</returns>
    </member>
    <member name="P:System.Threading.Overlapped.OffsetHigh">
      <summary>Gets or sets the high-order word of the file position at which to start the transfer. The file position is a byte offset from the start of the file.</summary>
      <returns>An <see cref="T:System.Int32" /> value representing the high word of the file position.</returns>
    </member>
    <member name="P:System.Threading.Overlapped.OffsetLow">
      <summary>Gets or sets the low-order word of the file position at which to start the transfer. The file position is a byte offset from the start of the file.</summary>
      <returns>An <see cref="T:System.Int32" /> value representing the low word of the file position.</returns>
    </member>
    <member name="T:System.Threading.PreAllocatedOverlapped">
      <summary>Represents pre-allocated state for native overlapped I/O operations.</summary>
    </member>
    <member name="M:System.Threading.PreAllocatedOverlapped.#ctor(System.Threading.IOCompletionCallback,System.Object,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Threading.PreAllocatedOverlapped" /> class and specifies a delegate to invoke when each asynchronous I/O operation is complete, a user-provided object that provides context, and managed objects that serve as buffers.</summary>
      <param name="callback">A delegate that represents the callback method to invoke when each asynchronous I/O operation completes.</param>
      <param name="state">A user-supplied object that distinguishes the <see cref="T:System.Threading.NativeOverlapped" /> instance produced from this object from other <see cref="T:System.Threading.NativeOverlapped" /> instances. Its value can be <see langword="null" />.</param>
      <param name="pinData">An object or array of objects that represent the input or output buffer for the operations. Each object represents a buffer, such as an array of bytes. Its value can be <see langword="null" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">This method was called after the <see cref="T:System.Threading.ThreadPoolBoundHandle" /> was disposed.</exception>
    </member>
    <member name="M:System.Threading.PreAllocatedOverlapped.Dispose">
      <summary>Frees the resources associated with this <see cref="T:System.Threading.PreAllocatedOverlapped" /> instance.</summary>
    </member>
    <member name="M:System.Threading.PreAllocatedOverlapped.Finalize">
      <summary>Frees unmanaged resources before the current instance is reclaimed by garbage collection.</summary>
    </member>
    <member name="M:System.Threading.PreAllocatedOverlapped.UnsafeCreate(System.Threading.IOCompletionCallback,System.Object,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:System.Threading.PreAllocatedOverlapped" /> class, specifying a delegate that is invoked when each asynchronous I/O operation is complete, a user-provided object providing context, and managed objects that serve as buffers.</summary>
      <param name="callback">An <see cref="T:System.Threading.IOCompletionCallback" /> delegate that represents the callback method invoked when each asynchronous I/O operation completes.</param>
      <param name="state">A user-provided object that distinguishes <see cref="T:System.Threading.NativeOverlapped" /> instance produced from this object from other <see cref="T:System.Threading.NativeOverlapped" /> instances. Can be <see langword="null" />.</param>
      <param name="pinData">An object or array of objects representing the input or output buffer for the operations. Each object represents a buffer, for example an array of bytes.  Can be <see langword="null" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">This method was called after the <see cref="T:System.Threading.ThreadPoolBoundHandle" /> was disposed.</exception>
      <returns>The new <see cref="T:System.Threading.PreAllocatedOverlapped" /> instance.</returns>
    </member>
    <member name="T:System.Threading.ThreadPoolBoundHandle">
      <summary>Represents an I/O handle that is bound to the system thread pool and enables low-level components to receive notifications for asynchronous I/O operations.</summary>
    </member>
    <member name="M:System.Threading.ThreadPoolBoundHandle.AllocateNativeOverlapped(System.Threading.IOCompletionCallback,System.Object,System.Object)">
      <summary>Returns an unmanaged pointer to a <see cref="T:System.Threading.NativeOverlapped" /> structure, specifying a delegate that is invoked when the asynchronous I/O operation is complete, a user-provided object that supplies context, and managed objects that serve as buffers.</summary>
      <param name="callback">A delegate that represents the callback method to invoke when the asynchronous I/O operation completes.</param>
      <param name="state">A user-provided object that distinguishes this <see cref="T:System.Threading.NativeOverlapped" /> instance from other <see cref="T:System.Threading.NativeOverlapped" /> instances.</param>
      <param name="pinData">An object or array of objects that represent the input or output buffer for the operation, or <see langword="null" />. Each object represents a buffer, such an array of bytes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">This method was called after the <see cref="T:System.Threading.ThreadPoolBoundHandle" /> object was disposed.</exception>
      <returns>An unmanaged pointer to a <see cref="T:System.Threading.NativeOverlapped" /> structure.</returns>
    </member>
    <member name="M:System.Threading.ThreadPoolBoundHandle.AllocateNativeOverlapped(System.Threading.PreAllocatedOverlapped)">
      <summary>Returns an unmanaged pointer to a <see cref="T:System.Threading.NativeOverlapped" /> structure using the callback state and buffers associated with the specified <see cref="T:System.Threading.PreAllocatedOverlapped" /> object.</summary>
      <param name="preAllocated">An object from which to create the <see cref="T:System.Threading.NativeOverlapped" /> pointer.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="preAllocated" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="preAllocated" /> is currently in use for another I/O operation.</exception>
      <exception cref="T:System.ObjectDisposedException">This method was called after the <see cref="T:System.Threading.ThreadPoolBoundHandle" /> was disposed.  
  
 -or-  
  
 This method was called after <paramref name="preAllocated" /> was disposed.</exception>
      <returns>An unmanaged pointer to a <see cref="T:System.Threading.NativeOverlapped" /> structure.</returns>
    </member>
    <member name="M:System.Threading.ThreadPoolBoundHandle.BindHandle(System.Runtime.InteropServices.SafeHandle)">
      <summary>Returns a <see cref="T:System.Threading.ThreadPoolBoundHandle" /> for the specified handle, which is bound to the system thread pool.</summary>
      <param name="handle">An object that holds the operating system handle. The handle must have been opened for overlapped I/O in unmanaged code.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="handle" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="handle" /> has been disposed.  
  
 -or-  
  
 <paramref name="handle" /> does not refer to a valid I/O handle.  
  
 -or-  
  
 <paramref name="handle" /> refers to a handle that has not been opened for overlapped I/O.  
  
 -or-  
  
 <paramref name="handle" /> refers to a handle that has already been bound.</exception>
      <returns>A <see cref="T:System.Threading.ThreadPoolBoundHandle" /> for <paramref name="handle" />, which is bound to the system thread pool.</returns>
    </member>
    <member name="M:System.Threading.ThreadPoolBoundHandle.Dispose">
      <summary>Releases all unmanaged resources used by the <see cref="T:System.Threading.ThreadPoolBoundHandle" /> instance.</summary>
    </member>
    <member name="M:System.Threading.ThreadPoolBoundHandle.FreeNativeOverlapped(System.Threading.NativeOverlapped*)">
      <summary>Frees the memory associated with a <see cref="T:System.Threading.NativeOverlapped" /> structure allocated by the <see cref="Overload:System.Threading.ThreadPoolBoundHandle.AllocateNativeOverlapped" /> method.</summary>
      <param name="overlapped">An unmanaged pointer to the <see cref="T:System.Threading.NativeOverlapped" /> structure to be freed.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="overlapped" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">This method was called after the <see cref="T:System.Threading.ThreadPoolBoundHandle" /> object was disposed.</exception>
    </member>
    <member name="M:System.Threading.ThreadPoolBoundHandle.GetNativeOverlappedState(System.Threading.NativeOverlapped*)">
      <summary>Returns the user-provided object that was specified when the <see cref="T:System.Threading.NativeOverlapped" /> instance was allocated by calling the <see cref="M:System.Threading.ThreadPoolBoundHandle.AllocateNativeOverlapped(System.Threading.IOCompletionCallback,System.Object,System.Object)" /> method.</summary>
      <param name="overlapped">An unmanaged pointer to the <see cref="T:System.Threading.NativeOverlapped" /> structure from which to return the associated user-provided object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="overlapped" /> is <see langword="null" />.</exception>
      <returns>A user-provided object that distinguishes this <see cref="T:System.Threading.NativeOverlapped" /> instance from other <see cref="T:System.Threading.NativeOverlapped" /> instances, or <see langword="null" /> if one was not specified when the instance was allocated by calling the <see cref="Overload:System.Threading.ThreadPoolBoundHandle.AllocateNativeOverlapped" /> method.</returns>
    </member>
    <member name="M:System.Threading.ThreadPoolBoundHandle.UnsafeAllocateNativeOverlapped(System.Threading.IOCompletionCallback,System.Object,System.Object)">
      <summary>Returns an unmanaged pointer to a <see cref="T:System.Threading.NativeOverlapped" /> structure, specifying a delegate that is invoked when the asynchronous I/O operation is complete, a user-provided object providing context, and managed objects that serve as buffers.</summary>
      <param name="callback">An <see cref="T:System.Threading.IOCompletionCallback" /> delegate that represents the callback method invoked when the asynchronous I/O operation completes.</param>
      <param name="state">A user-provided object that distinguishes this <see cref="T:System.Threading.NativeOverlapped" /> from other <see cref="T:System.Threading.NativeOverlapped" /> instances. Can be <see langword="null" />.</param>
      <param name="pinData">An object or array of objects representing the input or output buffer for the operation. Each object represents a buffer, for example an array of bytes.  Can be <see langword="null" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="callback" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">This method was called after the <see cref="T:System.Threading.ThreadPoolBoundHandle" /> was disposed.</exception>
      <returns>An unmanaged pointer to a <see cref="T:System.Threading.NativeOverlapped" /> structure.</returns>
    </member>
    <member name="P:System.Threading.ThreadPoolBoundHandle.Handle">
      <summary>Gets the bound operating system handle.</summary>
      <returns>An object that holds the bound operating system handle.</returns>
    </member>
  </members>
</doc>