using System;

namespace FlightPig.Models
{
    /// <summary>
    /// Represents a single flight data point for analysis
    /// </summary>
    public class FlightDataPoint
    {
        public DateTime Timestamp { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public double Altitude { get; set; }
        public double GroundSpeed { get; set; }
        public double VerticalSpeed { get; set; }
        public double Heading { get; set; }
        public double FuelQuantity { get; set; }
        public bool OnGround { get; set; }
        public double IndicatedAirspeed { get; set; }
        public double TrueAirspeed { get; set; }
        public double WindSpeed { get; set; }
        public double WindDirection { get; set; }
        public double GForce { get; set; }
        public double BankAngle { get; set; }
        public double PitchAngle { get; set; }
    }
}
