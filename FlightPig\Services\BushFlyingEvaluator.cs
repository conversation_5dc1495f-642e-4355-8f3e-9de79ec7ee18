using System;
using System.Collections.Generic;
using System.Linq;
using FlightPig.Models;

namespace FlightPig.Services
{
    /// <summary>
    /// Specialized evaluator for bush flying skills and performance
    /// </summary>
    public class BushFlyingEvaluator
    {
        private readonly MissionProgressMonitor _progressMonitor;
        private BushFlyingChallenge _currentChallenge;
        private DateTime _legStartTime;
        private double _legStartFuel;
        private List<FlightDataPoint> _legFlightData = new List<FlightDataPoint>();

        public BushFlyingEvaluator(MissionProgressMonitor progressMonitor)
        {
            _progressMonitor = progressMonitor;
        }

        /// <summary>
        /// Start evaluating a bush flying challenge
        /// </summary>
        public void StartChallenge(BushFlyingChallenge challenge)
        {
            _currentChallenge = challenge;
            _currentChallenge.Status = BushFlyingStatus.InProgress;
            Console.WriteLine($"Started bush flying challenge: {challenge.Title}");
        }

        /// <summary>
        /// Start evaluating a specific leg of the bush flying route
        /// </summary>
        public void StartLeg(BushFlyingWaypoint waypoint, AircraftInfo aircraftInfo)
        {
            _legStartTime = DateTime.UtcNow;
            _legStartFuel = aircraftInfo.FuelQuantityGallons;
            _legFlightData.Clear();
            
            Console.WriteLine($"Starting leg to: {waypoint.Name}");
            Console.WriteLine($"Distance: {waypoint.DistanceFromPreviousNM:F1} NM");
            Console.WriteLine($"Challenges: {string.Join(", ", waypoint.LocalChallenges)}");
        }

        /// <summary>
        /// Update flight data during the leg
        /// </summary>
        public void UpdateFlightData(AircraftInfo aircraftInfo)
        {
            var dataPoint = new FlightDataPoint
            {
                Timestamp = DateTime.UtcNow,
                Latitude = aircraftInfo.Latitude,
                Longitude = aircraftInfo.Longitude,
                Altitude = aircraftInfo.Altitude,
                GroundSpeed = aircraftInfo.GroundSpeed,
                VerticalSpeed = aircraftInfo.VerticalSpeed,
                Heading = aircraftInfo.Heading,
                FuelQuantity = aircraftInfo.FuelQuantityGallons,
                OnGround = aircraftInfo.OnGround
            };

            _legFlightData.Add(dataPoint);

            // Keep only recent data to manage memory
            if (_legFlightData.Count > 1000)
            {
                _legFlightData.RemoveRange(0, 500);
            }
        }

        /// <summary>
        /// Complete evaluation of a leg when landing is detected
        /// </summary>
        public BushFlyingLegResult CompleteLeg(BushFlyingWaypoint waypoint, AircraftInfo aircraftInfo, LandingScore landingScore)
        {
            var legEndTime = DateTime.UtcNow;
            var legEndFuel = aircraftInfo.FuelQuantityGallons;
            var flightTimeMinutes = (legEndTime - _legStartTime).TotalMinutes;
            var fuelUsed = _legStartFuel - legEndFuel;

            // Evaluate bush flying specific skills
            var bushScore = EvaluateBushFlyingSkills(waypoint, flightTimeMinutes, fuelUsed);

            var legResult = new BushFlyingLegResult
            {
                WaypointId = waypoint.Id,
                CompletedAt = legEndTime,
                FlightTimeMinutes = flightTimeMinutes,
                FuelUsedGallons = fuelUsed,
                Score = bushScore,
                LandingCompleted = true,
                LandingScore = landingScore,
                ChallengesEncountered = EvaluateChallengesEncountered(waypoint),
                SkillsDemonstrated = EvaluateSkillsDemonstrated(waypoint, bushScore),
                Notes = GenerateLegNotes(waypoint, bushScore, flightTimeMinutes, fuelUsed)
            };

            // Add to challenge results
            _currentChallenge.CompletedLegs.Add(legResult);
            _currentChallenge.CurrentWaypointIndex++;

            Console.WriteLine($"Leg completed to {waypoint.Name}");
            Console.WriteLine($"Bush Flying Score: {bushScore.OverallScore:F1}% ({bushScore.Grade})");
            Console.WriteLine($"Flight Time: {flightTimeMinutes:F1} minutes");
            Console.WriteLine($"Fuel Used: {fuelUsed:F1} gallons");

            return legResult;
        }

        /// <summary>
        /// Evaluate bush flying specific skills for the leg
        /// </summary>
        private BushFlyingLegScore EvaluateBushFlyingSkills(BushFlyingWaypoint waypoint, double flightTimeMinutes, double fuelUsed)
        {
            var score = new BushFlyingLegScore();

            // Navigation Accuracy (0-100)
            score.NavigationAccuracy = EvaluateNavigationAccuracy(waypoint);

            // Terrain Awareness (0-100)
            score.TerrainAwareness = EvaluateTerrainAwareness(waypoint);

            // Fuel Management (0-100)
            score.FuelManagement = EvaluateFuelManagement(waypoint, fuelUsed, flightTimeMinutes);

            // Weather Adaptation (0-100)
            score.WeatherAdaptation = EvaluateWeatherAdaptation(waypoint);

            // Landing Technique (0-100) - will be updated with actual landing score
            score.LandingTechnique = 85; // Default, will be updated with real landing score

            // Safety Consciousness (0-100)
            score.SafetyConsciousness = EvaluateSafetyConsciousness(waypoint);

            // Calculate overall score with bush flying weights
            score.OverallScore = CalculateBushFlyingOverallScore(score);

            // Assign grade
            score.Grade = AssignGrade(score.OverallScore);

            // Generate feedback
            score.Strengths = GenerateStrengths(score);
            score.AreasForImprovement = GenerateAreasForImprovement(score);

            return score;
        }

        /// <summary>
        /// Evaluate navigation accuracy for bush flying
        /// </summary>
        private double EvaluateNavigationAccuracy(BushFlyingWaypoint waypoint)
        {
            if (!_legFlightData.Any()) return 50;

            // Check if pilot maintained reasonable course to waypoint
            var courseDeviations = new List<double>();
            var targetLat = waypoint.Latitude;
            var targetLon = waypoint.Longitude;

            foreach (var point in _legFlightData.Where(p => !p.OnGround))
            {
                var distanceToTarget = CalculateDistance(point.Latitude, point.Longitude, targetLat, targetLon);
                var bearingToTarget = CalculateBearing(point.Latitude, point.Longitude, targetLat, targetLon);
                var headingError = Math.Abs(point.Heading - bearingToTarget);
                if (headingError > 180) headingError = 360 - headingError;
                
                courseDeviations.Add(headingError);
            }

            if (!courseDeviations.Any()) return 50;

            var averageDeviation = courseDeviations.Average();
            
            // Score based on average course deviation
            if (averageDeviation < 5) return 95;
            if (averageDeviation < 10) return 85;
            if (averageDeviation < 20) return 75;
            if (averageDeviation < 30) return 65;
            if (averageDeviation < 45) return 55;
            return 45;
        }

        /// <summary>
        /// Evaluate terrain awareness
        /// </summary>
        private double EvaluateTerrainAwareness(BushFlyingWaypoint waypoint)
        {
            if (!_legFlightData.Any()) return 50;

            var score = 80; // Base score

            // Check for appropriate altitude management
            var altitudes = _legFlightData.Where(p => !p.OnGround).Select(p => p.Altitude).ToList();
            if (altitudes.Any())
            {
                var minAltitude = altitudes.Min();
                var maxAltitude = altitudes.Max();
                var altitudeVariation = maxAltitude - minAltitude;

                // Reward consistent altitude management
                if (altitudeVariation < 500) score += 10;
                else if (altitudeVariation > 2000) score -= 10;

                // Check for appropriate terrain clearance
                var terrainClearance = minAltitude - waypoint.ElevationFeet;
                if (terrainClearance > 1000) score += 5;
                else if (terrainClearance < 500) score -= 15;
            }

            return Math.Max(0, Math.Min(100, score));
        }

        /// <summary>
        /// Evaluate fuel management skills
        /// </summary>
        private double EvaluateFuelManagement(BushFlyingWaypoint waypoint, double fuelUsed, double flightTimeMinutes)
        {
            if (flightTimeMinutes <= 0) return 50;

            // Calculate fuel consumption rate (gallons per hour)
            var fuelRate = (fuelUsed / flightTimeMinutes) * 60;

            // Expected fuel consumption for bush flying (conservative estimate)
            var expectedRate = 12; // GPH for typical bush aircraft

            var efficiency = expectedRate / Math.Max(fuelRate, 1);

            // Score based on fuel efficiency
            if (efficiency > 1.2) return 95; // Very efficient
            if (efficiency > 1.0) return 85; // Efficient
            if (efficiency > 0.8) return 75; // Acceptable
            if (efficiency > 0.6) return 65; // High consumption
            return 50; // Excessive consumption
        }

        /// <summary>
        /// Evaluate weather adaptation
        /// </summary>
        private double EvaluateWeatherAdaptation(BushFlyingWaypoint waypoint)
        {
            var score = 80; // Base score

            // Check for smooth flight (low turbulence handling)
            if (_legFlightData.Any())
            {
                var verticalSpeeds = _legFlightData.Where(p => !p.OnGround).Select(p => Math.Abs(p.VerticalSpeed)).ToList();
                if (verticalSpeeds.Any())
                {
                    var avgVerticalSpeed = verticalSpeeds.Average();
                    if (avgVerticalSpeed < 200) score += 10; // Smooth flight
                    else if (avgVerticalSpeed > 500) score -= 10; // Rough flight
                }
            }

            return Math.Max(0, Math.Min(100, score));
        }

        /// <summary>
        /// Evaluate safety consciousness
        /// </summary>
        private double EvaluateSafetyConsciousness(BushFlyingWaypoint waypoint)
        {
            var score = 85; // Base score for bush flying

            // Check for appropriate speeds
            if (_legFlightData.Any())
            {
                var speeds = _legFlightData.Where(p => !p.OnGround).Select(p => p.GroundSpeed).ToList();
                if (speeds.Any())
                {
                    var avgSpeed = speeds.Average();
                    var maxSpeed = speeds.Max();

                    // Reward conservative speeds for bush flying
                    if (maxSpeed < 150) score += 5; // Conservative speed
                    else if (maxSpeed > 200) score -= 10; // Too fast for bush flying
                }
            }

            return Math.Max(0, Math.Min(100, score));
        }

        /// <summary>
        /// Calculate overall bush flying score with appropriate weights
        /// </summary>
        private double CalculateBushFlyingOverallScore(BushFlyingLegScore score)
        {
            // Bush flying specific weights
            var weights = new Dictionary<string, double>
            {
                ["Navigation"] = 0.25,      // Critical for bush flying
                ["Terrain"] = 0.20,        // Very important
                ["Fuel"] = 0.20,           // Critical for remote operations
                ["Weather"] = 0.15,        // Important adaptation
                ["Landing"] = 0.15,        // Short field skills
                ["Safety"] = 0.05          // Always important
            };

            return (score.NavigationAccuracy * weights["Navigation"]) +
                   (score.TerrainAwareness * weights["Terrain"]) +
                   (score.FuelManagement * weights["Fuel"]) +
                   (score.WeatherAdaptation * weights["Weather"]) +
                   (score.LandingTechnique * weights["Landing"]) +
                   (score.SafetyConsciousness * weights["Safety"]);
        }

        /// <summary>
        /// Helper method to calculate distance between two points
        /// </summary>
        private double CalculateDistance(double lat1, double lon1, double lat2, double lon2)
        {
            var R = 3440.065; // Earth's radius in nautical miles
            var dLat = (lat2 - lat1) * Math.PI / 180;
            var dLon = (lon2 - lon1) * Math.PI / 180;
            var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                    Math.Cos(lat1 * Math.PI / 180) * Math.Cos(lat2 * Math.PI / 180) *
                    Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
            var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
            return R * c;
        }

        /// <summary>
        /// Helper method to calculate bearing between two points
        /// </summary>
        private double CalculateBearing(double lat1, double lon1, double lat2, double lon2)
        {
            var dLon = (lon2 - lon1) * Math.PI / 180;
            var lat1Rad = lat1 * Math.PI / 180;
            var lat2Rad = lat2 * Math.PI / 180;

            var y = Math.Sin(dLon) * Math.Cos(lat2Rad);
            var x = Math.Cos(lat1Rad) * Math.Sin(lat2Rad) - Math.Sin(lat1Rad) * Math.Cos(lat2Rad) * Math.Cos(dLon);

            var bearing = Math.Atan2(y, x) * 180 / Math.PI;
            return (bearing + 360) % 360;
        }

        /// <summary>
        /// Assign letter grade based on score
        /// </summary>
        private string AssignGrade(double score)
        {
            return score switch
            {
                >= 97 => "A+",
                >= 93 => "A",
                >= 90 => "A-",
                >= 87 => "B+",
                >= 83 => "B",
                >= 80 => "B-",
                >= 77 => "C+",
                >= 73 => "C",
                >= 70 => "C-",
                >= 67 => "D+",
                >= 63 => "D",
                >= 60 => "D-",
                _ => "F"
            };
        }

        /// <summary>
        /// Generate strengths based on performance
        /// </summary>
        private List<string> GenerateStrengths(BushFlyingLegScore score)
        {
            var strengths = new List<string>();

            if (score.NavigationAccuracy >= 85)
                strengths.Add("Excellent navigation skills");
            if (score.TerrainAwareness >= 85)
                strengths.Add("Strong terrain awareness");
            if (score.FuelManagement >= 85)
                strengths.Add("Efficient fuel management");
            if (score.WeatherAdaptation >= 85)
                strengths.Add("Good weather adaptation");
            if (score.SafetyConsciousness >= 85)
                strengths.Add("Safety-conscious flying");

            if (!strengths.Any())
                strengths.Add("Completed the leg successfully");

            return strengths;
        }

        /// <summary>
        /// Generate areas for improvement
        /// </summary>
        private List<string> GenerateAreasForImprovement(BushFlyingLegScore score)
        {
            var improvements = new List<string>();

            if (score.NavigationAccuracy < 70)
                improvements.Add("Work on navigation accuracy and course tracking");
            if (score.TerrainAwareness < 70)
                improvements.Add("Improve terrain awareness and altitude management");
            if (score.FuelManagement < 70)
                improvements.Add("Focus on fuel efficiency and consumption monitoring");
            if (score.WeatherAdaptation < 70)
                improvements.Add("Practice flying in various weather conditions");
            if (score.SafetyConsciousness < 70)
                improvements.Add("Emphasize safety procedures and conservative flying");

            return improvements;
        }

        /// <summary>
        /// Evaluate challenges encountered during the leg
        /// </summary>
        private List<string> EvaluateChallengesEncountered(BushFlyingWaypoint waypoint)
        {
            var challenges = new List<string>();

            // Add waypoint-specific challenges
            challenges.AddRange(waypoint.LocalChallenges);

            // Add challenges based on flight data analysis
            if (_legFlightData.Any())
            {
                var altitudes = _legFlightData.Where(p => !p.OnGround).Select(p => p.Altitude).ToList();
                if (altitudes.Any())
                {
                    var altitudeVariation = altitudes.Max() - altitudes.Min();
                    if (altitudeVariation > 2000)
                        challenges.Add("Significant altitude changes");
                }

                var speeds = _legFlightData.Where(p => !p.OnGround).Select(p => p.GroundSpeed).ToList();
                if (speeds.Any() && speeds.Max() > 180)
                    challenges.Add("High speed operations");
            }

            return challenges.Distinct().ToList();
        }

        /// <summary>
        /// Evaluate skills demonstrated during the leg
        /// </summary>
        private List<string> EvaluateSkillsDemonstrated(BushFlyingWaypoint waypoint, BushFlyingLegScore score)
        {
            var skills = new List<string>();

            if (score.NavigationAccuracy >= 80)
                skills.Add("Precise navigation");
            if (score.TerrainAwareness >= 80)
                skills.Add("Terrain awareness");
            if (score.FuelManagement >= 80)
                skills.Add("Fuel management");
            if (score.WeatherAdaptation >= 80)
                skills.Add("Weather adaptation");

            // Add specific bush flying skills
            skills.Add("Bush flying operations");

            if (waypoint.Airport?.Runways?.Any(r => r.LengthFeet < 3000) == true)
                skills.Add("Short field operations");

            return skills;
        }

        /// <summary>
        /// Generate detailed notes for the leg
        /// </summary>
        private string GenerateLegNotes(BushFlyingWaypoint waypoint, BushFlyingLegScore score, double flightTimeMinutes, double fuelUsed)
        {
            var notes = new List<string>();

            notes.Add($"Completed leg to {waypoint.Name} in {flightTimeMinutes:F1} minutes");
            notes.Add($"Fuel consumption: {fuelUsed:F1} gallons");
            notes.Add($"Overall bush flying performance: {score.Grade} ({score.OverallScore:F1}%)");

            if (score.OverallScore >= 90)
                notes.Add("Excellent bush flying demonstration");
            else if (score.OverallScore >= 80)
                notes.Add("Good bush flying skills shown");
            else if (score.OverallScore >= 70)
                notes.Add("Adequate bush flying performance");
            else
                notes.Add("Bush flying skills need improvement");

            return string.Join(". ", notes) + ".";
        }

        /// <summary>
        /// Complete the entire bush flying challenge
        /// </summary>
        public void CompleteChallenge()
        {
            if (_currentChallenge == null) return;

            _currentChallenge.Status = BushFlyingStatus.Completed;

            var overallScore = _currentChallenge.CompletedLegs.Average(l => l.Score.OverallScore);
            var totalTime = _currentChallenge.CompletedLegs.Sum(l => l.FlightTimeMinutes);
            var totalFuel = _currentChallenge.CompletedLegs.Sum(l => l.FuelUsedGallons);

            Console.WriteLine($"\n=== BUSH FLYING CHALLENGE COMPLETED ===");
            Console.WriteLine($"Challenge: {_currentChallenge.Title}");
            Console.WriteLine($"Overall Score: {overallScore:F1}% ({AssignGrade(overallScore)})");
            Console.WriteLine($"Total Flight Time: {totalTime:F1} minutes");
            Console.WriteLine($"Total Fuel Used: {totalFuel:F1} gallons");
            Console.WriteLine($"Legs Completed: {_currentChallenge.CompletedLegs.Count}/{_currentChallenge.Waypoints.Count}");

            if (overallScore >= 85)
                Console.WriteLine("🏆 Outstanding bush flying performance!");
            else if (overallScore >= 75)
                Console.WriteLine("✅ Good bush flying skills demonstrated");
            else
                Console.WriteLine("📚 Continue practicing bush flying techniques");
        }
    }
}
