using System;
using System.IO;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using NAudio.Wave;
using FlightPig.Models;

namespace FlightPig.Services
{
    /// <summary>
    /// Text-to-speech service using ElevenLabs API
    /// </summary>
    public class TextToSpeechService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly AppSettings _settings;
        private WaveOutEvent _waveOut;
        private readonly object _lockObject = new object();

        public TextToSpeechService(HttpClient httpClient, AppSettings settings)
        {
            _httpClient = httpClient;
            _settings = settings;
            
            if (!string.IsNullOrEmpty(_settings.ElevenLabs.ApiKey))
            {
                _httpClient.DefaultRequestHeaders.Add("xi-api-key", _settings.ElevenLabs.ApiKey);
            }
        }

        /// <summary>
        /// Speak text using the default voice
        /// </summary>
        public async Task SpeakAsync(string text, bool isPilotVoice = false)
        {
            if (!_settings.ElevenLabs.Enabled || string.IsNullOrEmpty(text))
            {
                Console.WriteLine($"TTS: {text}"); // Fallback to console output
                return;
            }

            var voiceId = isPilotVoice ? _settings.ElevenLabs.PilotVoiceId : _settings.ElevenLabs.DefaultVoiceId;
            await SpeakWithVoiceAsync(text, voiceId);
        }

        /// <summary>
        /// Speak text using a specific voice
        /// </summary>
        public async Task SpeakWithVoiceAsync(string text, string voiceId)
        {
            if (!_settings.ElevenLabs.Enabled || string.IsNullOrEmpty(_settings.ElevenLabs.ApiKey))
            {
                Console.WriteLine($"TTS: {text}"); // Fallback to console output
                return;
            }

            try
            {
                Console.WriteLine($"🔊 Speaking: \"{text}\"");
                
                var audioData = await GenerateSpeechAsync(text, voiceId);
                if (audioData != null)
                {
                    await PlayAudioAsync(audioData);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TTS Error: {ex.Message}");
                Console.WriteLine($"TTS Fallback: {text}"); // Fallback to console output
            }
        }

        /// <summary>
        /// Generate speech audio data from text
        /// </summary>
        private async Task<byte[]> GenerateSpeechAsync(string text, string voiceId)
        {
            try
            {
                var requestBody = new
                {
                    text = text,
                    model_id = "eleven_monolingual_v1",
                    voice_settings = new
                    {
                        stability = _settings.ElevenLabs.Stability,
                        similarity_boost = _settings.ElevenLabs.SimilarityBoost
                    }
                };

                var json = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var url = $"{_settings.ElevenLabs.BaseUrl}/text-to-speech/{voiceId}";
                var response = await _httpClient.PostAsync(url, content);

                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadAsByteArrayAsync();
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"ElevenLabs API Error: {response.StatusCode} - {errorContent}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating speech: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Play audio data through speakers
        /// </summary>
        private async Task PlayAudioAsync(byte[] audioData)
        {
            try
            {
                lock (_lockObject)
                {
                    _waveOut?.Stop();
                    _waveOut?.Dispose();
                }

                using (var audioStream = new MemoryStream(audioData))
                using (var reader = new Mp3FileReader(audioStream))
                {
                    var waveOut = new WaveOutEvent();
                    
                    lock (_lockObject)
                    {
                        _waveOut = waveOut;
                    }

                    waveOut.Volume = (float)_settings.Voice.Volume;
                    waveOut.Init(reader);
                    waveOut.Play();

                    // Wait for playback to complete
                    while (waveOut.PlaybackState == PlaybackState.Playing)
                    {
                        await Task.Delay(100);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error playing audio: {ex.Message}");
            }
            finally
            {
                lock (_lockObject)
                {
                    _waveOut?.Dispose();
                    _waveOut = null;
                }
            }
        }

        /// <summary>
        /// Speak mission information with appropriate voice
        /// </summary>
        public async Task SpeakMissionAsync(Mission mission)
        {
            if (mission == null) return;

            var missionText = $"New mission: {mission.Title}. {mission.Description}";
            await SpeakAsync(missionText, isPilotVoice: true);

            if (mission.Objectives?.Count > 0)
            {
                await Task.Delay(500); // Brief pause
                
                var objectiveText = $"You have {mission.Objectives.Count} objectives:";
                for (int i = 0; i < mission.Objectives.Count; i++)
                {
                    objectiveText += $" {i + 1}. {mission.Objectives[i].Title}.";
                }
                
                await SpeakAsync(objectiveText, isPilotVoice: true);
            }
        }

        /// <summary>
        /// Speak objective completion
        /// </summary>
        public async Task SpeakObjectiveCompletedAsync(Objective objective)
        {
            if (objective == null) return;

            var text = $"Objective completed: {objective.Title}";
            await SpeakAsync(text, isPilotVoice: true);
        }

        /// <summary>
        /// Speak aircraft status information
        /// </summary>
        public async Task SpeakAircraftStatusAsync(AircraftInfo aircraftInfo)
        {
            var statusText = $"Aircraft status: {aircraftInfo.Title}. " +
                           $"Altitude {aircraftInfo.Altitude:F0} feet. " +
                           $"Airspeed {aircraftInfo.AirspeedKnots:F0} knots. " +
                           $"Heading {aircraftInfo.Heading:F0} degrees. " +
                           $"{(aircraftInfo.OnGround ? "On ground" : "In flight")}.";

            await SpeakAsync(statusText, isPilotVoice: true);
        }

        /// <summary>
        /// Speak confirmation messages
        /// </summary>
        public async Task SpeakConfirmationAsync(string message)
        {
            await SpeakAsync(message, isPilotVoice: false);
        }

        /// <summary>
        /// Speak error messages
        /// </summary>
        public async Task SpeakErrorAsync(string error)
        {
            var errorText = $"Error: {error}";
            await SpeakAsync(errorText, isPilotVoice: false);
        }

        /// <summary>
        /// Speak tour introduction with aircraft-specific context
        /// </summary>
        public async Task SpeakTourIntroductionAsync(Tour tour, string aircraftTitle = null)
        {
            if (tour == null) return;

            var aircraftType = DetermineAircraftType(aircraftTitle);
            var aircraftSpecificIntro = GetAircraftSpecificIntroduction(aircraftType);

            var introText = $"Welcome to {tour.Title}. {aircraftSpecificIntro} {tour.Introduction}";
            await SpeakAsync(introText, isPilotVoice: false); // Use tour guide voice

            if (!string.IsNullOrEmpty(tour.Description))
            {
                await Task.Delay(500);
                await SpeakAsync(tour.Description, isPilotVoice: false);
            }

            // Announce first POI with aircraft-specific guidance
            if (tour.PointsOfInterest?.Count > 0)
            {
                await Task.Delay(1000);
                var firstPoi = tour.PointsOfInterest[0];
                var aircraftGuidance = GetAircraftSpecificGuidance(aircraftType);
                var firstPoiText = $"Our first destination is {firstPoi.Name}. {aircraftGuidance} {firstPoi.NextPoiInstructions}";
                await SpeakAsync(firstPoiText, isPilotVoice: false);
            }
        }

        /// <summary>
        /// Speak POI information when reached
        /// </summary>
        public async Task SpeakPoiInformationAsync(string poiName, string tourGuideText, string nextPoiInstructions = null)
        {
            if (string.IsNullOrEmpty(tourGuideText)) return;

            // Announce arrival
            var arrivalText = $"We have arrived at {poiName}.";
            await SpeakAsync(arrivalText, isPilotVoice: false);

            await Task.Delay(1000);

            // Speak the detailed tour guide information
            await SpeakAsync(tourGuideText, isPilotVoice: false);

            // Give instructions for next POI if provided
            if (!string.IsNullOrEmpty(nextPoiInstructions))
            {
                await Task.Delay(1500);
                await SpeakAsync(nextPoiInstructions, isPilotVoice: false);
            }
        }

        /// <summary>
        /// Speak tour conclusion
        /// </summary>
        public async Task SpeakTourConclusionAsync(Tour tour)
        {
            if (tour == null) return;

            var conclusionText = !string.IsNullOrEmpty(tour.Conclusion)
                ? tour.Conclusion
                : $"This concludes our tour of {tour.Title}. Thank you for flying with us today!";

            await SpeakAsync(conclusionText, isPilotVoice: false);
        }

        /// <summary>
        /// Speak tour progress update
        /// </summary>
        public async Task SpeakTourProgressAsync(int currentPoi, int totalPois, string nextPoiName)
        {
            var progressText = $"Point of interest {currentPoi} of {totalPois} completed.";

            if (!string.IsNullOrEmpty(nextPoiName))
            {
                progressText += $" Next destination: {nextPoiName}.";
            }

            await SpeakAsync(progressText, isPilotVoice: false);
        }

        /// <summary>
        /// Test TTS with a sample message
        /// </summary>
        public async Task TestSpeechAsync()
        {
            await SpeakAsync("FlightPig text-to-speech is working correctly.", isPilotVoice: false);
            await Task.Delay(1000);
            await SpeakAsync("This is the pilot voice speaking.", isPilotVoice: true);
        }

        /// <summary>
        /// Stop any currently playing audio
        /// </summary>
        public void StopSpeaking()
        {
            lock (_lockObject)
            {
                _waveOut?.Stop();
            }
        }

        /// <summary>
        /// Determine aircraft type based on aircraft title
        /// </summary>
        private string DetermineAircraftType(string aircraftTitle)
        {
            if (string.IsNullOrEmpty(aircraftTitle))
                return "General Aviation";

            var title = aircraftTitle.ToLower();

            if (title.Contains("helicopter") || title.Contains("heli"))
                return "Helicopter";
            if (title.Contains("jet") || title.Contains("boeing") || title.Contains("airbus"))
                return "Jet";
            if (title.Contains("turboprop") || title.Contains("king air"))
                return "Turboprop";
            if (title.Contains("glider") || title.Contains("sailplane"))
                return "Glider";
            if (title.Contains("seaplane") || title.Contains("floatplane"))
                return "Seaplane";

            return "General Aviation";
        }

        /// <summary>
        /// Get aircraft-specific introduction text
        /// </summary>
        private string GetAircraftSpecificIntroduction(string aircraftType)
        {
            return aircraftType switch
            {
                "Helicopter" => "Today we'll be taking advantage of your helicopter's unique capabilities for low-altitude sightseeing and precision flying.",
                "Jet" => "We'll be conducting a high-altitude tour that showcases the speed and range capabilities of your jet aircraft.",
                "Turboprop" => "Your turboprop aircraft is perfect for this medium-altitude tour that balances efficiency with detailed sightseeing.",
                "Glider" => "We'll be using thermal currents and ridge lift to explore the area in your glider, focusing on soaring opportunities.",
                "Seaplane" => "Your amphibious aircraft opens up unique opportunities to explore both land and water features on this tour.",
                _ => "Your general aviation aircraft is well-suited for this scenic tour of the local area."
            };
        }

        /// <summary>
        /// Get aircraft-specific flight guidance
        /// </summary>
        private string GetAircraftSpecificGuidance(string aircraftType)
        {
            return aircraftType switch
            {
                "Helicopter" => "Take your time and use your hover capability to get the best views.",
                "Jet" => "Maintain efficient cruise speeds and altitudes for optimal fuel consumption.",
                "Turboprop" => "Use moderate climb and descent rates for passenger comfort.",
                "Glider" => "Look for thermal activity and ridge lift to maintain altitude.",
                "Seaplane" => "Be aware of water landing opportunities along the route.",
                _ => "Maintain standard VFR procedures and enjoy the scenery."
            };
        }

        public void Dispose()
        {
            lock (_lockObject)
            {
                _waveOut?.Stop();
                _waveOut?.Dispose();
                _waveOut = null;
            }
        }
    }
}
