<!--
 ***********************************************************************************************
 Microsoft.CodeCoverage.targets

 WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
           created a backup copy.  Incorrect changes to this file will make it
           impossible to load or build your test projects from the command-line or the IDE.

 Copyright (c) Microsoft. All rights reserved.
 ***********************************************************************************************
-->

<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <!-- This target required to enable /collect:"Code Coverage" in "dotnet publish" scenario with "dotnet vstest".
       E.g: Release pipelines where user/project nuget cache not available on current machine. -->
  <Target Name="CopyTraceDataCollectorArtifacts" AfterTargets="ComputeFilesToPublish">

    <ItemGroup>
      <TraceDataCollectorArtifacts Include="$(MSBuildThisFileDirectory)\**\*.*" />
    </ItemGroup>

    <Copy SourceFiles="@(TraceDataCollectorArtifacts)" DestinationFolder="$(PublishDir)%(RecursiveDir)" />

  </Target>
</Project>
