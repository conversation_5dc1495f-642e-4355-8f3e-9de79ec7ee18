﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.Cryptography.Algorithms</name>
  </assembly>
  <members>
    <member name="T:System.Security.Cryptography.Aes">
      <summary>Represents the abstract base class from which all implementations of the Advanced Encryption Standard (AES) must inherit.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Aes.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Aes" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Aes.Create">
      <summary>Creates a cryptographic object that is used to perform the symmetric algorithm.</summary>
      <returns>A cryptographic object that is used to perform the symmetric algorithm.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Aes.Create(System.String)">
      <summary>Creates a cryptographic object that specifies the implementation of AES to use to perform the symmetric algorithm.</summary>
      <param name="algorithmName">The name of the specific implementation of AES to use.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="algorithmName" /> parameter is <see langword="null" />.</exception>
      <returns>A cryptographic object that is used to perform the symmetric algorithm.</returns>
    </member>
    <member name="T:System.Security.Cryptography.AesCcm">
      <summary>Represents an Advanced Encryption Standard (AES) key to be used with the Counter with CBC-MAC (CCM) mode of operation.</summary>
    </member>
    <member name="M:System.Security.Cryptography.AesCcm.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.AesCcm" /> class with a provided key.</summary>
      <param name="key">The secret key to use for this instance.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="key" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="key" /> parameter length is other than 16, 24, or 32 bytes (128, 192, or 256 bits).</exception>
    </member>
    <member name="M:System.Security.Cryptography.AesCcm.#ctor(System.ReadOnlySpan{System.Byte})">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.AesCcm" /> class with a provided key.</summary>
      <param name="key">The secret key to use for this instance.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="key" /> parameter length is other than 16, 24, or 32 bytes (128, 192, or 256 bits).</exception>
    </member>
    <member name="M:System.Security.Cryptography.AesCcm.Decrypt(System.Byte[],System.Byte[],System.Byte[],System.Byte[],System.Byte[])">
      <summary>Decrypts the ciphertext into the provided destination buffer if the authentication tag can be validated.</summary>
      <param name="nonce">The nonce associated with this message, which must match the value provided during encryption.</param>
      <param name="ciphertext">The encrypted content to decrypt.</param>
      <param name="tag">The authentication tag produced for this message during encryption.</param>
      <param name="plaintext">The byte array to receive the decrypted contents.</param>
      <param name="associatedData">Extra data associated with this message, which must match the value provided during encryption.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="plaintext" /> parameter and the <paramref name="ciphertext" /> do not have the same length.

-or-

The <paramref name="nonce" /> parameter length is not permitted by <see cref="P:System.Security.Cryptography.AesCcm.NonceByteSizes" />.

-or-

The <paramref name="tag" /> parameter length is not permitted by <see cref="P:System.Security.Cryptography.AesCcm.TagByteSizes" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="nonce" />, <paramref name="ciphertext" />, <paramref name="tag" />, or <paramref name="plaintext" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The tag value could not be verified, or the decryption operation otherwise failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.AesCcm.Decrypt(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.ReadOnlySpan{System.Byte})">
      <summary>Decrypts the ciphertext into the provided destination buffer if the authentication tag can be validated.</summary>
      <param name="nonce">The nonce associated with this message, which must match the value provided during encryption.</param>
      <param name="ciphertext">The encrypted content to decrypt.</param>
      <param name="tag">The authentication tag produced for this message during encryption.</param>
      <param name="plaintext">The byte span to receive the decrypted contents.</param>
      <param name="associatedData">Extra data associated with this message, which must match the value provided during encryption.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="plaintext" /> parameter and the <paramref name="ciphertext" /> do not have the same length.

-or-

The <paramref name="nonce" /> parameter length is not permitted by <see cref="P:System.Security.Cryptography.AesCcm.NonceByteSizes" />.

-or-

The <paramref name="tag" /> parameter length is not permitted by <see cref="P:System.Security.Cryptography.AesCcm.TagByteSizes" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The tag value could not be verified, or the decryption operation otherwise failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.AesCcm.Dispose">
      <summary>Releases the resources used by the current instance of the <see cref="T:System.Security.Cryptography.AesCcm" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.AesCcm.Encrypt(System.Byte[],System.Byte[],System.Byte[],System.Byte[],System.Byte[])">
      <summary>Encrypts the plaintext into the ciphertext destination buffer and generates the authentication tag into a separate buffer.</summary>
      <param name="nonce">The nonce associated with this message, which should be a unique value for every operation with the same key.</param>
      <param name="plaintext">The content to encrypt.</param>
      <param name="ciphertext">The byte array to receive the encrypted contents.</param>
      <param name="tag">The byte array to receive the generated authentication tag.</param>
      <param name="associatedData">Extra data associated with this message, which must also be provided during decryption.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="plaintext" /> parameter and the <paramref name="ciphertext" /> do not have the same length.

-or-

The <paramref name="nonce" /> parameter length is not permitted by <see cref="P:System.Security.Cryptography.AesCcm.NonceByteSizes" />.

-or-

The <paramref name="tag" /> parameter length is not permitted by <see cref="P:System.Security.Cryptography.AesCcm.TagByteSizes" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="nonce" />, <paramref name="ciphertext" />, <paramref name="tag" />, or <paramref name="plaintext" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The encryption operation failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.AesCcm.Encrypt(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Span{System.Byte},System.ReadOnlySpan{System.Byte})">
      <summary>Encrypts the plaintext into the ciphertext destination buffer and generates the authentication tag into a separate buffer.</summary>
      <param name="nonce">The nonce associated with this message, which should be a unique value for every operation with the same key.</param>
      <param name="plaintext">The content to encrypt.</param>
      <param name="ciphertext">The byte span to receive the encrypted contents.</param>
      <param name="tag">The byte span to receive the generated authentication tag.</param>
      <param name="associatedData">Extra data associated with this message, which must also be provided during decryption.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="plaintext" /> parameter and the <paramref name="ciphertext" /> do not have the same length.

-or-

The <paramref name="nonce" /> parameter length is not permitted by <see cref="P:System.Security.Cryptography.AesCcm.NonceByteSizes" />.

-or-

The <paramref name="tag" /> parameter length is not permitted by <see cref="P:System.Security.Cryptography.AesCcm.TagByteSizes" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The encryption operation failed.</exception>
    </member>
    <member name="P:System.Security.Cryptography.AesCcm.IsSupported">
      <summary>Gets a value that indicates whether the algorithm is supported on the current platform.</summary>
      <returns>
        <see langword="true" /> if the algorithm is supported; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.AesCcm.NonceByteSizes">
      <summary>Gets the nonce sizes, in bytes, supported by this instance.</summary>
      <returns>The nonce sizes supported by this instance: 7, 8, 9, 10, 11, 12, or 13 bytes (56, 64, 72, 80, 88, 96, or 104 bits).</returns>
    </member>
    <member name="P:System.Security.Cryptography.AesCcm.TagByteSizes">
      <summary>Gets the tag sizes, in bytes, supported by this instance.</summary>
      <returns>The tag sizes supported by this instance: 4, 6, 8, 10, 12, 14, or 16 bytes (32, 48, 64, 80, 96, 112, or 128 bits).</returns>
    </member>
    <member name="T:System.Security.Cryptography.AesGcm">
      <summary>Represents an Advanced Encryption Standard (AES) key to be used with the Galois/Counter Mode (GCM) mode of operation.</summary>
    </member>
    <member name="M:System.Security.Cryptography.AesGcm.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.AesGcm" /> class with a provided key.</summary>
      <param name="key">The secret key to use for this instance.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="key" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="key" /> parameter length is other than 16, 24, or 32 bytes (128, 192, or 256 bits).</exception>
    </member>
    <member name="M:System.Security.Cryptography.AesGcm.#ctor(System.ReadOnlySpan{System.Byte})">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.AesGcm" /> class with a provided key.</summary>
      <param name="key">The secret key to use for this instance.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="key" /> parameter length is other than 16, 24, or 32 bytes (128, 192, or 256 bits).</exception>
    </member>
    <member name="M:System.Security.Cryptography.AesGcm.Decrypt(System.Byte[],System.Byte[],System.Byte[],System.Byte[],System.Byte[])">
      <summary>Decrypts the ciphertext into the provided destination buffer if the authentication tag can be validated.</summary>
      <param name="nonce">The nonce associated with this message, which must match the value provided during encryption.</param>
      <param name="ciphertext">The encrypted content to decrypt.</param>
      <param name="tag">The authentication tag produced for this message during encryption.</param>
      <param name="plaintext">The byte array to receive the decrypted contents.</param>
      <param name="associatedData">Extra data associated with this message, which must match the value provided during encryption.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="plaintext" /> parameter and the <paramref name="ciphertext" /> do not have the same length.

-or-

The <paramref name="nonce" /> parameter length is not permitted by <see cref="P:System.Security.Cryptography.AesGcm.NonceByteSizes" />.

-or-

The <paramref name="tag" /> parameter length is not permitted by <see cref="P:System.Security.Cryptography.AesGcm.TagByteSizes" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="nonce" />, <paramref name="ciphertext" />, <paramref name="tag" />, or <paramref name="plaintext" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The tag value could not be verified, or the decryption operation otherwise failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.AesGcm.Decrypt(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.ReadOnlySpan{System.Byte})">
      <summary>Decrypts the ciphertext into the provided destination buffer if the authentication tag can be validated.</summary>
      <param name="nonce">The nonce associated with this message, which must match the value provided during encryption.</param>
      <param name="ciphertext">The encrypted content to decrypt.</param>
      <param name="tag">The authentication tag produced for this message during encryption.</param>
      <param name="plaintext">The byte span to receive the decrypted contents.</param>
      <param name="associatedData">Extra data associated with this message, which must match the value provided during encryption.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="plaintext" /> parameter and the <paramref name="ciphertext" /> do not have the same length.

-or-

The <paramref name="nonce" /> parameter length is not permitted by <see cref="P:System.Security.Cryptography.AesGcm.NonceByteSizes" />.

-or-

The <paramref name="tag" /> parameter length is not permitted by <see cref="P:System.Security.Cryptography.AesGcm.TagByteSizes" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The tag value could not be verified, or the decryption operation otherwise failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.AesGcm.Dispose">
      <summary>Releases the resources used by the current instance of the <see cref="T:System.Security.Cryptography.AesGcm" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.AesGcm.Encrypt(System.Byte[],System.Byte[],System.Byte[],System.Byte[],System.Byte[])">
      <summary>Encrypts the plaintext into the ciphertext destination buffer and generates the authentication tag into a separate buffer.</summary>
      <param name="nonce">The nonce associated with this message, which should be a unique value for every operation with the same key.</param>
      <param name="plaintext">The content to encrypt.</param>
      <param name="ciphertext">The byte array to receive the encrypted contents.</param>
      <param name="tag">The byte array to receive the generated authentication tag.</param>
      <param name="associatedData">Extra data associated with this message, which must also be provided during decryption.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="plaintext" /> parameter and the <paramref name="ciphertext" /> do not have the same length.

-or-

The <paramref name="nonce" /> parameter length is not permitted by <see cref="P:System.Security.Cryptography.AesGcm.NonceByteSizes" />.

-or-

The <paramref name="tag" /> parameter length is not permitted by <see cref="P:System.Security.Cryptography.AesGcm.TagByteSizes" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="nonce" />, <paramref name="ciphertext" />, <paramref name="tag" />, or <paramref name="plaintext" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The encryption operation failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.AesGcm.Encrypt(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Span{System.Byte},System.ReadOnlySpan{System.Byte})">
      <summary>Encrypts the plaintext into the ciphertext destination buffer and generates the authentication tag into a separate buffer.</summary>
      <param name="nonce">The nonce associated with this message, which should be a unique value for every operation with the same key.</param>
      <param name="plaintext">The content to encrypt.</param>
      <param name="ciphertext">The byte array to receive the encrypted contents.</param>
      <param name="tag">The byte array to receive the generated authentication tag.</param>
      <param name="associatedData">Extra data associated with this message, which must also be provided during decryption.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="plaintext" /> parameter and the <paramref name="ciphertext" /> do not have the same length.

-or-

The <paramref name="nonce" /> parameter length is not permitted by <see cref="P:System.Security.Cryptography.AesGcm.NonceByteSizes" />.

-or-

The <paramref name="tag" /> parameter length is not permitted by <see cref="P:System.Security.Cryptography.AesGcm.TagByteSizes" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The encryption operation failed.</exception>
    </member>
    <member name="P:System.Security.Cryptography.AesGcm.IsSupported">
      <summary>Gets a value that indicates whether the algorithm is supported on the current platform.</summary>
      <returns>
        <see langword="true" /> if the algorithm is supported; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.AesGcm.NonceByteSizes">
      <summary>Gets the nonce sizes, in bytes, supported by this instance.</summary>
      <returns>The nonce sizes supported by this instance: 12 bytes (96 bits).</returns>
    </member>
    <member name="P:System.Security.Cryptography.AesGcm.TagByteSizes">
      <summary>Gets the tag sizes, in bytes, supported by this instance.</summary>
      <returns>The tag sizes supported by this instance: 12, 13, 14, 15, or 16 bytes (96, 104, 112, 120, or 128 bits).</returns>
    </member>
    <member name="T:System.Security.Cryptography.AesManaged">
      <summary>Provides a managed implementation of the Advanced Encryption Standard (AES) symmetric algorithm.</summary>
    </member>
    <member name="M:System.Security.Cryptography.AesManaged.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.AesManaged" /> class.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The Windows security policy setting for FIPS is enabled.</exception>
      <exception cref="T:System.InvalidOperationException">This implementation is not part of the Windows Platform FIPS-validated cryptographic algorithms.</exception>
    </member>
    <member name="M:System.Security.Cryptography.AesManaged.CreateDecryptor">
      <summary>Creates a symmetric decryptor object using the current key and initialization vector (IV).</summary>
      <returns>A symmetric decryptor object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.AesManaged.CreateDecryptor(System.Byte[],System.Byte[])">
      <summary>Creates a symmetric decryptor object using the specified key and initialization vector (IV).</summary>
      <param name="rgbKey">The secret key to use for the symmetric algorithm.</param>
      <param name="rgbIV">The initialization vector to use for the symmetric algorithm.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> or <paramref name="iv" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> is invalid.</exception>
      <returns>A symmetric decryptor object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.AesManaged.CreateEncryptor">
      <summary>Creates a symmetric encryptor object using the current key and initialization vector (IV).</summary>
      <returns>A symmetric encryptor object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.AesManaged.CreateEncryptor(System.Byte[],System.Byte[])">
      <summary>Creates a symmetric encryptor object using the specified key and initialization vector (IV).</summary>
      <param name="rgbKey">The secret key to use for the symmetric algorithm.</param>
      <param name="rgbIV">The initialization vector to use for the symmetric algorithm.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> or <paramref name="iv" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> is invalid.</exception>
      <returns>A symmetric encryptor object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.AesManaged.GenerateIV">
      <summary>Generates a random initialization vector (IV) to use for the symmetric algorithm.</summary>
    </member>
    <member name="M:System.Security.Cryptography.AesManaged.GenerateKey">
      <summary>Generates a random key to use for the symmetric algorithm.</summary>
    </member>
    <member name="P:System.Security.Cryptography.AesManaged.BlockSize">
      <summary>Gets or sets the block size, in bits, of the cryptographic operation.</summary>
      <returns>The block size, in bits, of the cryptographic operation. The default is 128 bits.</returns>
    </member>
    <member name="P:System.Security.Cryptography.AesManaged.FeedbackSize">
      <summary>Gets or sets the number of bits to use as feedback.</summary>
      <returns>The feedback size, in bits.</returns>
    </member>
    <member name="P:System.Security.Cryptography.AesManaged.IV">
      <summary>Gets or sets the initialization vector (IV) to use for the symmetric algorithm.</summary>
      <returns>The initialization vector to use for the symmetric algorithm.</returns>
    </member>
    <member name="P:System.Security.Cryptography.AesManaged.Key">
      <summary>Gets or sets the secret key used for the symmetric algorithm.</summary>
      <returns>The key for the symmetric algorithm.</returns>
    </member>
    <member name="P:System.Security.Cryptography.AesManaged.KeySize">
      <summary>Gets or sets the size, in bits, of the secret key used for the symmetric algorithm.</summary>
      <returns>The size, in bits, of the key used by the symmetric algorithm.</returns>
    </member>
    <member name="P:System.Security.Cryptography.AesManaged.LegalBlockSizes">
      <summary>Gets the block sizes, in bits, that are supported by the symmetric algorithm.</summary>
      <returns>The block sizes, in bits, that are supported by the symmetric algorithm.</returns>
    </member>
    <member name="P:System.Security.Cryptography.AesManaged.LegalKeySizes">
      <summary>Gets the key sizes, in bits, that are supported by the symmetric algorithm.</summary>
      <returns>The key sizes, in bits, that are supported by the symmetric algorithm.</returns>
    </member>
    <member name="P:System.Security.Cryptography.AesManaged.Mode">
      <summary>Gets or sets the mode for operation of the symmetric algorithm.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <see cref="P:System.Security.Cryptography.AesManaged.Mode" /> is set to <see cref="F:System.Security.Cryptography.CipherMode.CFB" /> or <see cref="F:System.Security.Cryptography.CipherMode.OFB" />.</exception>
      <returns>One of the enumeration values that specifies the block cipher mode to use for encryption. The default is <see cref="F:System.Security.Cryptography.CipherMode.CBC" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.AesManaged.Padding">
      <summary>Gets or sets the padding mode used in the symmetric algorithm.</summary>
      <returns>One of the enumeration values that specifies the type of padding to apply. The default is <see cref="F:System.Security.Cryptography.PaddingMode.PKCS7" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.AsymmetricKeyExchangeDeformatter">
      <summary>Represents the base class from which all asymmetric key exchange deformatters derive.</summary>
    </member>
    <member name="M:System.Security.Cryptography.AsymmetricKeyExchangeDeformatter.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.Security.Cryptography.AsymmetricKeyExchangeDeformatter" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.AsymmetricKeyExchangeDeformatter.DecryptKeyExchange(System.Byte[])">
      <summary>When overridden in a derived class, extracts secret information from the encrypted key exchange data.</summary>
      <param name="rgb">The key exchange data within which the secret information is hidden.</param>
      <returns>The secret information derived from the key exchange data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.AsymmetricKeyExchangeDeformatter.SetKey(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>When overridden in a derived class, sets the private key to use for decrypting the secret information.</summary>
      <param name="key">The instance of the implementation of <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> that holds the private key.</param>
    </member>
    <member name="P:System.Security.Cryptography.AsymmetricKeyExchangeDeformatter.Parameters">
      <summary>When overridden in a derived class, gets or sets the parameters for the asymmetric key exchange.</summary>
      <returns>A string in XML format containing the parameters of the asymmetric key exchange operation.</returns>
    </member>
    <member name="T:System.Security.Cryptography.AsymmetricKeyExchangeFormatter">
      <summary>Represents the base class from which all asymmetric key exchange formatters derive.</summary>
    </member>
    <member name="M:System.Security.Cryptography.AsymmetricKeyExchangeFormatter.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.Security.Cryptography.AsymmetricKeyExchangeFormatter" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.AsymmetricKeyExchangeFormatter.CreateKeyExchange(System.Byte[])">
      <summary>When overridden in a derived class, creates the encrypted key exchange data from the specified input data.</summary>
      <param name="data">The secret information to be passed in the key exchange.</param>
      <returns>The encrypted key exchange data to be sent to the intended recipient.</returns>
    </member>
    <member name="M:System.Security.Cryptography.AsymmetricKeyExchangeFormatter.CreateKeyExchange(System.Byte[],System.Type)">
      <summary>When overridden in a derived class, creates the encrypted key exchange data from the specified input data.</summary>
      <param name="data">The secret information to be passed in the key exchange.</param>
      <param name="symAlgType">This parameter is not used in the current version.</param>
      <returns>The encrypted key exchange data to be sent to the intended recipient.</returns>
    </member>
    <member name="M:System.Security.Cryptography.AsymmetricKeyExchangeFormatter.SetKey(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>When overridden in a derived class, sets the public key to use for encrypting the secret information.</summary>
      <param name="key">The instance of the implementation of <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> that holds the public key.</param>
    </member>
    <member name="P:System.Security.Cryptography.AsymmetricKeyExchangeFormatter.Parameters">
      <summary>When overridden in a derived class, gets the parameters for the asymmetric key exchange.</summary>
      <returns>A string in XML format containing the parameters of the asymmetric key exchange operation.</returns>
    </member>
    <member name="T:System.Security.Cryptography.AsymmetricSignatureDeformatter">
      <summary>Represents the abstract base class from which all implementations of asymmetric signature deformatters derive.</summary>
    </member>
    <member name="M:System.Security.Cryptography.AsymmetricSignatureDeformatter.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.Security.Cryptography.AsymmetricSignatureDeformatter" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.AsymmetricSignatureDeformatter.SetHashAlgorithm(System.String)">
      <summary>When overridden in a derived class, sets the hash algorithm to use for verifying the signature.</summary>
      <param name="strName">The name of the hash algorithm to use for verifying the signature.</param>
    </member>
    <member name="M:System.Security.Cryptography.AsymmetricSignatureDeformatter.SetKey(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>When overridden in a derived class, sets the public key to use for verifying the signature.</summary>
      <param name="key">The instance of an implementation of <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> that holds the public key.</param>
    </member>
    <member name="M:System.Security.Cryptography.AsymmetricSignatureDeformatter.VerifySignature(System.Byte[],System.Byte[])">
      <summary>When overridden in a derived class, verifies the signature for the specified data.</summary>
      <param name="rgbHash">The data signed with <paramref name="rgbSignature" />.</param>
      <param name="rgbSignature">The signature to be verified for <paramref name="rgbHash" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="rgbSignature" /> matches the signature computed using the specified hash algorithm and key on <paramref name="rgbHash" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.AsymmetricSignatureDeformatter.VerifySignature(System.Security.Cryptography.HashAlgorithm,System.Byte[])">
      <summary>Verifies the signature from the specified hash value.</summary>
      <param name="hash">The hash algorithm to use to verify the signature.</param>
      <param name="rgbSignature">The signature to be verified.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="hash" /> parameter is <see langword="null" />.</exception>
      <returns>
        <see langword="true" /> if the signature is valid for the hash; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.AsymmetricSignatureFormatter">
      <summary>Represents the base class from which all implementations of asymmetric signature formatters derive.</summary>
    </member>
    <member name="M:System.Security.Cryptography.AsymmetricSignatureFormatter.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.Security.Cryptography.AsymmetricSignatureFormatter" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.AsymmetricSignatureFormatter.CreateSignature(System.Byte[])">
      <summary>When overridden in a derived class, creates the signature for the specified data.</summary>
      <param name="rgbHash">The data to be signed.</param>
      <returns>The digital signature for the <paramref name="rgbHash" /> parameter.</returns>
    </member>
    <member name="M:System.Security.Cryptography.AsymmetricSignatureFormatter.CreateSignature(System.Security.Cryptography.HashAlgorithm)">
      <summary>Creates the signature from the specified hash value.</summary>
      <param name="hash">The hash algorithm to use to create the signature.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="hash" /> parameter is <see langword="null" />.</exception>
      <returns>The signature for the specified hash value.</returns>
    </member>
    <member name="M:System.Security.Cryptography.AsymmetricSignatureFormatter.SetHashAlgorithm(System.String)">
      <summary>When overridden in a derived class, sets the hash algorithm to use for creating the signature.</summary>
      <param name="strName">The name of the hash algorithm to use for creating the signature.</param>
    </member>
    <member name="M:System.Security.Cryptography.AsymmetricSignatureFormatter.SetKey(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>When overridden in a derived class, sets the asymmetric algorithm to use to create the signature.</summary>
      <param name="key">The instance of the implementation of <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> to use to create the signature.</param>
    </member>
    <member name="T:System.Security.Cryptography.ChaCha20Poly1305">
      <summary>Represents a symmetric key to be used with the ChaCha20 stream cipher in the combined mode with the Poly1305 authenticator.</summary>
    </member>
    <member name="M:System.Security.Cryptography.ChaCha20Poly1305.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.ChaCha20Poly1305" /> class with a provided key.</summary>
      <param name="key">The secret key to use for this instance.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="key" /> parameter length is not 32 bytes (256 bits).</exception>
    </member>
    <member name="M:System.Security.Cryptography.ChaCha20Poly1305.#ctor(System.ReadOnlySpan{System.Byte})">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.ChaCha20Poly1305" /> class with a provided key.</summary>
      <param name="key">The secret key to use for this instance.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="key" /> parameter length is not 32 bytes (256 bits).</exception>
    </member>
    <member name="M:System.Security.Cryptography.ChaCha20Poly1305.Decrypt(System.Byte[],System.Byte[],System.Byte[],System.Byte[],System.Byte[])">
      <summary>Decrypts the ciphertext into the provided destination buffer if the authentication tag can be validated.</summary>
      <param name="nonce">The nonce associated with this message, which must match the value provided during encryption.</param>
      <param name="ciphertext">The encrypted content to decrypt.</param>
      <param name="tag">The authentication tag produced for this message during encryption.</param>
      <param name="plaintext">The byte array to receive the decrypted contents.</param>
      <param name="associatedData">Extra data associated with this message, which must match the value provided during encryption.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="plaintext" /> parameter and the <paramref name="ciphertext" /> do not have the same length.

-or-

The <paramref name="nonce" /> parameter length is not 12 bytes (96 bits).

-or-

The <paramref name="tag" /> parameter length is not 16 bytes (128 bits).</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="nonce" />, <paramref name="ciphertext" />, <paramref name="tag" />, or <paramref name="plaintext" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The tag value could not be verified, or the decryption operation otherwise failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ChaCha20Poly1305.Decrypt(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.ReadOnlySpan{System.Byte})">
      <summary>Decrypts the ciphertext into the provided destination buffer if the authentication tag can be validated.</summary>
      <param name="nonce">The nonce associated with this message, which must match the value provided during encryption.</param>
      <param name="ciphertext">The encrypted content to decrypt.</param>
      <param name="tag">The authentication tag produced for this message during encryption.</param>
      <param name="plaintext">The byte array to receive the decrypted contents.</param>
      <param name="associatedData">Extra data associated with this message, which must match the value provided during encryption.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="plaintext" /> parameter and the <paramref name="ciphertext" /> do not have the same length.

-or-

The <paramref name="nonce" /> parameter length is not 12 bytes (96 bits).

-or-

The <paramref name="tag" /> parameter length is not 16 bytes (128 bits).</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The tag value could not be verified, or the decryption operation otherwise failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ChaCha20Poly1305.Dispose">
      <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
    </member>
    <member name="M:System.Security.Cryptography.ChaCha20Poly1305.Encrypt(System.Byte[],System.Byte[],System.Byte[],System.Byte[],System.Byte[])">
      <summary>Encrypts the plaintext into the ciphertext destination buffer and generates the authentication tag into a separate buffer.</summary>
      <param name="nonce">The nonce associated with this message, which should be a unique value for every operation with the same key.</param>
      <param name="plaintext">The content to encrypt.</param>
      <param name="ciphertext">The byte array to receive the encrypted contents.</param>
      <param name="tag">The byte array to receive the generated authentication tag.</param>
      <param name="associatedData">Extra data associated with this message, which must also be provided during decryption.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="plaintext" /> parameter and the <paramref name="ciphertext" /> do not have the same length.

-or-

The <paramref name="nonce" /> parameter length is not 12 bytes (96 bits).

-or-

The <paramref name="tag" /> parameter length is not 16 bytes (128 bits).</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="nonce" />, <paramref name="ciphertext" />, <paramref name="tag" />, or <paramref name="plaintext" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The encryption operation failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ChaCha20Poly1305.Encrypt(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Span{System.Byte},System.ReadOnlySpan{System.Byte})">
      <summary>Encrypts the plaintext into the ciphertext destination buffer and generates the authentication tag into a separate buffer.</summary>
      <param name="nonce">The nonce associated with this message, which should be a unique value for every operation with the same key.</param>
      <param name="plaintext">The content to encrypt.</param>
      <param name="ciphertext">The byte array to receive the encrypted contents.</param>
      <param name="tag">The byte array to receive the generated authentication tag.</param>
      <param name="associatedData">Extra data associated with this message, which must also be provided during decryption.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="plaintext" /> parameter and the <paramref name="ciphertext" /> do not have the same length.

-or-

The <paramref name="nonce" /> parameter length is not 12 bytes (96 bits).

-or-

The <paramref name="tag" /> parameter length is not 16 bytes (128 bits).</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="nonce" />, <paramref name="ciphertext" />, <paramref name="tag" />, or <paramref name="plaintext" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The encryption operation failed.</exception>
    </member>
    <member name="P:System.Security.Cryptography.ChaCha20Poly1305.IsSupported">
      <summary>Gets a value that indicates whether the algorithm is supported on the current platform.</summary>
      <returns>
        <see langword="true" /> if the algorithm is supported; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.CryptoConfig">
      <summary>Accesses the cryptography configuration information.</summary>
    </member>
    <member name="M:System.Security.Cryptography.CryptoConfig.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.CryptoConfig" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.CryptoConfig.AddAlgorithm(System.Type,System.String[])">
      <summary>Adds a set of names to algorithm mappings to be used for the current application domain.</summary>
      <param name="algorithm">The algorithm to map to.</param>
      <param name="names">An array of names to map to the algorithm.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="algorithm" /> or <paramref name="names" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="algorithm" /> cannot be accessed from outside the assembly.  
  
 -or-  
  
 One of the entries in the <paramref name="names" /> parameter is empty or <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.CryptoConfig.AddOID(System.String,System.String[])">
      <summary>Adds a set of names to object identifier (OID) mappings to be used for the current application domain.</summary>
      <param name="oid">The object identifier (OID) to map to.</param>
      <param name="names">An array of names to map to the OID.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="oid" /> or <paramref name="names" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">One of the entries in the <paramref name="names" /> parameter is empty or <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.CryptoConfig.CreateFromName(System.String)">
      <summary>Creates a new instance of the specified cryptographic object.</summary>
      <param name="name">The simple name of the cryptographic object of which to create an instance.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="name" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Reflection.TargetInvocationException">The algorithm described by the <paramref name="name" /> parameter was used with Federal Information Processing Standards (FIPS) mode enabled, but is not FIPS compatible.</exception>
      <returns>A new instance of the specified cryptographic object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.CryptoConfig.CreateFromName(System.String,System.Object[])">
      <summary>Creates a new instance of the specified cryptographic object with the specified arguments.</summary>
      <param name="name">The simple name of the cryptographic object of which to create an instance.</param>
      <param name="args">The arguments used to create the specified cryptographic object.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="name" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Reflection.TargetInvocationException">The algorithm described by the <paramref name="name" /> parameter was used with Federal Information Processing Standards (FIPS) mode enabled, but is not FIPS compatible.</exception>
      <returns>A new instance of the specified cryptographic object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.CryptoConfig.EncodeOID(System.String)">
      <summary>Encodes the specified object identifier (OID).</summary>
      <param name="str">The OID to encode.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="str" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicUnexpectedOperationException">An error occurred while encoding the OID.</exception>
      <returns>A byte array containing the encoded OID.</returns>
    </member>
    <member name="M:System.Security.Cryptography.CryptoConfig.MapNameToOID(System.String)">
      <summary>Gets the object identifier (OID) of the algorithm corresponding to the specified simple name.</summary>
      <param name="name">The simple name of the algorithm for which to get the OID.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="name" /> parameter is <see langword="null" />.</exception>
      <returns>The OID of the specified algorithm.</returns>
    </member>
    <member name="P:System.Security.Cryptography.CryptoConfig.AllowOnlyFipsAlgorithms">
      <summary>Indicates whether the runtime should enforce the policy to create only Federal Information Processing Standard (FIPS) certified algorithms.</summary>
      <returns>
        <see langword="true" /> to enforce the policy; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.DeriveBytes">
      <summary>Represents the abstract base class from which all classes that derive byte sequences of a specified length inherit.</summary>
    </member>
    <member name="M:System.Security.Cryptography.DeriveBytes.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.DeriveBytes" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.DeriveBytes.Dispose">
      <summary>When overridden in a derived class, releases all resources used by the current instance of the <see cref="T:System.Security.Cryptography.DeriveBytes" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.DeriveBytes.Dispose(System.Boolean)">
      <summary>When overridden in a derived class, releases the unmanaged resources used by the <see cref="T:System.Security.Cryptography.DeriveBytes" /> class and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Security.Cryptography.DeriveBytes.GetBytes(System.Int32)">
      <summary>When overridden in a derived class, returns pseudo-random key bytes.</summary>
      <param name="cb">The number of pseudo-random key bytes to generate.</param>
      <returns>A byte array filled with pseudo-random key bytes.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DeriveBytes.Reset">
      <summary>When overridden in a derived class, resets the state of the operation.</summary>
    </member>
    <member name="T:System.Security.Cryptography.DES">
      <summary>Represents the base class for the Data Encryption Standard (DES) algorithm from which all <see cref="T:System.Security.Cryptography.DES" /> implementations must derive.</summary>
    </member>
    <member name="M:System.Security.Cryptography.DES.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.DES" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.DES.Create">
      <summary>Creates an instance of a cryptographic object to perform the Data Encryption Standard (<see cref="T:System.Security.Cryptography.DES" />) algorithm.</summary>
      <returns>A cryptographic object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DES.Create(System.String)">
      <summary>Creates an instance of a cryptographic object to perform the specified implementation of the Data Encryption Standard (<see cref="T:System.Security.Cryptography.DES" />) algorithm.</summary>
      <param name="algName">The name of the specific implementation of <see cref="T:System.Security.Cryptography.DES" /> to use.</param>
      <returns>A cryptographic object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DES.IsSemiWeakKey(System.Byte[])">
      <summary>Determines whether the specified key is semi-weak.</summary>
      <param name="rgbKey">The secret key to test for semi-weakness.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The size of the <paramref name="rgbKey" /> parameter is not valid.</exception>
      <returns>
        <see langword="true" /> if the key is semi-weak; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DES.IsWeakKey(System.Byte[])">
      <summary>Determines whether the specified key is weak.</summary>
      <param name="rgbKey">The secret key to test for weakness.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The size of the <paramref name="rgbKey" /> parameter is not valid.</exception>
      <returns>
        <see langword="true" /> if the key is weak; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.DES.Key">
      <summary>Gets or sets the secret key for the Data Encryption Standard (<see cref="T:System.Security.Cryptography.DES" />) algorithm.</summary>
      <exception cref="T:System.ArgumentNullException">An attempt was made to set the key to <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">An attempt was made to set a key whose length is not equal to <see cref="F:System.Security.Cryptography.SymmetricAlgorithm.BlockSizeValue" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An attempt was made to set a weak key (see <see cref="M:System.Security.Cryptography.DES.IsWeakKey(System.Byte[])" />) or a semi-weak key (see <see cref="M:System.Security.Cryptography.DES.IsSemiWeakKey(System.Byte[])" />).</exception>
      <returns>The secret key for the <see cref="T:System.Security.Cryptography.DES" /> algorithm.</returns>
    </member>
    <member name="T:System.Security.Cryptography.DSA">
      <summary>Represents the abstract base class from which all implementations of the Digital Signature Algorithm (<see cref="T:System.Security.Cryptography.DSA" />) must inherit.</summary>
    </member>
    <member name="M:System.Security.Cryptography.DSA.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.DSA" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.DSA.Create">
      <summary>Creates the default cryptographic object used to perform the asymmetric algorithm.</summary>
      <returns>A cryptographic object used to perform the asymmetric algorithm.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.Create(System.Int32)">
      <summary>Creates a new ephemeral DSA key with the specified key size.</summary>
      <param name="keySizeInBits">The key size, in bits.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="keySizeInBits" /> is not permitted by <see cref="P:System.Security.Cryptography.AsymmetricAlgorithm.LegalKeySizes" />.</exception>
      <returns>A new ephemeral DSA key with the specified key size.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.Create(System.Security.Cryptography.DSAParameters)">
      <summary>Creates a new ephemeral DSA key with the specified DSA key parameters.</summary>
      <param name="parameters">The parameters for the <see cref="T:System.Security.Cryptography.DSA" /> algorithm.</param>
      <returns>A new ephemeral DSA key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.Create(System.String)">
      <summary>Creates the specified cryptographic object used to perform the asymmetric algorithm.</summary>
      <param name="algName">The name of the specific implementation of <see cref="T:System.Security.Cryptography.DSA" /> to use.</param>
      <returns>A cryptographic object used to perform the asymmetric algorithm.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.CreateSignature(System.Byte[])">
      <summary>When overridden in a derived class, creates the <see cref="T:System.Security.Cryptography.DSA" /> signature for the specified hash value.</summary>
      <param name="rgbHash">The hash value to be signed.</param>
      <returns>The digital signature for the specified hash value.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.CreateSignature(System.Byte[],System.Security.Cryptography.DSASignatureFormat)">
      <summary>Creates the DSA signature for the specified hash value in the indicated format.</summary>
      <param name="rgbHash">The hash value to sign.</param>
      <param name="signatureFormat">The encoding format to use for the signature.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rgbHash" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the signing operation.</exception>
      <returns>The DSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.CreateSignatureCore(System.ReadOnlySpan{System.Byte},System.Security.Cryptography.DSASignatureFormat)">
      <summary>Creates the DSA signature for the specified hash value in the indicated format.</summary>
      <param name="hash">The hash value to sign.</param>
      <param name="signatureFormat">The encoding format to use for the signature.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the signing operation.</exception>
      <returns>The DSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.ExportParameters(System.Boolean)">
      <summary>When overridden in a derived class, exports the <see cref="T:System.Security.Cryptography.DSAParameters" />.</summary>
      <param name="includePrivateParameters">
        <see langword="true" /> to include private parameters; otherwise, <see langword="false" />.</param>
      <returns>The parameters for <see cref="T:System.Security.Cryptography.DSA" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.FromXmlString(System.String)">
      <summary>Reconstructs a <see cref="T:System.Security.Cryptography.DSA" /> object from an XML string.</summary>
      <param name="xmlString">The XML string to use to reconstruct the <see cref="T:System.Security.Cryptography.DSA" /> object.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="xmlString" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The format of the <paramref name="xmlString" /> parameter is not valid.</exception>
    </member>
    <member name="M:System.Security.Cryptography.DSA.GetMaxSignatureSize(System.Security.Cryptography.DSASignatureFormat)">
      <summary>Gets the largest size, in bytes, for a signature produced by this key in the indicated format.</summary>
      <param name="signatureFormat">The encoding format for a signature.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.</exception>
      <returns>The largest size, in bytes, for a signature produced by this key in the indicated format.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.HashData(System.Byte[],System.Int32,System.Int32,System.Security.Cryptography.HashAlgorithmName)">
      <summary>When overridden in a derived class, computes the hash value of a specified portion of a byte array by using a specified hashing algorithm.</summary>
      <param name="data">The data to be hashed.</param>
      <param name="offset">The index of the first byte in <paramref name="data" /> that is to be hashed.</param>
      <param name="count">The number of bytes to hash.</param>
      <param name="hashAlgorithm">The algorithm to use to hash the data.</param>
      <exception cref="T:System.NotImplementedException">A derived class must override this method.</exception>
      <returns>The hashed data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.HashData(System.IO.Stream,System.Security.Cryptography.HashAlgorithmName)">
      <summary>When overridden in a derived class, computes the hash value of a specified binary stream by using a specified hashing algorithm.</summary>
      <param name="data">The binary stream to hash.</param>
      <param name="hashAlgorithm">The algorithm to use to hash the data.</param>
      <exception cref="T:System.NotImplementedException">A derived class must override this method.</exception>
      <returns>The hashed data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.ImportEncryptedPkcs8PrivateKey(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Imports the public/private keypair from a PKCS#8 EncryptedPrivateKeyInfo structure after decrypting with a byte-based password, replacing the keys for this object.</summary>
      <param name="passwordBytes">The bytes to use as a password when decrypting the key material.</param>
      <param name="source">The bytes of a PKCS#8 EncryptedPrivateKeyInfo structure in the ASN.1-BER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The password is incorrect.

-or-

The contents of <paramref name="source" /> indicate the Key Derivation Function (KDF) to apply is the legacy PKCS#12 KDF, which requires <see cref="T:System.Char" />-based passwords.

-or-

The contents of <paramref name="source" /> do not represent an ASN.1-BER-encoded PKCS#8 EncryptedPrivateKeyInfo structure.

-or-

The contents of <paramref name="source" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

-or-

The contents of <paramref name="source" /> represent the key in a format that is not supported.

-or-

The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.DSA.ImportEncryptedPkcs8PrivateKey(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Imports the public/private keypair from a PKCS#8 EncryptedPrivateKeyInfo structure after decrypting with a char-based password, replacing the keys for this object.</summary>
      <param name="password">The password to use for decrypting the key material.</param>
      <param name="source">The bytes of a PKCS#8 EncryptedPrivateKeyInfo structure in the ASN.1-BER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The password is incorrect.

-or-

The contents of <paramref name="source" /> do not represent an ASN.1-BER-encoded PKCS#8 EncryptedPrivateKeyInfo structure.

-or-

The contents of <paramref name="source" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

-or-

The contents of <paramref name="source" /> represent the key in a format that is not supported.

-or-

The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.DSA.ImportFromEncryptedPem(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Byte})">
      <summary>Imports an encrypted RFC 7468 PEM-encoded private key, replacing the keys for this object.</summary>
      <param name="input">The PEM text of the encrypted key to import.</param>
      <param name="passwordBytes">The bytes to use as a password when decrypting the key material.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="input" /> does not contain a PEM-encoded key with a recognized label.

            -or-

            <paramref name="input" /> contains multiple PEM-encoded keys with a recognized label.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The password is incorrect.

            -or-

            The base-64 decoded contents of the PEM text from <paramref name="input" /> do not represent an ASN.1-BER-encoded PKCS#8 EncryptedPrivateKeyInfo structure.

            -or-

            The base-64 decoded contents of the PEM text from <paramref name="input" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

            -or-

            The base-64 decoded contents of the PEM text from <paramref name="input" /> represent the key in a format that is not supported.

            -or-

            The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.DSA.ImportFromEncryptedPem(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char})">
      <summary>Imports an encrypted RFC 7468 PEM-encoded private key, replacing the keys for this object.</summary>
      <param name="input">The PEM text of the encrypted key to import.</param>
      <param name="password">The password to use for decrypting the key material.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="input" /> does not contain a PEM-encoded key with a recognized label.

            -or-

            <paramref name="input" /> contains multiple PEM-encoded keys with a recognized label.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The password is incorrect.

            -or-

            The base-64 decoded contents of the PEM text from <paramref name="input" /> do not represent an ASN.1-BER-encoded PKCS#8 EncryptedPrivateKeyInfo structure.

            -or-

            The base-64 decoded contents of the PEM text from <paramref name="input" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

            -or-

            The base-64 decoded contents of the PEM text from <paramref name="input" /> represent the key in a format that is not supported.

            -or-

            The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.DSA.ImportFromPem(System.ReadOnlySpan{System.Char})">
      <summary>Imports an RFC 7468 PEM-encoded key, replacing the keys for this object.</summary>
      <param name="input">The PEM text of the key to import.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="input" /> does not contain a PEM-encoded key with a recognized label.

            -or-

            <paramref name="input" /> contains multiple PEM-encoded keys with a recognized label.

            -or-

            <paramref name="input" /> contains an encrypted PEM-encoded key.</exception>
    </member>
    <member name="M:System.Security.Cryptography.DSA.ImportParameters(System.Security.Cryptography.DSAParameters)">
      <summary>When overridden in a derived class, imports the specified <see cref="T:System.Security.Cryptography.DSAParameters" />.</summary>
      <param name="parameters">The parameters for <see cref="T:System.Security.Cryptography.DSA" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="parameters" /> is missing required fields.

-or-

<paramref name="parameters" /> has fields with inconsistent lengths for a valid key.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="parameters" /> does not represent a valid DSA key parameter set.</exception>
    </member>
    <member name="M:System.Security.Cryptography.DSA.ImportPkcs8PrivateKey(System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Imports the public/private keypair from a PKCS#8 PrivateKeyInfo structure after decryption, replacing the keys for this object.</summary>
      <param name="source">The bytes of a PKCS#8 PrivateKeyInfo structure in the ASN.1-BER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The contents of <paramref name="source" /> do not represent an ASN.1-BER-encoded PKCS#8 PrivateKeyInfo structure.

-or-

The contents of <paramref name="source" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

-or-

The contents of <paramref name="source" /> represent the key in a format that is not supported.

-or-

The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.DSA.ImportSubjectPublicKeyInfo(System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Imports the public key from an X.509 SubjectPublicKeyInfo structure after decryption, replacing the keys for this object.</summary>
      <param name="source">The bytes of an X.509 SubjectPublicKeyInfo structure in the ASN.1-DER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The contents of <paramref name="source" /> do not represent an ASN.1-DER-encoded X.509 SubjectPublicKeyInfo structure.

-or-

The contents of <paramref name="source" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

-or-

The contents of <paramref name="source" /> represent the key in a format that is not supported.

-or-

The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.DSA.SignData(System.Byte[],System.Int32,System.Int32,System.Security.Cryptography.HashAlgorithmName)">
      <summary>Computes the hash value of a portion of the specified byte array using the specified hash algorithm and signs the resulting hash value.</summary>
      <param name="data">The input data for which to compute the hash.</param>
      <param name="offset">The offset into the array at which to begin using data.</param>
      <param name="count">The number of bytes in the array to use as data.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than zero.  
  
 -or-  
  
 <paramref name="count" /> is less than zero.  
  
 -or-  
  
 <paramref name="offset" /> + <paramref name="count" /> - 1 results in an index that is beyond the upper bound of <paramref name="data" />.</exception>
      <returns>The DSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.SignData(System.Byte[],System.Int32,System.Int32,System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat)">
      <summary>Computes the hash value of the specified data and signs it using the specified signature format.</summary>
      <param name="data">The data to sign.</param>
      <param name="offset">The offset into <paramref name="data" /> at which to begin hashing.</param>
      <param name="count">The number of bytes to read from <paramref name="data" />.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <param name="signatureFormat">The encoding format to use for the signature.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.
 
          -or- 
          <paramref name="offset" /> is less than zero.
 
          -or- 
          <paramref name="count" /> is less than zero.
 
          -or- 
          <paramref name="offset" /> + <paramref name="count" /> - 1 results in an index that is beyond the upper bound of <paramref name="data" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" /> has a <see langword="null" /> or empty <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the hashing or signing operation.</exception>
      <returns>The DSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.SignData(System.Byte[],System.Security.Cryptography.HashAlgorithmName)">
      <summary>Computes the hash value of the specified byte array using the specified hash algorithm and signs the resulting hash value.</summary>
      <param name="data">The input data for which to compute the hash.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <returns>The DSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.SignData(System.Byte[],System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat)">
      <summary>Computes the hash value of the specified data and signs it using the specified signature format.</summary>
      <param name="data">The data to sign.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <param name="signatureFormat">The encoding format to use for the signature.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" /> has a <see langword="null" /> or empty <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the hashing or signing operation.</exception>
      <returns>The DSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.SignData(System.IO.Stream,System.Security.Cryptography.HashAlgorithmName)">
      <summary>Computes the hash value of the specified stream using the specified hash algorithm and signs the resulting hash value.</summary>
      <param name="data">The input stream for which to compute the hash.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <returns>The DSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.SignData(System.IO.Stream,System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat)">
      <summary>Computes the hash value of the specified data and signs it using the specified signature format.</summary>
      <param name="data">The data to sign.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <param name="signatureFormat">The encoding format to use for the signature.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" /> has a <see langword="null" /> or empty <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the hashing or signing operation.</exception>
      <returns>The DSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.SignDataCore(System.IO.Stream,System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat)">
      <summary>Computes the hash value of the specified data and signs it using the specified signature format.</summary>
      <param name="data">The data to sign.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <param name="signatureFormat">The encoding format to use for the signature.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the hashing or signing operation.</exception>
      <returns>The DSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.SignDataCore(System.ReadOnlySpan{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat)">
      <summary>Computes the hash value of the specified data and signs it using the specified signature format.</summary>
      <param name="data">The data to sign.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <param name="signatureFormat">The encoding format to use for the signature.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the hashing or signing operation.</exception>
      <returns>The DSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.ToXmlString(System.Boolean)">
      <summary>Creates and returns an XML string representation of the current <see cref="T:System.Security.Cryptography.DSA" /> object.</summary>
      <param name="includePrivateParameters">
        <see langword="true" /> to include private parameters; otherwise, <see langword="false" />.</param>
      <returns>An XML string encoding of the current <see cref="T:System.Security.Cryptography.DSA" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.TryCreateSignature(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to create the DSA signature for the specified hash into the provided buffer.</summary>
      <param name="hash">The hash to sign.</param>
      <param name="destination">The byte span to receive the signature.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">This instance represents only a public key.

-or-

The implementation type only supports legacy DSA (FIPS 186-2), and <paramref name="hash" /> is not a 20-byte value.

-or-

Creating the signature otherwise failed.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is large enough to receive the result; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.TryCreateSignature(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Security.Cryptography.DSASignatureFormat,System.Int32@)">
      <summary>Attempts to create the DSA signature for the specified hash value in the indicated format and place it into the provided buffer.</summary>
      <param name="hash">The hash value to sign.</param>
      <param name="destination">The buffer to receive the signature.</param>
      <param name="signatureFormat">The encoding format to use for the signature.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the signing operation.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is large enough to receive the signature; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.TryCreateSignatureCore(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Security.Cryptography.DSASignatureFormat,System.Int32@)">
      <summary>Attempts to create the DSA signature for the specified hash value in the indicated format and place it into the provided buffer.</summary>
      <param name="hash">The hash value to sign.</param>
      <param name="destination">The buffer to receive the signature.</param>
      <param name="signatureFormat">The encoding format to use for the signature.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the signing operation.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is large enough to receive the signature; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.TryExportEncryptedPkcs8PrivateKey(System.ReadOnlySpan{System.Byte},System.Security.Cryptography.PbeParameters,System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to export the current key in the PKCS#8 EncryptedPrivateKeyInfo format into a provided buffer, using a byte-based password.</summary>
      <param name="passwordBytes">The bytes to use as a password when encrypting the key material.</param>
      <param name="pbeParameters">The password-based encryption (PBE) parameters to use when encrypting the key material.</param>
      <param name="destination">The byte span to receive the PKCS#8 EncryptedPrivateKeyInfo data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.

-or-

<paramref name="pbeParameters" /> indicates that <see cref="F:System.Security.Cryptography.PbeEncryptionAlgorithm.TripleDes3KeyPkcs12" /> should be used, which requires <see cref="T:System.Char" />-based passwords.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.TryExportEncryptedPkcs8PrivateKey(System.ReadOnlySpan{System.Char},System.Security.Cryptography.PbeParameters,System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to export the current key in the PKCS#8 EncryptedPrivateKeyInfo format into a provided buffer, using a char-based password.</summary>
      <param name="password">The password to use when encrypting the key material.</param>
      <param name="pbeParameters">The password-based encryption (PBE) parameters to use when encrypting the key material.</param>
      <param name="destination">The byte span to receive the PKCS#8 EncryptedPrivateKeyInfo data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.TryExportPkcs8PrivateKey(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to export the current key in the PKCS#8 PrivateKeyInfo format into a provided buffer.</summary>
      <param name="destination">The byte span to receive the PKCS#8 PrivateKeyInfo data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.TryExportSubjectPublicKeyInfo(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to export the current key in the X.509 SubjectPublicKeyInfo format into a provided buffer.</summary>
      <param name="destination">The byte span to receive the X.509 SubjectPublicKeyInfo data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.TryHashData(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Int32@)">
      <summary>Attempts to compute the hash value of the provided data into a provided buffer.</summary>
      <param name="data">The data to be hashed.</param>
      <param name="destination">The byte span to receive the hash value.</param>
      <param name="hashAlgorithm">The name of the hash algorithm to use.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is large enough to receive the result; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.TrySignData(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Int32@)">
      <summary>Attempts to create the DSA signature for the specified data into the provided buffer.</summary>
      <param name="data">The data to hash and sign.</param>
      <param name="destination">The byte span to receive the signature.</param>
      <param name="hashAlgorithm">The name of the hash algorithm to use.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />.</param>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is large enough to receive the result; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.TrySignData(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat,System.Int32@)">
      <summary>Attempts to create the DSA signature for the specified data in the indicated format and place it into the provided buffer.</summary>
      <param name="data">The data to hash and sign.</param>
      <param name="destination">The buffer to receive the signature.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <param name="signatureFormat">The encoding format to use for the signature.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" /> has a <see langword="null" /> or empty <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the signing operation.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the signature; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.TrySignDataCore(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat,System.Int32@)">
      <summary>Attempts to create the DSA signature for the specified data in the indicated format and place it into the provided buffer.</summary>
      <param name="data">The data to hash and sign.</param>
      <param name="destination">The buffer to receive the signature.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <param name="signatureFormat">The encoding format to use for the signature.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the signing operation.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the signature; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.VerifyData(System.Byte[],System.Byte[],System.Security.Cryptography.HashAlgorithmName)">
      <summary>Verifies that a digital signature is valid by calculating the hash value of the specified data using the specified hash algorithm and comparing it to the provided signature.</summary>
      <param name="data">The signed data.</param>
      <param name="signature">The signature data to be verified.</param>
      <param name="hashAlgorithm">The hash algorithm used to create the hash value of the data.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="signature" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The implementation type only supports legacy DSA (FIPS 186-2), and the hash algorithm is not SHA-1.

-or-

Verifying the signature otherwise failed.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.VerifyData(System.Byte[],System.Byte[],System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat)">
      <summary>Verifies that a digital signature is valid for the provided data.</summary>
      <param name="data">The signed data.</param>
      <param name="signature">The signature to verify.</param>
      <param name="hashAlgorithm">The hash algorithm used to hash the data for the verification process.</param>
      <param name="signatureFormat">The encoding format for <paramref name="signature" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> or <paramref name="signature" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" /> has a <see langword="null" /> or empty <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the hashing or verification operation.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid for the provided data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.VerifyData(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Security.Cryptography.HashAlgorithmName)">
      <summary>Verifies that a digital signature is valid by calculating the hash value of the data in a portion of a byte array using the specified hash algorithm and comparing it to the provided signature.</summary>
      <param name="data">The signed data.</param>
      <param name="offset">The starting index at which to compute the hash.</param>
      <param name="count">The number of bytes to hash.</param>
      <param name="signature">The signature data to be verified.</param>
      <param name="hashAlgorithm">The hash algorithm used to create the hash value of the data.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="signature" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than zero.  
  
 -or-  
  
 <paramref name="count" /> is less than zero.  
  
 -or-  
  
 <paramref name="offset" /> + <paramref name="count" /> - 1 results in an index that is beyond the upper bound of <paramref name="data" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The implementation type only supports legacy DSA (FIPS 186-2), and the hash algorithm is not SHA-1.

-or-

Verifying the signature otherwise failed.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.VerifyData(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat)">
      <summary>Verifies that a digital signature is valid for the provided data.</summary>
      <param name="data">An array that contains the signed data.</param>
      <param name="offset">The starting index of the signed portion of <paramref name="data" />.</param>
      <param name="count">The number of bytes in <paramref name="data" /> that were signed.</param>
      <param name="signature">The signature to verify.</param>
      <param name="hashAlgorithm">The hash algorithm used to hash the data for the verification process.</param>
      <param name="signatureFormat">The encoding format for <paramref name="signature" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> or <paramref name="signature" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.
 
 -or- 
 <paramref name="offset" /> is less than zero.
 
 -or- 
 <paramref name="count" /> is less than zero.
 
 -or- 
 <paramref name="offset" /> + <paramref name="count" /> - 1 results in an index that is beyond the upper bound of <paramref name="data" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" /> has a <see langword="null" /> or empty <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the hashing or verification operation.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid for the provided data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.VerifyData(System.IO.Stream,System.Byte[],System.Security.Cryptography.HashAlgorithmName)">
      <summary>Verifies that a digital signature is valid by calculating the hash value of the specified stream using the specified hash algorithm and comparing it to the provided signature.</summary>
      <param name="data">The signed data.</param>
      <param name="signature">The signature data to be verified.</param>
      <param name="hashAlgorithm">The hash algorithm used to create the hash value of the data.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="signature" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The implementation type only supports legacy DSA (FIPS 186-2), and the hash algorithm is not SHA-1.

-or-

Verifying the signature otherwise failed.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.VerifyData(System.IO.Stream,System.Byte[],System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat)">
      <summary>Verifies that a digital signature is valid for the provided data.</summary>
      <param name="data">The signed data.</param>
      <param name="signature">The signature to verify.</param>
      <param name="hashAlgorithm">The hash algorithm used to hash the data for the verification process.</param>
      <param name="signatureFormat">The encoding format for <paramref name="signature" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> or <paramref name="signature" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" /> has a <see langword="null" /> or empty <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the hashing or verification operation.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid for the provided data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.VerifyData(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Security.Cryptography.HashAlgorithmName)">
      <summary>Verifies that a digital signature is valid by calculating the hash value of the data in a byte span using the specified hash algorithm and comparing it to the provided signature.</summary>
      <param name="data">The signed data.</param>
      <param name="signature">The signature to be verified.</param>
      <param name="hashAlgorithm">The hash algorithm used to create the hash value of the data.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The implementation type only supports legacy DSA (FIPS 186-2), and the hash algorithm is not SHA-1.

-or-

Verifying the signature otherwise failed.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.VerifyData(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat)">
      <summary>Verifies that a digital signature is valid for the provided data.</summary>
      <param name="data">The signed data.</param>
      <param name="signature">The signature to verify.</param>
      <param name="hashAlgorithm">The hash algorithm used to hash the data for the verification process.</param>
      <param name="signatureFormat">The encoding format for <paramref name="signature" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the hashing or verification operation.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid for the provided data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.VerifyDataCore(System.IO.Stream,System.ReadOnlySpan{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat)">
      <summary>Verifies that a digital signature is valid for the provided data.</summary>
      <param name="data">The signed data.</param>
      <param name="signature">The signature to verify.</param>
      <param name="hashAlgorithm">The hash algorithm used to hash the data for the verification process.</param>
      <param name="signatureFormat">The encoding format for <paramref name="signature" />.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the hashing or verification operation.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid for the provided data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.VerifyDataCore(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat)">
      <summary>Verifies that a digital signature is valid for the provided data.</summary>
      <param name="data">The signed data.</param>
      <param name="signature">The signature to verify.</param>
      <param name="hashAlgorithm">The hash algorithm used to hash the data for the verification process.</param>
      <param name="signatureFormat">The encoding format for <paramref name="signature" />.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the hashing or verification operation.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid for the provided data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.VerifySignature(System.Byte[],System.Byte[])">
      <summary>When overridden in a derived class, verifies the <see cref="T:System.Security.Cryptography.DSA" /> signature for the specified data.</summary>
      <param name="rgbHash">The hash of the data signed with <paramref name="rgbSignature" />.</param>
      <param name="rgbSignature">The signature to be verified for <c>rgbData</c>.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The implementation type only supports legacy DSA (FIPS 186-2), and the hash value is not 20 bytes long.

-or-

Verifying the signature otherwise failed.</exception>
      <returns>
        <see langword="true" /> if <paramref name="rgbSignature" /> matches the signature computed using the specified hash algorithm and key on <paramref name="rgbHash" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.VerifySignature(System.Byte[],System.Byte[],System.Security.Cryptography.DSASignatureFormat)">
      <summary>Verifies that a digital signature is valid for the provided hash.</summary>
      <param name="rgbHash">The signed hash.</param>
      <param name="rgbSignature">The signature to verify.</param>
      <param name="signatureFormat">The encoding format for <paramref name="rgbSignature" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rgbHash" /> or <paramref name="rgbSignature" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the verification operation.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid for the provided data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.VerifySignature(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte})">
      <summary>Verifies that a digital signature is valid for a provided data hash.</summary>
      <param name="hash">The data hash to verify.</param>
      <param name="signature">The signature to be verify.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The implementation type only supports legacy DSA (FIPS 186-2), and the hash value is not 20 bytes long.

-or-

Verifying the signature otherwise failed.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid for the hash; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.VerifySignature(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Security.Cryptography.DSASignatureFormat)">
      <summary>Verifies that a digital signature is valid for the provided hash.</summary>
      <param name="hash">The signed hash.</param>
      <param name="signature">The signature to verify.</param>
      <param name="signatureFormat">The encoding format for <paramref name="signature" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the verification operation.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid for the provided data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSA.VerifySignatureCore(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Security.Cryptography.DSASignatureFormat)">
      <summary>Verifies that a digital signature is valid for the provided hash.</summary>
      <param name="hash">The signed hash.</param>
      <param name="signature">The signature to verify.</param>
      <param name="signatureFormat">The encoding format for <paramref name="signature" />.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the verification operation.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid for the provided data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.DSAParameters">
      <summary>Contains the typical parameters for the <see cref="T:System.Security.Cryptography.DSA" /> algorithm.</summary>
    </member>
    <member name="F:System.Security.Cryptography.DSAParameters.Counter">
      <summary>Specifies the counter for the <see cref="T:System.Security.Cryptography.DSA" /> algorithm.</summary>
    </member>
    <member name="F:System.Security.Cryptography.DSAParameters.G">
      <summary>Specifies the <see langword="G" /> parameter for the <see cref="T:System.Security.Cryptography.DSA" /> algorithm.</summary>
    </member>
    <member name="F:System.Security.Cryptography.DSAParameters.J">
      <summary>Specifies the <see langword="J" /> parameter for the <see cref="T:System.Security.Cryptography.DSA" /> algorithm.</summary>
    </member>
    <member name="F:System.Security.Cryptography.DSAParameters.P">
      <summary>Specifies the <see langword="P" /> parameter for the <see cref="T:System.Security.Cryptography.DSA" /> algorithm.</summary>
    </member>
    <member name="F:System.Security.Cryptography.DSAParameters.Q">
      <summary>Specifies the <see langword="Q" /> parameter for the <see cref="T:System.Security.Cryptography.DSA" /> algorithm.</summary>
    </member>
    <member name="F:System.Security.Cryptography.DSAParameters.Seed">
      <summary>Specifies the seed for the <see cref="T:System.Security.Cryptography.DSA" /> algorithm.</summary>
    </member>
    <member name="F:System.Security.Cryptography.DSAParameters.X">
      <summary>Specifies the <see langword="X" /> parameter for the <see cref="T:System.Security.Cryptography.DSA" /> algorithm.</summary>
    </member>
    <member name="F:System.Security.Cryptography.DSAParameters.Y">
      <summary>Specifies the <see langword="Y" /> parameter for the <see cref="T:System.Security.Cryptography.DSA" /> algorithm.</summary>
    </member>
    <member name="T:System.Security.Cryptography.DSASignatureDeformatter">
      <summary>Verifies a Digital Signature Algorithm (<see cref="T:System.Security.Cryptography.DSA" />) PKCS#1 v1.5 signature.</summary>
    </member>
    <member name="M:System.Security.Cryptography.DSASignatureDeformatter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.DSASignatureDeformatter" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.DSASignatureDeformatter.#ctor(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.DSASignatureDeformatter" /> class with the specified key.</summary>
      <param name="key">The instance of Digital Signature Algorithm (<see cref="T:System.Security.Cryptography.DSA" />) that holds the key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.DSASignatureDeformatter.SetHashAlgorithm(System.String)">
      <summary>Specifies the hash algorithm for the Digital Signature Algorithm (<see cref="T:System.Security.Cryptography.DSA" />) signature deformatter.</summary>
      <param name="strName">The name of the hash algorithm to use for the signature deformatter.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicUnexpectedOperationException">The <paramref name="strName" /> parameter does not map to the <see cref="T:System.Security.Cryptography.SHA1" /> hash algorithm.</exception>
    </member>
    <member name="M:System.Security.Cryptography.DSASignatureDeformatter.SetKey(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Specifies the key to be used for the Digital Signature Algorithm (<see cref="T:System.Security.Cryptography.DSA" />) signature deformatter.</summary>
      <param name="key">The instance of <see cref="T:System.Security.Cryptography.DSA" /> that holds the key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.DSASignatureDeformatter.VerifySignature(System.Byte[],System.Byte[])">
      <summary>Verifies the Digital Signature Algorithm (<see cref="T:System.Security.Cryptography.DSA" />) signature on the data.</summary>
      <param name="rgbHash">The data signed with <paramref name="rgbSignature" />.</param>
      <param name="rgbSignature">The signature to be verified for <paramref name="rgbHash" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rgbHash" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="rgbSignature" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicUnexpectedOperationException">The DSA key is missing.</exception>
      <returns>
        <see langword="true" /> if the signature is valid for the data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.DSASignatureFormat">
      <summary>Specifies the data format for signatures with the DSA family of algorithms.</summary>
    </member>
    <member name="F:System.Security.Cryptography.DSASignatureFormat.IeeeP1363FixedFieldConcatenation">
      <summary>The signature format from IEEE P1363, which produces a fixed-size signature for a given key.</summary>
    </member>
    <member name="F:System.Security.Cryptography.DSASignatureFormat.Rfc3279DerSequence">
      <summary>The signature format from IETF RFC 3279, which produces a variably-sized signature.</summary>
    </member>
    <member name="T:System.Security.Cryptography.DSASignatureFormatter">
      <summary>Creates a Digital Signature Algorithm (<see cref="T:System.Security.Cryptography.DSA" />) signature.</summary>
    </member>
    <member name="M:System.Security.Cryptography.DSASignatureFormatter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.DSASignatureFormatter" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.DSASignatureFormatter.#ctor(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.DSASignatureFormatter" /> class with the specified key.</summary>
      <param name="key">The instance of the Digital Signature Algorithm (<see cref="T:System.Security.Cryptography.DSA" />) that holds the key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.DSASignatureFormatter.CreateSignature(System.Byte[])">
      <summary>Creates the Digital Signature Algorithm (<see cref="T:System.Security.Cryptography.DSA" />) PKCS #1 signature for the specified data.</summary>
      <param name="rgbHash">The data to be signed.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="rgbHash" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicUnexpectedOperationException">The OID is <see langword="null" />.  
  
 -or-  
  
 The DSA key is <see langword="null" />.</exception>
      <returns>The digital signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.DSASignatureFormatter.SetHashAlgorithm(System.String)">
      <summary>Specifies the hash algorithm for the Digital Signature Algorithm (<see cref="T:System.Security.Cryptography.DSA" />) signature formatter.</summary>
      <param name="strName">The name of the hash algorithm to use for the signature formatter.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicUnexpectedOperationException">The <paramref name="strName" /> parameter does not map to the <see cref="T:System.Security.Cryptography.SHA1" /> hash algorithm.</exception>
    </member>
    <member name="M:System.Security.Cryptography.DSASignatureFormatter.SetKey(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Specifies the key to be used for the Digital Signature Algorithm (<see cref="T:System.Security.Cryptography.DSA" />) signature formatter.</summary>
      <param name="key">The instance of <see cref="T:System.Security.Cryptography.DSA" /> that holds the key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
    </member>
    <member name="T:System.Security.Cryptography.ECCurve">
      <summary>Represents an elliptic curve.</summary>
    </member>
    <member name="F:System.Security.Cryptography.ECCurve.A">
      <summary>The first coefficient for an explicit curve. <c>A</c> for short Weierstrass, Montgomery, and Twisted Edwards curves.</summary>
    </member>
    <member name="F:System.Security.Cryptography.ECCurve.B">
      <summary>The second coefficient for an explicit curve. <c>B</c> for short Weierstrass and <c>d</c> for Twisted Edwards curves.</summary>
    </member>
    <member name="F:System.Security.Cryptography.ECCurve.Cofactor">
      <summary>The cofactor of the curve.</summary>
    </member>
    <member name="F:System.Security.Cryptography.ECCurve.CurveType">
      <summary>Identifies the composition of the <see cref="T:System.Security.Cryptography.ECCurve" /> object.</summary>
    </member>
    <member name="F:System.Security.Cryptography.ECCurve.G">
      <summary>The generator, or base point, for operations on the curve.</summary>
    </member>
    <member name="F:System.Security.Cryptography.ECCurve.Hash">
      <summary>The name of the hash algorithm which was used to generate the curve coefficients (<see cref="F:System.Security.Cryptography.ECCurve.A" /> and <see cref="F:System.Security.Cryptography.ECCurve.B" />) from the <see cref="F:System.Security.Cryptography.ECCurve.Seed" /> under the ANSI X9.62 generation algorithm. Applies only to explicit curves.</summary>
    </member>
    <member name="F:System.Security.Cryptography.ECCurve.Order">
      <summary>The order of the curve. Applies only to explicit curves.</summary>
    </member>
    <member name="F:System.Security.Cryptography.ECCurve.Polynomial">
      <summary>The curve polynomial. Applies only to characteristic 2 curves.</summary>
    </member>
    <member name="F:System.Security.Cryptography.ECCurve.Prime">
      <summary>The prime specifying the base field. Applies only to prime curves.</summary>
    </member>
    <member name="F:System.Security.Cryptography.ECCurve.Seed">
      <summary>The seed value for coefficient generation under the ANSI X9.62 generation algorithm. Applies only to explicit curves.</summary>
    </member>
    <member name="M:System.Security.Cryptography.ECCurve.CreateFromFriendlyName(System.String)">
      <summary>Creates a named curve using the specified friendly name of the identifier.</summary>
      <param name="oidFriendlyName">The friendly name of the identifier.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="oidFriendlyName" /> is <see langword="null" />.</exception>
      <returns>An object representing the named curve.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECCurve.CreateFromOid(System.Security.Cryptography.Oid)">
      <summary>Creates a named curve using the specified <see cref="T:System.Security.Cryptography.Oid" /> object.</summary>
      <param name="curveOid">The object identifier to use.</param>
      <returns>An object representing the named curve.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECCurve.CreateFromValue(System.String)">
      <summary>Creates a named curve using the specified dotted-decimal representation of the identifier.</summary>
      <param name="oidValue">The dotted number of the identifier.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="oidValue" /> is <see langword="null" />.</exception>
      <returns>An object representing the named curve.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECCurve.Validate">
      <summary>Validates the integrity of the current curve. Throws a <see cref="T:System.Security.Cryptography.CryptographicException" /> exception if the structure is not valid.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The curve parameters are not valid for the current curve type.</exception>
    </member>
    <member name="P:System.Security.Cryptography.ECCurve.IsCharacteristic2">
      <summary>Gets a value that indicates whether the curve type indicates an explicit characteristic 2 curve.</summary>
      <returns>
        <see langword="true" /> if the curve is an explicit characteristic 2 curve; <see langword="false" /> if the curve is a named characteristic 2, prime, or implicit curve.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECCurve.IsExplicit">
      <summary>Gets a value that indicates whether the curve type indicates an explicit curve (either prime or characteristic 2).</summary>
      <returns>
        <see langword="true" /> if the curve is an explicit curve (either prime or characteristic 2); <see langword="false" /> if the curve is a named or implicit curve.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECCurve.IsNamed">
      <summary>Gets a value that indicates whether the curve type indicates a named curve.</summary>
      <returns>
        <see langword="true" /> if the curve is a named curve; <see langword="false" /> if the curve is an implicit or an  explicit curve (either prime or characteristic 2).</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECCurve.IsPrime">
      <summary>Gets a value that indicates whether the curve type indicates an explicit prime curve.</summary>
      <returns>
        <see langword="true" /> if the curve is an explicit prime curve; <see langword="false" /> if the curve is a named prime, characteristic 2 or implicit curves.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECCurve.Oid">
      <summary>Gets the identifier of a named curve.</summary>
      <returns>The identifier of a named curve.</returns>
    </member>
    <member name="T:System.Security.Cryptography.ECCurve.ECCurveType">
      <summary>Indicates how to interpret the data contained in an <see cref="T:System.Security.Cryptography.ECCurve" /> object.</summary>
    </member>
    <member name="F:System.Security.Cryptography.ECCurve.ECCurveType.Characteristic2">
      <summary>The curve parameters represent a characteristic 2 curve.</summary>
    </member>
    <member name="F:System.Security.Cryptography.ECCurve.ECCurveType.Implicit">
      <summary>No curve data is interpreted. The caller is assumed to know what the curve is.</summary>
    </member>
    <member name="F:System.Security.Cryptography.ECCurve.ECCurveType.Named">
      <summary>The curve parameters represent a named curve.</summary>
    </member>
    <member name="F:System.Security.Cryptography.ECCurve.ECCurveType.PrimeMontgomery">
      <summary>The curve parameters represent a prime curve with the formula B*y^2 = x^3 + A*x^2 + x.</summary>
    </member>
    <member name="F:System.Security.Cryptography.ECCurve.ECCurveType.PrimeShortWeierstrass">
      <summary>The curve parameters represent a prime curve with the formula y^2 = x^3 + A*x + B in the prime field P.</summary>
    </member>
    <member name="F:System.Security.Cryptography.ECCurve.ECCurveType.PrimeTwistedEdwards">
      <summary>The curve parameters represent a prime curve with the formula A*x^2 + y^2 = 1 + B*x^2*y^2 in the prime field P.</summary>
    </member>
    <member name="T:System.Security.Cryptography.ECCurve.NamedCurves">
      <summary>Represents a factory class for creating named curves.</summary>
    </member>
    <member name="P:System.Security.Cryptography.ECCurve.NamedCurves.brainpoolP160r1">
      <summary>Gets a brainpoolP160r1 named curve.</summary>
      <returns>A brainpoolP160r1 named curve.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECCurve.NamedCurves.brainpoolP160t1">
      <summary>Gets a brainpoolP160t1 named curve.</summary>
      <returns>A brainpoolP160t1 named curve.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECCurve.NamedCurves.brainpoolP192r1">
      <summary>Gets a brainpoolP192r1 named curve.</summary>
      <returns>A brainpoolP192r1 named curve.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECCurve.NamedCurves.brainpoolP192t1">
      <summary>Gets a brainpoolP192t1 named curve.</summary>
      <returns>A brainpoolP192t1 named curve.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECCurve.NamedCurves.brainpoolP224r1">
      <summary>Gets a brainpoolP224r1 named curve.</summary>
      <returns>A brainpoolP224r1 named curve.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECCurve.NamedCurves.brainpoolP224t1">
      <summary>Gets a brainpoolP224t1 named curve.</summary>
      <returns>A brainpoolP224t1 named curve.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECCurve.NamedCurves.brainpoolP256r1">
      <summary>Gets a brainpoolP256r1 named curve.</summary>
      <returns>A brainpoolP256r1 named curve.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECCurve.NamedCurves.brainpoolP256t1">
      <summary>Gets a brainpoolP256t1 named curve.</summary>
      <returns>A brainpoolP256t1 named curve.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECCurve.NamedCurves.brainpoolP320r1">
      <summary>Gets a brainpoolP320r1 named curve.</summary>
      <returns>A brainpoolP320r1 named curve.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECCurve.NamedCurves.brainpoolP320t1">
      <summary>Gets a brainpoolP320t1 named curve.</summary>
      <returns>A brainpoolP320t1 named curve.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECCurve.NamedCurves.brainpoolP384r1">
      <summary>Gets a brainpoolP384r1 named curve.</summary>
      <returns>A brainpoolP384r1 named curve.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECCurve.NamedCurves.brainpoolP384t1">
      <summary>Gets a brainpoolP384t1 named curve.</summary>
      <returns>A brainpoolP384t1 named curve.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECCurve.NamedCurves.brainpoolP512r1">
      <summary>Gets a brainpoolP512r1 named curve.</summary>
      <returns>A brainpoolP512r1 named curve.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECCurve.NamedCurves.brainpoolP512t1">
      <summary>Gets a brainpoolP512t1 named curve.</summary>
      <returns>A brainpoolP512t1 named curve.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECCurve.NamedCurves.nistP256">
      <summary>Gets a nistP256 named curve.</summary>
      <returns>A nistP256 named curve.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECCurve.NamedCurves.nistP384">
      <summary>Gets a nistP384 named curve.</summary>
      <returns>A nistP384 named curve.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECCurve.NamedCurves.nistP521">
      <summary>Gets a nistP521 named curve.</summary>
      <returns>A nistP521 named curve.</returns>
    </member>
    <member name="T:System.Security.Cryptography.ECDiffieHellman">
      <summary>Provides an abstract base class that Elliptic Curve Diffie-Hellman (ECDH) algorithm implementations can derive from. This class provides the basic set of operations that all ECDH implementations must support.</summary>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.ECDiffieHellman" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.Create">
      <summary>Creates a new instance of the default implementation of the Elliptic Curve Diffie-Hellman (ECDH) algorithm.</summary>
      <returns>A new instance of the default implementation of this class.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.Create(System.Security.Cryptography.ECCurve)">
      <summary>Creates a new instance of the default implementation of the Elliptic Curve Diffie-Hellman (ECDH) algorithm with a new public/private key-pair generated over the specified curve.</summary>
      <param name="curve">The curve to use to generate a new public/private key-pair.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="curve" /> does not validate.</exception>
      <returns>A new instance of the default implementation of the Elliptic Curve Diffie-Hellman (ECDH) algorithm.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.Create(System.Security.Cryptography.ECParameters)">
      <summary>Creates a new instance of the default implementation of the Elliptic Curve Diffie-Hellman (ECDH) algorithm with the key described by the specified  <see cref="T:System.Security.Cryptography.ECParameters" /> object.</summary>
      <param name="parameters">The parameters  for the elliptic curve cryptography (ECC) algorithm.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="parameters" /> does not validate.</exception>
      <returns>A new instance of the default implementation of the Elliptic Curve Diffie-Hellman (ECDH) algorithm.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.Create(System.String)">
      <summary>Creates a new instance of the specified implementation of the Elliptic Curve Diffie-Hellman (ECDH) algorithm.</summary>
      <param name="algorithm">The name of an implementation of the ECDH algorithm.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="algorithm" /> parameter is <see langword="null" />.</exception>
      <returns>A new instance of the specified implementation of this class. If the specified algorithm name does not map to an ECDH implementation, this method returns <see langword="null" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.DeriveKeyFromHash(System.Security.Cryptography.ECDiffieHellmanPublicKey,System.Security.Cryptography.HashAlgorithmName)">
      <summary>Performs key derivation using a specified hash algorithm.</summary>
      <param name="otherPartyPublicKey">The other party's public key.</param>
      <param name="hashAlgorithm">The hash algorithm to use to derive the key material.</param>
      <exception cref="T:System.ArgumentException">The curve used by <paramref name="otherPartyPublicKey" /> has a different size than the curve from this key.

-or-

The <paramref name="hashAlgorithm" /> parameter does not specify a hash.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="otherPartyPublicKey" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The curve used by <paramref name="otherPartyPublicKey" /> is different than the curve from this key.

-or-

This instance represents only a public key.</exception>
      <returns>The hash of the shared secret.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.DeriveKeyFromHash(System.Security.Cryptography.ECDiffieHellmanPublicKey,System.Security.Cryptography.HashAlgorithmName,System.Byte[],System.Byte[])">
      <summary>When implemented in a derived class, performs key derivation using a specified hash algorithm with optional prepended or appended data.</summary>
      <param name="otherPartyPublicKey">The other party's public key.</param>
      <param name="hashAlgorithm">The hash algorithm to use to derive the key material.</param>
      <param name="secretPrepend">A value to prepend to the derived secret before hashing.</param>
      <param name="secretAppend">A value to append to the derived secret before hashing.</param>
      <exception cref="T:System.NotImplementedException">A derived class must override this method.</exception>
      <exception cref="T:System.ArgumentException">The curve used by <paramref name="otherPartyPublicKey" /> has a different size than the curve from this key.

-or-

The <paramref name="hashAlgorithm" /> parameter does not specify a hash.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="otherPartyPublicKey" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The curve used by <paramref name="otherPartyPublicKey" /> is different than the curve from this key.

-or-

This instance represents only a public key.</exception>
      <returns>The hash of the shared secret after prepending or appending data as requested.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.DeriveKeyFromHmac(System.Security.Cryptography.ECDiffieHellmanPublicKey,System.Security.Cryptography.HashAlgorithmName,System.Byte[])">
      <summary>Performs key derivation using a specified HMAC (Hash-based Message Authentication Code) algorithm.</summary>
      <param name="otherPartyPublicKey">The other party's public key.</param>
      <param name="hashAlgorithm">The hash algorithm to use to derive the key material.</param>
      <param name="hmacKey">The key for the HMAC.</param>
      <exception cref="T:System.ArgumentException">The curve used by <paramref name="otherPartyPublicKey" /> has a different size than the curve from this key.

-or-

The <paramref name="hashAlgorithm" /> parameter does not specify a hash.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="otherPartyPublicKey" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The curve used by <paramref name="otherPartyPublicKey" /> is different than the curve from this key.

-or-

This instance represents only a public key.</exception>
      <returns>The HMAC of the shared secret.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.DeriveKeyFromHmac(System.Security.Cryptography.ECDiffieHellmanPublicKey,System.Security.Cryptography.HashAlgorithmName,System.Byte[],System.Byte[],System.Byte[])">
      <summary>When implemented in a derived class, performs key derivation using a specified HMAC (Hash-based Message Authentication Code) algorithm with optional prepended or appended data.</summary>
      <param name="otherPartyPublicKey">The other party's public key.</param>
      <param name="hashAlgorithm">The hash algorithm to use to derive the key material.</param>
      <param name="hmacKey">The key for the HMAC.</param>
      <param name="secretPrepend">A value to prepend to the derived secret before hashing.</param>
      <param name="secretAppend">A value to append to the derived secret before hashing.</param>
      <exception cref="T:System.NotImplementedException">A derived class must override this method.</exception>
      <exception cref="T:System.ArgumentException">The curve used by <paramref name="otherPartyPublicKey" /> has a different size than the curve from this key.

-or-

The <paramref name="hashAlgorithm" /> parameter does not specify a hash.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="otherPartyPublicKey" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The curve used by <paramref name="otherPartyPublicKey" /> is different than the curve from this key.

-or-

This instance represents only a public key.</exception>
      <returns>The HMAC of the shared secret after prepending or appending data as requested.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.DeriveKeyMaterial(System.Security.Cryptography.ECDiffieHellmanPublicKey)">
      <summary>When implemented in a derived class, performs a key derivation on the shared secret.</summary>
      <param name="otherPartyPublicKey">The other party's public key.</param>
      <exception cref="T:System.ArgumentException">The curve used by <paramref name="otherPartyPublicKey" /> has a different size than the curve from this key.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="otherPartyPublicKey" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The curve used by <paramref name="otherPartyPublicKey" /> is different than the curve from this key.

-or-

This instance represents only a public key.</exception>
      <returns>The result of the key derivation function, using the shared secret as an input.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.DeriveKeyTls(System.Security.Cryptography.ECDiffieHellmanPublicKey,System.Byte[],System.Byte[])">
      <summary>When implemented in a derived class, performs key derivation using the TLS (Transport Layer Security) 1.1 PRF (Pseudo-Random Function).</summary>
      <param name="otherPartyPublicKey">The other party's public key.</param>
      <param name="prfLabel">The ASCII-encoded PRF label.</param>
      <param name="prfSeed">The 64-byte PRF seed.</param>
      <exception cref="T:System.NotImplementedException">A derived class must override this method.</exception>
      <exception cref="T:System.ArgumentException">The curve used by <paramref name="otherPartyPublicKey" /> has a different size than the curve from this key.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="otherPartyPublicKey" />, <paramref name="prfLabel" /> or <paramref name="prfSeed" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="prfSeed" /> is not exactly 64 bytes in length.

-or-

The curve used by <paramref name="otherPartyPublicKey" /> is different than the curve from this key.

-or-

This instance represents only a public key.</exception>
      <returns>The first 48 bytes from the TLS 1.1 PRF, using the shared secret as the key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.ExportECPrivateKey">
      <summary>Exports the current key in the ECPrivateKey format.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.</exception>
      <returns>A byte array containing the ECPrivateKey representation of this key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.ExportExplicitParameters(System.Boolean)">
      <summary>When overridden in a derived class, exports either the public or the public and private key information using the explicit curve form from a working <see cref="T:System.Security.Cryptography.ECDiffieHellman" /> key to an <see cref="T:System.Security.Cryptography.ECParameters" /> structure so that it can be passed to the <see cref="M:System.Security.Cryptography.ECDiffieHellman.ImportParameters(System.Security.Cryptography.ECParameters)" /> method.</summary>
      <param name="includePrivateParameters">
        <see langword="true" /> to include private parameters; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.NotImplementedException">A derived class must override this method.</exception>
      <returns>An object that represents the point on the curve for this key, using the explicit curve format.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.ExportParameters(System.Boolean)">
      <summary>When overridden in a derived class, exports either the public or the public and private key information from a working <see cref="T:System.Security.Cryptography.ECDiffieHellman" /> key to an <see cref="T:System.Security.Cryptography.ECParameters" /> structure so that it can be passed to the <see cref="M:System.Security.Cryptography.ECDiffieHellman.ImportParameters(System.Security.Cryptography.ECParameters)" /> method.</summary>
      <param name="includePrivateParameters">
        <see langword="true" /> to include private parameters; otherwise, <see langword="false" /> to include public parameters only.</param>
      <exception cref="T:System.NotImplementedException">A derived class must override this method.</exception>
      <returns>An object that represents the point on the curve for this key. It can be passed to the <see cref="M:System.Security.Cryptography.ECDiffieHellman.ImportParameters(System.Security.Cryptography.ECParameters)" /> method.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.FromXmlString(System.String)">
      <summary>This method throws in all cases.</summary>
      <param name="xmlString">The XML string to use to reconstruct the <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> object.</param>
      <exception cref="T:System.NotImplementedException">In all cases.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.GenerateKey(System.Security.Cryptography.ECCurve)">
      <summary>When overridden in a derived class, generates a new ephemeral public/private key pair for the specified curve.</summary>
      <param name="curve">The curve used to generate an ephemeral public/private key pair.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="curve" /> does not validate.</exception>
      <exception cref="T:System.NotImplementedException">A derived class must override this method.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.ImportECPrivateKey(System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Imports the public/private keypair from an ECPrivateKey structure, replacing the keys for this object.</summary>
      <param name="source">The bytes of an ECPrivateKey structure in the ASN.1-BER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The contents of <paramref name="source" /> do not represent an ASN.1-BER-encoded PKCS#8 ECPrivateKey structure.

-or-

The key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.ImportEncryptedPkcs8PrivateKey(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Imports the public/private keypair from a PKCS#8 EncryptedPrivateKeyInfo structure after decrypting with a byte-based password, replacing the keys for this object.</summary>
      <param name="passwordBytes">The bytes to use as a password when decrypting the key material.</param>
      <param name="source">The bytes of a PKCS#8 EncryptedPrivateKeyInfo structure in the ASN.1-BER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The password is incorrect.

-or-

The contents of <paramref name="source" /> indicate the Key Derivation Function (KDF) to apply is the legacy PKCS#12 KDF, which requires <see cref="T:System.Char" />-based passwords.

-or-

The contents of <paramref name="source" /> do not represent an ASN.1-BER-encoded PKCS#8 EncryptedPrivateKeyInfo structure.

-or-

The contents of <paramref name="source" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

-or-

The contents of <paramref name="source" /> represent the key in a format that is not supported.

-or-

The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.ImportEncryptedPkcs8PrivateKey(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Imports the public/private keypair from a PKCS#8 EncryptedPrivateKeyInfo structure after decrypting with a char-based password, replacing the keys for this object.</summary>
      <param name="password">The password to use for decrypting the key material.</param>
      <param name="source">The bytes of a PKCS#8 EncryptedPrivateKeyInfo structure in the ASN.1-BER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The password is incorrect.

-or-

The contents of <paramref name="source" /> do not represent an ASN.1-BER-encoded PKCS#8 EncryptedPrivateKeyInfo structure.

-or-

The contents of <paramref name="source" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

-or-

The contents of <paramref name="source" /> represent the key in a format that is not supported.

-or-

The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.ImportFromEncryptedPem(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Byte})">
      <summary>Imports an encrypted RFC 7468 PEM-encoded private key, replacing the keys for this object.</summary>
      <param name="input">The PEM text of the encrypted key to import.</param>
      <param name="passwordBytes">The bytes to use as a password when decrypting the key material.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="input" /> does not contain a PEM-encoded key with a recognized label.

-or-

<paramref name="input" /> contains multiple PEM-encoded keys with a recognized label.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
The password is incorrect.

-or-

The base-64 decoded contents of the PEM text from <paramref name="input" /> do not represent an ASN.1-BER-encoded PKCS#8 EncryptedPrivateKeyInfo structure.

-or-

The base-64 decoded contents of the PEM text from <paramref name="input" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

-or-

The base-64 decoded contents of the PEM text from <paramref name="input" /> represent the key in a format that is not supported.

-or-

The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.ImportFromEncryptedPem(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char})">
      <summary>Imports an encrypted RFC 7468 PEM-encoded private key, replacing the keys for this object.</summary>
      <param name="input">The PEM text of the encrypted key to import.</param>
      <param name="password">The password to use for decrypting the key material.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="input" /> does not contain a PEM-encoded key with a recognized label.

-or-

<paramref name="input" /> contains multiple PEM-encoded keys with a recognized label.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
The password is incorrect.

-or-

The base-64 decoded contents of the PEM text from <paramref name="input" /> do not represent an ASN.1-BER-encoded PKCS#8 EncryptedPrivateKeyInfo structure.

-or-

The base-64 decoded contents of the PEM text from <paramref name="input" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

-or-

The base-64 decoded contents of the PEM text from <paramref name="input" /> represent the key in a format that is not supported.

-or-

The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.ImportFromPem(System.ReadOnlySpan{System.Char})">
      <summary>Imports an RFC 7468 PEM-encoded key, replacing the keys for this object.</summary>
      <param name="input">The PEM text of the key to import.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="input" /> does not contain a PEM-encoded key with a recognized label.

-or-

<paramref name="input" /> contains multiple PEM-encoded keys with a recognized label.

-or-

<paramref name="input" /> contains an encrypted PEM-encoded key.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.ImportParameters(System.Security.Cryptography.ECParameters)">
      <summary>When overridden in a derived class, imports the specified parameters for an <see cref="T:System.Security.Cryptography.ECCurve" /> as an ephemeral key into the current <see cref="T:System.Security.Cryptography.ECDiffieHellman" /> object.</summary>
      <param name="parameters">The curve's parameters to import.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="parameters" /> does not validate.</exception>
      <exception cref="T:System.NotImplementedException">A derived class must override this method.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.ImportPkcs8PrivateKey(System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Imports the public/private keypair from a PKCS#8 PrivateKeyInfo structure after decryption, replacing the keys for this object.</summary>
      <param name="source">The bytes of a PKCS#8 PrivateKeyInfo structure in the ASN.1-BER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The contents of <paramref name="source" /> do not represent an ASN.1-BER-encoded PKCS#8 PrivateKeyInfo structure.

-or-

The contents of <paramref name="source" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

-or-

The contents of <paramref name="source" /> represent the key in a format that is not supported.

-or-

The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.ImportSubjectPublicKeyInfo(System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Imports the public key from an X.509 SubjectPublicKeyInfo structure after decryption, replacing the keys for this object.</summary>
      <param name="source">The bytes of an X.509 SubjectPublicKeyInfo structure in the ASN.1-DER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The contents of <paramref name="source" /> do not represent an ASN.1-DER-encoded X.509 SubjectPublicKeyInfo structure.

-or-

The contents of <paramref name="source" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

-or-

The contents of <paramref name="source" /> represent the key in a format that is not supported.

-or-

The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.ToXmlString(System.Boolean)">
      <summary>This method throws in all cases.</summary>
      <param name="includePrivateParameters">
        <see langword="true" /> to include private parameters; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.NotImplementedException">In all cases.</exception>
      <returns>This method does not return a value.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.TryExportECPrivateKey(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to export the current key in the <see langword="ECPrivateKey" /> format into a provided buffer.</summary>
      <param name="destination">The byte span to receive the <see langword="ECPrivateKey" /> data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.TryExportEncryptedPkcs8PrivateKey(System.ReadOnlySpan{System.Byte},System.Security.Cryptography.PbeParameters,System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to export the current key in the PKCS#8 EncryptedPrivateKeyInfo format into a provided buffer, using a byte-based password.</summary>
      <param name="passwordBytes">The bytes to use as a password when encrypting the key material.</param>
      <param name="pbeParameters">The password-based encryption (PBE) parameters to use when encrypting the key material.</param>
      <param name="destination">The byte span to receive the PKCS#8 EncryptedPrivateKeyInfo data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.

-or-

<paramref name="pbeParameters" /> indicates that <see cref="F:System.Security.Cryptography.PbeEncryptionAlgorithm.TripleDes3KeyPkcs12" /> should be used, which requires <see cref="T:System.Char" />-based passwords.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.TryExportEncryptedPkcs8PrivateKey(System.ReadOnlySpan{System.Char},System.Security.Cryptography.PbeParameters,System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to export the current key in the PKCS#8 EncryptedPrivateKeyInfo format into a provided buffer, using a char-based password.</summary>
      <param name="password">The password to use when encrypting the key material.</param>
      <param name="pbeParameters">The password-based encryption (PBE) parameters to use when encrypting the key material.</param>
      <param name="destination">The byte span to receive the PKCS#8 EncryptedPrivateKeyInfo data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.TryExportPkcs8PrivateKey(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to export the current key in the PKCS#8 PrivateKeyInfo format into a provided buffer.</summary>
      <param name="destination">The byte span to receive the PKCS#8 PrivateKeyInfo data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellman.TryExportSubjectPublicKeyInfo(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to export the current key in the X.509 SubjectPublicKeyInfo format into a provided buffer.</summary>
      <param name="destination">The byte span to receive the X.509 SubjectPublicKeyInfo data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECDiffieHellman.KeyExchangeAlgorithm">
      <summary>Gets the name of the key exchange algorithm.</summary>
      <returns>The name of the key exchange algorithm.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECDiffieHellman.PublicKey">
      <summary>Gets the public key that is being used by the current Elliptic Curve Diffie-Hellman (ECDH) instance.</summary>
      <returns>The public part of the ECDH key pair that is being used by this <see cref="T:System.Security.Cryptography.ECDiffieHellman" /> instance.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECDiffieHellman.SignatureAlgorithm">
      <summary>Gets the name of the signature algorithm.</summary>
      <returns>Always <see langword="null" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.ECDiffieHellmanPublicKey">
      <summary>Provides an abstract base class from which all <see cref="T:System.Security.Cryptography.ECDiffieHellmanCngPublicKey" /> implementations must inherit.</summary>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellmanPublicKey.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.ECDiffieHellmanPublicKey" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellmanPublicKey.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.ECDiffieHellmanPublicKey" /> class.</summary>
      <param name="keyBlob">A byte array that represents an <see cref="T:System.Security.Cryptography.ECDiffieHellmanPublicKey" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyBlob" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellmanPublicKey.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.Security.Cryptography.ECDiffieHellman" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellmanPublicKey.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Security.Cryptography.ECDiffieHellman" /> class and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellmanPublicKey.ExportExplicitParameters">
      <summary>When overridden in a derived class, exports the explicit <see cref="T:System.Security.Cryptography.ECParameters" /> for an <see cref="T:System.Security.Cryptography.ECCurve" /> object.</summary>
      <exception cref="T:System.NotSupportedException">A derived class must override this method.</exception>
      <returns>An object that represents the point on the curve for this key, using the explicit curve format.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellmanPublicKey.ExportParameters">
      <summary>When overridden in a derived class, exports the named or explicit <see cref="T:System.Security.Cryptography.ECParameters" /> for an <see cref="T:System.Security.Cryptography.ECCurve" /> object.</summary>
      <exception cref="T:System.NotSupportedException">A derived class must override this method.</exception>
      <returns>An object that represents the point on the curve for this key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellmanPublicKey.ExportSubjectPublicKeyInfo">
      <summary>Exports the current key in the X.509 SubjectPublicKeyInfo format.</summary>
      <exception cref="T:System.NotSupportedException">The member <see cref="M:System.Security.Cryptography.ECDiffieHellmanPublicKey.ExportParameters" /> has not been overridden in a derived class.</exception>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key is invalid and could not be exported.</exception>
      <returns>A byte array containing the X.509 SubjectPublicKeyInfo representation of this key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellmanPublicKey.ToByteArray">
      <summary>Serializes the <see cref="T:System.Security.Cryptography.ECDiffieHellmanPublicKey" /> key BLOB to a byte array.</summary>
      <returns>A byte array that contains the serialized Elliptic Curve Diffie-Hellman (ECDH) public key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellmanPublicKey.ToXmlString">
      <summary>Serializes the <see cref="T:System.Security.Cryptography.ECDiffieHellmanPublicKey" /> public key to an XML string.</summary>
      <returns>An XML string that contains the serialized Elliptic Curve Diffie-Hellman (ECDH) public key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDiffieHellmanPublicKey.TryExportSubjectPublicKeyInfo(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to export the current key in the X.509 SubjectPublicKeyInfo format.</summary>
      <param name="destination">The byte span to receive the X.509 SubjectPublicKeyInfo data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />.
            This parameter is treated as uninitialized.</param>
      <exception cref="T:System.NotSupportedException">The member <see cref="M:System.Security.Cryptography.ECDiffieHellmanPublicKey.ExportParameters" /> has not been overridden in a derived class.</exception>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key is invalid and could not be exported.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.ECDsa">
      <summary>Provides an abstract base class that encapsulates the Elliptic Curve Digital Signature Algorithm (ECDSA).</summary>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.ECDsa" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.Create">
      <summary>Creates a new instance of the default implementation of the Elliptic Curve Digital Signature Algorithm (ECDSA).</summary>
      <returns>A new instance of the default implementation (<see cref="T:System.Security.Cryptography.ECDsaCng" />) of this class.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.Create(System.Security.Cryptography.ECCurve)">
      <summary>Creates a new instance of the default implementation of the Elliptic Curve Digital Signature Algorithm (ECDSA) with a newly generated key over the specified curve.</summary>
      <param name="curve">The curve to use for key generation.</param>
      <returns>A new instance of the default implementation (<see cref="T:System.Security.Cryptography.ECDsaCng" />) of this class.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.Create(System.Security.Cryptography.ECParameters)">
      <summary>Creates a new instance of the default implementation of the Elliptic Curve Digital Signature Algorithm (ECDSA) using the specified parameters as the key.</summary>
      <param name="parameters">The parameters representing the key to use.</param>
      <returns>A new instance of the default implementation (<see cref="T:System.Security.Cryptography.ECDsaCng" />) of this class.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.Create(System.String)">
      <summary>Creates a new instance of the specified implementation of the Elliptic Curve Digital Signature Algorithm (ECDSA).</summary>
      <param name="algorithm">The name of an ECDSA implementation. The following strings all refer to the same implementation, which is the only implementation currently supported in the .NET Framework:  
  
- "ECDsa"  
  
- "ECDsaCng"  
  
- "System.Security.Cryptography.ECDsaCng"  
  
 You can also provide the name of a custom ECDSA implementation.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="algorithm" /> parameter is <see langword="null" />.</exception>
      <returns>A new instance of the specified implementation of this class. If the specified algorithm name does not map to an ECDSA implementation, this method returns <see langword="null" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.ExportECPrivateKey">
      <summary>Exports the current key in the ECPrivateKey format.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.</exception>
      <returns>A byte array containing the ECPrivateKey representation of this key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.ExportExplicitParameters(System.Boolean)">
      <summary>When overridden in a derived class, exports the explicit parameters for an elliptic curve.</summary>
      <param name="includePrivateParameters">
        <see langword="true" /> to include private parameters; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.NotSupportedException">A derived class must override this method.</exception>
      <returns>The parameters representing the point on the curve for this key, using the explicit curve format.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.ExportParameters(System.Boolean)">
      <summary>When overridden in a derived class, exports the named or explicit parameters for an elliptic curve. If the curve has a name, the <see cref="F:System.Security.Cryptography.ECParameters.Curve" /> field contains named curve parameters, otherwise it         contains explicit parameters.</summary>
      <param name="includePrivateParameters">
        <see langword="true" /> to include private parameters; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.NotSupportedException">A derived class must override this method.</exception>
      <returns>The parameters representing the point on the curve for this key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.FromXmlString(System.String)">
      <summary>This method throws in all cases.</summary>
      <param name="xmlString">The XML string to use to reconstruct the <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> object.</param>
      <exception cref="T:System.NotImplementedException">In all cases.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.GenerateKey(System.Security.Cryptography.ECCurve)">
      <summary>When overridden in a derived class, generates a new ephemeral public/private key pair for the specified curve, replacing the current key.</summary>
      <param name="curve">The curve to use to generate the key.</param>
      <exception cref="T:System.NotSupportedException">A derived class must override this method.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.GetMaxSignatureSize(System.Security.Cryptography.DSASignatureFormat)">
      <summary>Gets the largest size, in bytes, for a signature produced by this key in the indicated format.</summary>
      <param name="signatureFormat">The encoding format for a signature.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.</exception>
      <returns>The largest size, in bytes, for a signature produced by this key in the indicated format.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.HashData(System.Byte[],System.Int32,System.Int32,System.Security.Cryptography.HashAlgorithmName)">
      <summary>When overridden in a derived class, computes the hash value of the specified portion of a byte array by using the specified hashing algorithm.</summary>
      <param name="data">The data to be hashed.</param>
      <param name="offset">The index of the first byte in <paramref name="data" /> to be hashed.</param>
      <param name="count">The number of bytes to hash.</param>
      <param name="hashAlgorithm">The algorithm to use to hash the data.</param>
      <exception cref="T:System.NotImplementedException">A derived class must override this method.</exception>
      <returns>The hashed data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.HashData(System.IO.Stream,System.Security.Cryptography.HashAlgorithmName)">
      <summary>When overridden in a derived class, computes the hash value of the specified binary stream by using the specified hashing algorithm.</summary>
      <param name="data">The binary stream to hash.</param>
      <param name="hashAlgorithm">The algorithm to use to hash the data.</param>
      <exception cref="T:System.NotImplementedException">A derived class must override this method.</exception>
      <returns>The hashed data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.ImportECPrivateKey(System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Imports the public/private keypair from an ECPrivateKey structure, replacing the keys for this object.</summary>
      <param name="source">The bytes of an ECPrivateKey structure in the ASN.1-BER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The contents of <paramref name="source" /> do not represent an ASN.1-BER-encoded PKCS#8 ECPrivateKey structure.

-or-

The key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.ImportEncryptedPkcs8PrivateKey(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Imports the public/private keypair from a PKCS#8 EncryptedPrivateKeyInfo structure after decrypting with a byte-based password, replacing the keys for this object.</summary>
      <param name="passwordBytes">The bytes to use as a password when decrypting the key material.</param>
      <param name="source">The bytes of a PKCS#8 EncryptedPrivateKeyInfo structure in the ASN.1-BER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The password is incorrect.

-or-

The contents of <paramref name="source" /> indicate the Key Derivation Function (KDF) to apply is the legacy PKCS#12 KDF, which requires <see cref="T:System.Char" />-based passwords.

-or-

The contents of <paramref name="source" /> do not represent an ASN.1-BER-encoded PKCS#8 EncryptedPrivateKeyInfo structure.

-or-

The contents of <paramref name="source" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

-or-

The contents of <paramref name="source" /> represent the key in a format that is not supported.

-or-

The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.ImportEncryptedPkcs8PrivateKey(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Imports the public/private keypair from a PKCS#8 EncryptedPrivateKeyInfo structure after decrypting with a char-based password, replacing the keys for this object.</summary>
      <param name="password">The password to use for decrypting the key material.</param>
      <param name="source">The bytes of a PKCS#8 EncryptedPrivateKeyInfo structure in the ASN.1-BER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The password is incorrect.

-or-

The contents of <paramref name="source" /> do not represent an ASN.1-BER-encoded PKCS#8 EncryptedPrivateKeyInfo structure.

-or-

The contents of <paramref name="source" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

-or-

The contents of <paramref name="source" /> represent the key in a format that is not supported.

-or-

The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.ImportFromEncryptedPem(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Byte})">
      <summary>Imports an encrypted RFC 7468 PEM-encoded private key, replacing the keys for this object.</summary>
      <param name="input">The PEM text of the encrypted key to import.</param>
      <param name="passwordBytes">The bytes to use as a password when decrypting the key material.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="input" /> does not contain a PEM-encoded key with a recognized label.

-or-

<paramref name="input" /> contains multiple PEM-encoded keys with a recognized label.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
The password is incorrect.

-or-
          
The base-64 decoded contents of the PEM text from <paramref name="input" /> do not represent an ASN.1-BER-encoded PKCS#8 EncryptedPrivateKeyInfo structure.

-or-

The base-64 decoded contents of the PEM text from <paramref name="input" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

-or-

The base-64 decoded contents of the PEM text from <paramref name="input" /> represent the key in a format that is not supported.

-or-

The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.ImportFromEncryptedPem(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char})">
      <summary>Imports an encrypted RFC 7468 PEM-encoded private key, replacing the keys for this object.</summary>
      <param name="input">The PEM text of the encrypted key to import.</param>
      <param name="password">The password to use for decrypting the key material.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="input" /> does not contain a PEM-encoded key with a recognized label.

-or-

<paramref name="input" /> contains multiple PEM-encoded keys with a recognized label.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
The password is incorrect.

-or-

The base-64 decoded contents of the PEM text from <paramref name="input" /> do not represent an ASN.1-BER-encoded PKCS#8 EncryptedPrivateKeyInfo structure.

-or-

The base-64 decoded contents of the PEM text from <paramref name="input" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

-or-

The base-64 decoded contents of the PEM text from <paramref name="input" /> represent the key in a format that is not supported.

-or-

The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.ImportFromPem(System.ReadOnlySpan{System.Char})">
      <summary>Imports an RFC 7468 PEM-encoded key, replacing the keys for this object.</summary>
      <param name="input">The PEM text of the key to import.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="input" /> does not contain a PEM-encoded key with a recognized label.

-or-

<paramref name="input" /> contains multiple PEM-encoded keys with a recognized label.

-or-

<paramref name="input" /> contains an encrypted PEM-encoded key.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.ImportParameters(System.Security.Cryptography.ECParameters)">
      <summary>When overridden in a derived class, imports the specified parameters.</summary>
      <param name="parameters">The curve parameters.</param>
      <exception cref="T:System.NotSupportedException">A derived class must override this method.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.ImportPkcs8PrivateKey(System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Imports the public/private keypair from a PKCS#8 PrivateKeyInfo structure after decryption, replacing the keys for this object.</summary>
      <param name="source">The bytes of a PKCS#8 PrivateKeyInfo structure in the ASN.1-BER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The contents of <paramref name="source" /> do not represent an ASN.1-BER-encoded PKCS#8 PrivateKeyInfo structure.

-or-

The contents of <paramref name="source" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

-or-

The contents of <paramref name="source" /> represent the key in a format that is not supported.

-or-

The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.ImportSubjectPublicKeyInfo(System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Imports the public key from an X.509 SubjectPublicKeyInfo structure after decryption, replacing the keys for this object.</summary>
      <param name="source">The bytes of an X.509 SubjectPublicKeyInfo structure in the ASN.1-DER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The contents of <paramref name="source" /> do not represent an ASN.1-DER-encoded X.509 SubjectPublicKeyInfo structure.

-or-

The contents of <paramref name="source" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

-or-

The contents of <paramref name="source" /> represent the key in a format that is not supported.

-or-

The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.SignData(System.Byte[],System.Int32,System.Int32,System.Security.Cryptography.HashAlgorithmName)">
      <summary>Computes the hash value of a portion of the specified byte array using the specified hash algorithm and signs the resulting hash value.</summary>
      <param name="data">The input data for which to compute the hash.</param>
      <param name="offset">The offset into the array at which to begin using data.</param>
      <param name="count">The number of bytes in the array to use as data.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than zero.  
  
 -or-  
  
 <paramref name="count" /> is less than zero.  
  
 -or-  
  
 <paramref name="offset" /> + <paramref name="count" /> - 1 results in an index that is beyond the upper bound of <paramref name="data" />.</exception>
      <returns>The ECDSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.SignData(System.Byte[],System.Int32,System.Int32,System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat)">
      <summary>Computes the hash value of the specified data and signs it using the specified signature format.</summary>
      <param name="data">The data to sign.</param>
      <param name="offset">The offset into <paramref name="data" /> at which to begin hashing.</param>
      <param name="count">The number of bytes to read from <paramref name="data" />.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <param name="signatureFormat">The encoding format to use for the signature.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.
 
 -or- 
 
 <paramref name="offset" /> is less than zero.
 
 -or- 
 
 <paramref name="count" /> is less than zero.
 
 -or- 
 
 <paramref name="offset" /> + <paramref name="count" /> - 1 results in an index that is beyond the upper bound of <paramref name="data" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" /> has a <see langword="null" /> or empty <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the hashing or signing operation.</exception>
      <returns>The ECDSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.SignData(System.Byte[],System.Security.Cryptography.HashAlgorithmName)">
      <summary>Computes the hash value of the specified byte array using the specified hash algorithm and signs the resulting hash value.</summary>
      <param name="data">The input data for which to compute the hash.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <returns>The ECDSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.SignData(System.Byte[],System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat)">
      <summary>Computes the hash value of the specified data and signs it using the specified signature format.</summary>
      <param name="data">The data to sign.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <param name="signatureFormat">The encoding format to use for the signature.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" /> has a <see langword="null" /> or empty <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the hashing or signing operation.</exception>
      <returns>The ECDSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.SignData(System.IO.Stream,System.Security.Cryptography.HashAlgorithmName)">
      <summary>Computes the hash value of the specified stream using the specified hash algorithm and signs the resulting hash value.</summary>
      <param name="data">The input stream for which to compute the hash.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <returns>The ECDSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.SignData(System.IO.Stream,System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat)">
      <summary>Computes the hash value of the specified data and signs it using the specified signature format.</summary>
      <param name="data">The data to sign.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <param name="signatureFormat">The encoding format to use for the signature.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" /> has a <see langword="null" /> or empty <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the hashing or signing operation.</exception>
      <returns>The ECDSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.SignDataCore(System.IO.Stream,System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat)">
      <summary>Computes the hash value of the specified data and signs it using the specified signature format.</summary>
      <param name="data">The data to sign.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <param name="signatureFormat">The encoding format to use for the signature.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the hashing or signing operation.</exception>
      <returns>The ECDSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.SignDataCore(System.ReadOnlySpan{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat)">
      <summary>Computes the hash value of the specified data and signs it using the specified signature format.</summary>
      <param name="data">The data to sign.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <param name="signatureFormat">The encoding format to use for the signature.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the hashing or signing operation.</exception>
      <returns>The ECDSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.SignHash(System.Byte[])">
      <summary>Generates a digital signature for the specified hash value.</summary>
      <param name="hash">The hash value of the data that is being signed.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="hash" /> parameter is <see langword="null" />.</exception>
      <returns>A digital signature for the specified hash value.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.SignHash(System.Byte[],System.Security.Cryptography.DSASignatureFormat)">
      <summary>Computes the ECDSA signature for the specified hash value in the indicated format.</summary>
      <param name="hash">The hash value to sign.</param>
      <param name="signatureFormat">The encoding format to use for the signature.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="hash" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the signing operation.</exception>
      <returns>The ECDSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.SignHashCore(System.ReadOnlySpan{System.Byte},System.Security.Cryptography.DSASignatureFormat)">
      <summary>Computes the ECDSA signature for the specified hash value in the indicated format.</summary>
      <param name="hash">The hash value to sign.</param>
      <param name="signatureFormat">The encoding format to use for the signature.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the signing operation.</exception>
      <returns>The ECDSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.ToXmlString(System.Boolean)">
      <summary>This method throws in all cases.</summary>
      <param name="includePrivateParameters">
        <see langword="true" /> to include private parameters; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.NotImplementedException">In all cases.</exception>
      <returns>This method does not return a value.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.TryExportECPrivateKey(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to export the current key in the ECPrivateKey format into a provided buffer.</summary>
      <param name="destination">The byte span to receive the ECPrivateKey data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.TryExportEncryptedPkcs8PrivateKey(System.ReadOnlySpan{System.Byte},System.Security.Cryptography.PbeParameters,System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to export the current key in the PKCS#8 EncryptedPrivateKeyInfo format into a provided buffer, using a byte-based password.</summary>
      <param name="passwordBytes">The bytes to use as a password when encrypting the key material.</param>
      <param name="pbeParameters">The password-based encryption (PBE) parameters to use when encrypting the key material.</param>
      <param name="destination">The byte span to receive the PKCS#8 EncryptedPrivateKeyInfo data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.

-or-

<paramref name="pbeParameters" /> indicates that <see cref="F:System.Security.Cryptography.PbeEncryptionAlgorithm.TripleDes3KeyPkcs12" /> should be used, which requires <see cref="T:System.Char" />-based passwords.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.TryExportEncryptedPkcs8PrivateKey(System.ReadOnlySpan{System.Char},System.Security.Cryptography.PbeParameters,System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to export the current key in the PKCS#8 EncryptedPrivateKeyInfo format into a provided buffer, using a char-based password.</summary>
      <param name="password">The password to use when encrypting the key material.</param>
      <param name="pbeParameters">The password-based encryption (PBE) parameters to use when encrypting the key material.</param>
      <param name="destination">The byte span to receive the PKCS#8 EncryptedPrivateKeyInfo data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.TryExportPkcs8PrivateKey(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to export the current key in the PKCS#8 PrivateKeyInfo format into a provided buffer.</summary>
      <param name="destination">The byte span to receive the PKCS#8 PrivateKeyInfo data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.TryExportSubjectPublicKeyInfo(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to export the current key in the X.509 SubjectPublicKeyInfo format into a provided buffer.</summary>
      <param name="destination">The byte span to receive the X.509 SubjectPublicKeyInfo data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.TryHashData(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Int32@)">
      <summary>Attempts to compute the hash value of the specified read-only span of bytes into the provided destination by using the specified hashing algorithm.</summary>
      <param name="data">The data to be hashed.</param>
      <param name="destination">The buffer to receive the hash value.</param>
      <param name="hashAlgorithm">The algorithm to use to hash the data.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <returns>
        <see langword="false" /> if <paramref name="destination" /> is not long enough to receive the hash value.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.TrySignData(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Int32@)">
      <summary>Attempts to compute the ECDSA digital signature for the specified read-only span of bytes into the provided destination by using the specified hashing algorithm and the current key.</summary>
      <param name="data">The data to be signed.</param>
      <param name="destination">The buffer to receive the signature.</param>
      <param name="hashAlgorithm">The algorithm to use to hash the data for signing.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <returns>
        <see langword="false" /> if <paramref name="destination" /> is not long enough to receive the signature.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.TrySignData(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat,System.Int32@)">
      <summary>Attempts to create the ECDSA signature for the specified data in the indicated format into the provided buffer.</summary>
      <param name="data">The data to hash and sign.</param>
      <param name="destination">The buffer to receive the signature.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <param name="signatureFormat">The encoding format to use for the signature.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" /> has a <see langword="null" /> or empty <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the signing operation.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the signature; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.TrySignDataCore(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat,System.Int32@)">
      <summary>Attempts to create the ECDSA signature for the specified data in the indicated format into the provided buffer.</summary>
      <param name="data">The data to hash and sign.</param>
      <param name="destination">The buffer to receive the signature.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <param name="signatureFormat">The encoding format to use for the signature.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the signing operation.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the signature; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.TrySignHash(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to compute the ECDSA digital signature for the specified read-only span of bytes representing a data hash into the provided destination by using the current key.</summary>
      <param name="hash">The hash value of the data that is being signed.</param>
      <param name="destination">The buffer to receive the signature.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <returns>
        <see langword="false" /> if <paramref name="destination" /> is not long enough to receive the signature.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.TrySignHash(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Security.Cryptography.DSASignatureFormat,System.Int32@)">
      <summary>Attempts to create the ECDSA signature for the specified hash value in the indicated format into the provided buffer.</summary>
      <param name="hash">The hash value to sign.</param>
      <param name="destination">The buffer to receive the signature.</param>
      <param name="signatureFormat">The encoding format to use for the signature.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the signing operation.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the signature; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.TrySignHashCore(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Security.Cryptography.DSASignatureFormat,System.Int32@)">
      <summary>Attempts to create the ECDSA signature for the specified hash value in the indicated format into the provided buffer.</summary>
      <param name="hash">The hash value to sign.</param>
      <param name="destination">The buffer to receive the signature.</param>
      <param name="signatureFormat">The encoding format to use for the signature.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the signing operation.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the signature; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.VerifyData(System.Byte[],System.Byte[],System.Security.Cryptography.HashAlgorithmName)">
      <summary>Verifies that a digital signature is appropriate for the current key and provided data with a specified hash algorithm.</summary>
      <param name="data">The signed data.</param>
      <param name="signature">The signature data to be verified.</param>
      <param name="hashAlgorithm">The hash algorithm used to create the hash value of the data.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="signature" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <returns>
        <see langword="true" /> if the signature is valid; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.VerifyData(System.Byte[],System.Byte[],System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat)">
      <summary>Verifies that a digital signature is valid for the provided data.</summary>
      <param name="data">The signed data.</param>
      <param name="signature">The signature to verify.</param>
      <param name="hashAlgorithm">The hash algorithm used to hash the data for the verification process.</param>
      <param name="signatureFormat">The encoding format for <paramref name="signature" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> or <paramref name="signature" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" /> has a <see langword="null" /> or empty <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the hashing or verification operation.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid for the provided data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.VerifyData(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Security.Cryptography.HashAlgorithmName)">
      <summary>Verifies that a digital signature is appropriate for the current key and provided portion of data with a specified hash algorithm.</summary>
      <param name="data">The signed data.</param>
      <param name="offset">The starting index at which to compute the hash.</param>
      <param name="count">The number of bytes to hash.</param>
      <param name="signature">The signature data to be verified.</param>
      <param name="hashAlgorithm">The hash algorithm used to create the hash value of the data.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="signature" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than zero.  
  
 -or-  
  
 <paramref name="count" /> is less than zero.  
  
 -or-  
  
 <paramref name="offset" /> + <paramref name="count" /> - 1 results in an index that is beyond the upper bound of <paramref name="data" />.</exception>
      <returns>
        <see langword="true" /> if the signature is valid; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.VerifyData(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat)">
      <summary>Verifies that a digital signature is valid for the provided data.</summary>
      <param name="data">An array that contains the signed data.</param>
      <param name="offset">The starting index of the signed portion of <paramref name="data" />.</param>
      <param name="count">The number of bytes in <paramref name="data" /> that were signed.</param>
      <param name="signature">The signature to verify.</param>
      <param name="hashAlgorithm">The hash algorithm used to hash the data for the verification process.</param>
      <param name="signatureFormat">The encoding format for <paramref name="signature" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> or <paramref name="signature" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.
 
 -or- 
 
 <paramref name="offset" /> is less than zero.
 
 -or- 
 
 <paramref name="count" /> is less than zero.
 
 -or- 
 
 <paramref name="offset" /> + <paramref name="count" /> - 1 results in an index that is beyond the upper bound of <paramref name="data" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" /> has a <see langword="null" /> or empty <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the hashing or verification operation.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid for the provided data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.VerifyData(System.IO.Stream,System.Byte[],System.Security.Cryptography.HashAlgorithmName)">
      <summary>Verifies that a digital signature is appropriate for the current key and provided data with a specified hash algorithm.</summary>
      <param name="data">The signed data.</param>
      <param name="signature">The signature data to be verified.</param>
      <param name="hashAlgorithm">The hash algorithm used to create the hash value of the data.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="signature" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <returns>
        <see langword="true" /> if the signature is valid; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.VerifyData(System.IO.Stream,System.Byte[],System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat)">
      <summary>Verifies that a digital signature is valid for the provided data.</summary>
      <param name="data">The signed data.</param>
      <param name="signature">The signature to verify.</param>
      <param name="hashAlgorithm">The hash algorithm used to hash the data for the verification process.</param>
      <param name="signatureFormat">The encoding format for <paramref name="signature" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> or <paramref name="signature" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" /> has a <see langword="null" /> or empty <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the hashing or verification operation.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid for the provided data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.VerifyData(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Security.Cryptography.HashAlgorithmName)">
      <summary>Verifies that a digital signature is appropriate for the current key and provided data with a specified hash algorithm.</summary>
      <param name="data">The signed data.</param>
      <param name="signature">The signature to be verified.</param>
      <param name="hashAlgorithm">The hash algorithm used to create the hash value of the data.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <returns>
        <see langword="true" /> if the signature is valid; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.VerifyData(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat)">
      <summary>Verifies that a digital signature is valid for the provided data.</summary>
      <param name="data">The signed data.</param>
      <param name="signature">The signature to verify.</param>
      <param name="hashAlgorithm">The hash algorithm used to hash the data for the verification process.</param>
      <param name="signatureFormat">The encoding format for <paramref name="signature" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the hashing or verification operation.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid for the provided data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.VerifyDataCore(System.IO.Stream,System.ReadOnlySpan{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat)">
      <summary>Verifies that a digital signature is valid for the provided data.</summary>
      <param name="data">The signed data.</param>
      <param name="signature">The signature to verify.</param>
      <param name="hashAlgorithm">The hash algorithm used to hash the data for the verification process.</param>
      <param name="signatureFormat">The encoding format for <paramref name="signature" />.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the hashing or verification operation.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid for the provided data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.VerifyDataCore(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.DSASignatureFormat)">
      <summary>Verifies that a digital signature is valid for the provided data.</summary>
      <param name="data">The signed data.</param>
      <param name="signature">The signature to verify.</param>
      <param name="hashAlgorithm">The hash algorithm used to hash the data for the verification process.</param>
      <param name="signatureFormat">The encoding format for <paramref name="signature" />.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the hashing or verification operation.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid for the provided data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.VerifyHash(System.Byte[],System.Byte[])">
      <summary>Verifies that a digital signature is appropriate for the current key and provided data hash.</summary>
      <param name="hash">The hash value of the data to be verified.</param>
      <param name="signature">The digital signature of the data to be verified against the hash value.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="hash" /> or <paramref name="signature" /> is <see langword="null" />.</exception>
      <returns>
        <see langword="true" /> if the signature is valid; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.VerifyHash(System.Byte[],System.Byte[],System.Security.Cryptography.DSASignatureFormat)">
      <summary>Verifies that a digital signature is valid for the provided hash.</summary>
      <param name="hash">The signed hash.</param>
      <param name="signature">The signature to verify.</param>
      <param name="signatureFormat">The encoding format for <paramref name="signature" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="hash" /> or <paramref name="signature" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the verification operation.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid for the provided data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.VerifyHash(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte})">
      <summary>Verifies that a digital signature is appropriate for the current key and provided data hash.</summary>
      <param name="hash">The hash value of the data to be verified.</param>
      <param name="signature">The digital signature of the data to be verified against the hash value.</param>
      <returns>
        <see langword="true" /> if the signature is valid; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.VerifyHash(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Security.Cryptography.DSASignatureFormat)">
      <summary>Verifies that a digital signature is valid for the provided hash.</summary>
      <param name="hash">The signed hash.</param>
      <param name="signature">The signature to verify.</param>
      <param name="signatureFormat">The encoding format for <paramref name="signature" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="signatureFormat" /> is not a known format.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the verification operation.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid for the provided data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.ECDsa.VerifyHashCore(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Security.Cryptography.DSASignatureFormat)">
      <summary>Verifies that a digital signature is valid for the provided hash.</summary>
      <param name="hash">The signed hash.</param>
      <param name="signature">The signature to verify.</param>
      <param name="signatureFormat">The encoding format for <paramref name="signature" />.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred in the verification operation.</exception>
      <returns>
        <see langword="true" /> if the digital signature is valid for the provided data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECDsa.KeyExchangeAlgorithm">
      <summary>Gets the name of the key exchange algorithm.</summary>
      <returns>Always <see langword="null" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.ECDsa.SignatureAlgorithm">
      <summary>Gets the name of the signature algorithm.</summary>
      <returns>The string "ECDsa".</returns>
    </member>
    <member name="T:System.Security.Cryptography.ECParameters">
      <summary>Represents the standard parameters for the elliptic curve cryptography (ECC) algorithm.</summary>
    </member>
    <member name="F:System.Security.Cryptography.ECParameters.Curve">
      <summary>Represents the curve associated with the public key (<see cref="F:System.Security.Cryptography.ECParameters.Q" />) and the optional private key (<see cref="F:System.Security.Cryptography.ECParameters.D" />).</summary>
    </member>
    <member name="F:System.Security.Cryptography.ECParameters.D">
      <summary>Represents the private key <see langword="D" /> for the elliptic curve cryptography (ECC) algorithm, stored in big-endian format.</summary>
    </member>
    <member name="F:System.Security.Cryptography.ECParameters.Q">
      <summary>Represents the public key <see langword="Q" /> for the elliptic curve cryptography (ECC) algorithm.</summary>
    </member>
    <member name="M:System.Security.Cryptography.ECParameters.Validate">
      <summary>Validates the current object.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key or curve parameters are not valid for the current curve type.</exception>
    </member>
    <member name="T:System.Security.Cryptography.ECPoint">
      <summary>Represents a (X,Y) coordinate pair for elliptic curve cryptography (ECC) structures.</summary>
    </member>
    <member name="F:System.Security.Cryptography.ECPoint.X">
      <summary>Represents the X coordinate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.ECPoint.Y">
      <summary>Represents the Y coordinate.</summary>
    </member>
    <member name="T:System.Security.Cryptography.HKDF">
      <summary>RFC5869  HMAC-based Extract-and-Expand Key Derivation (HKDF)</summary>
    </member>
    <member name="M:System.Security.Cryptography.HKDF.DeriveKey(System.Security.Cryptography.HashAlgorithmName,System.Byte[],System.Int32,System.Byte[],System.Byte[])">
      <summary>Performs the key derivation HKDF Expand and Extract functions.</summary>
      <param name="hashAlgorithmName">The hash algorithm used for HMAC operations.</param>
      <param name="ikm">The input keying material.</param>
      <param name="outputLength">The length of the output keying material.</param>
      <param name="salt">The optional salt value (a non-secret random value). If not provided, it defaults to a byte array of the same length as the output of the specified hash algorithm.</param>
      <param name="info">The optional context and application specific information.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ikm" />is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="outputLength" /> is less than 1.</exception>
      <returns>The output keying material.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HKDF.DeriveKey(System.Security.Cryptography.HashAlgorithmName,System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte})">
      <summary>Performs the key derivation HKDF Expand and Extract functions.</summary>
      <param name="hashAlgorithmName">The hash algorithm used for HMAC operations.</param>
      <param name="ikm">The input keying material.</param>
      <param name="output">The output buffer that represents output keying material.</param>
      <param name="salt">The salt value (a non-secret random value).</param>
      <param name="info">The context and application specific information (can be an empty span).</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="ikm" /> is empty, or is larger than the maximum allowed length.</exception>
    </member>
    <member name="M:System.Security.Cryptography.HKDF.Expand(System.Security.Cryptography.HashAlgorithmName,System.Byte[],System.Int32,System.Byte[])">
      <summary>Performs the HKDF-Expand function See section 2.3 of RFC5869.</summary>
      <param name="hashAlgorithmName">The hash algorithm used for HMAC operations.</param>
      <param name="prk">The pseudorandom key that is at least as long as the output byte array of the specified hash algorithm (usually the output from the Extract step).</param>
      <param name="outputLength">The length of the output keying material.</param>
      <param name="info">The optional context and application specific information.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="prk" />is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="outputLength" /> is less than 1.</exception>
      <returns>The output keying material.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HKDF.Expand(System.Security.Cryptography.HashAlgorithmName,System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.ReadOnlySpan{System.Byte})">
      <summary>Performs the HKDF-Expand function. See section 2.3 of RFC5869.</summary>
      <param name="hashAlgorithmName">The hash algorithm used for HMAC operations.</param>
      <param name="prk">The pseudorandom key that is at least as long as as the output byte array of the specified hash algorithm (usually the output from the Extract step).</param>
      <param name="output">The destination buffer to receive the output keying material.</param>
      <param name="info">The context and application specific information (can be an empty span).</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="output" /> is empty, or is larger than the maximum allowed length.</exception>
    </member>
    <member name="M:System.Security.Cryptography.HKDF.Extract(System.Security.Cryptography.HashAlgorithmName,System.Byte[],System.Byte[])">
      <summary>Performs the HKDF-Extract function.
 See section 2.2 of RFC5869.</summary>
      <param name="hashAlgorithmName">The hash algorithm used for HMAC operations.</param>
      <param name="ikm">The input keying material.</param>
      <param name="salt">The optional salt value (a non-secret random value). If not provided it defaults to a byte array of the same length as the output of the specified hash algorithm.</param>
      <returns>The pseudorandom key (prk).</returns>
    </member>
    <member name="M:System.Security.Cryptography.HKDF.Extract(System.Security.Cryptography.HashAlgorithmName,System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Span{System.Byte})">
      <summary>Performs the HKDF-Extract function.
 See section 2.2 of RFC5869.</summary>
      <param name="hashAlgorithmName">The hash algorithm used for HMAC operations.</param>
      <param name="ikm">The input keying material.</param>
      <param name="salt">The salt value (a non-secret random value).</param>
      <param name="prk">The destination buffer to receive the pseudorandom key (prk).</param>
      <returns>The number of bytes written to the <paramref name="prk" /> buffer.</returns>
    </member>
    <member name="T:System.Security.Cryptography.HMACMD5">
      <summary>Computes a Hash-based Message Authentication Code (HMAC) by using the <see cref="T:System.Security.Cryptography.MD5" /> hash function.</summary>
    </member>
    <member name="M:System.Security.Cryptography.HMACMD5.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.HMACMD5" /> class by using a randomly generated key.</summary>
    </member>
    <member name="M:System.Security.Cryptography.HMACMD5.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.HMACMD5" /> class by using the specified key.</summary>
      <param name="key">The secret key for <see cref="T:System.Security.Cryptography.HMACMD5" /> encryption. The key can be any length, but if it is more than 64 bytes long it will be hashed (using SHA-1) to derive a 64-byte key. Therefore, the recommended size of the secret key is 64 bytes.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="key" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.HMACMD5.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Security.Cryptography.HMACMD5" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Security.Cryptography.HMACMD5.HashCore(System.Byte[],System.Int32,System.Int32)">
      <summary>Routes data written to the object into the HMAC algorithm for computing the HMAC.</summary>
      <param name="rgb">The input to compute the HMAC for.</param>
      <param name="ib">The offset into the byte array from which to begin using data.</param>
      <param name="cb">The number of bytes in the byte array to use as data.</param>
    </member>
    <member name="M:System.Security.Cryptography.HMACMD5.HashCore(System.ReadOnlySpan{System.Byte})">
      <summary>Routes data written to the object into the HMAC algorithm for computing the HMAC.</summary>
      <param name="source">The input to compute the HMAC for.</param>
    </member>
    <member name="M:System.Security.Cryptography.HMACMD5.HashData(System.Byte[],System.Byte[])">
      <summary>Computes the HMAC of data using the MD5 algorithm.</summary>
      <param name="key">The HMAC key.</param>
      <param name="source">The data to HMAC.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> or <paramref name="source" /> is <see langword="null" />.</exception>
      <returns>The HMAC of the data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACMD5.HashData(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte})">
      <summary>Computes the HMAC of data using the MD5 algorithm.</summary>
      <param name="key">The HMAC key.</param>
      <param name="source">The data to HMAC.</param>
      <returns>The HMAC of the data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACMD5.HashData(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Span{System.Byte})">
      <summary>Computes the HMAC of data using the MD5 algorithm.</summary>
      <param name="key">The HMAC key.</param>
      <param name="source">The data to HMAC.</param>
      <param name="destination">The buffer to receive the HMAC value.</param>
      <exception cref="T:System.ArgumentException">The buffer in <paramref name="destination" /> is too small to hold the calculated hash size. The MD5 algorithm always produces a 128-bit HMAC, or 16 bytes.</exception>
      <returns>The total number of bytes written to <paramref name="destination" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACMD5.HashFinal">
      <summary>Finalizes the HMAC computation after the last data is processed by the algorithm.</summary>
      <returns>The computed HMAC value in a byte array.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACMD5.Initialize">
      <summary>Resets the hash algorithm to its initial state.</summary>
    </member>
    <member name="M:System.Security.Cryptography.HMACMD5.TryHashData(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to compute the HMAC of data using the MD5 algorithm.</summary>
      <param name="key">The HMAC key.</param>
      <param name="source">The data to HMAC.</param>
      <param name="destination">The buffer to receive the HMAC value.</param>
      <param name="bytesWritten">When this method returns, contains the total number of bytes written into <paramref name="destination" />.</param>
      <returns>
        <see langword="false" /> if <paramref name="destination" /> is too small to hold the calculated hash, <see langword="true" /> otherwise.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACMD5.TryHashFinal(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to finalize the HMAC computation after the last data is processed by the HMAC algorithm.</summary>
      <param name="destination">The buffer to receive the HMAC value.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is long enough to receive the HMAC value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.HMACMD5.Key">
      <summary>Gets or sets the key to use in the HMAC calculation.</summary>
      <returns>The key to use in the HMAC calculation.</returns>
    </member>
    <member name="T:System.Security.Cryptography.HMACSHA1">
      <summary>Computes a Hash-based Message Authentication Code (HMAC) using the <see cref="T:System.Security.Cryptography.SHA1" /> hash function.</summary>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA1.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.HMACSHA1" /> class with a randomly generated key.</summary>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA1.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.HMACSHA1" /> class with the specified key data.</summary>
      <param name="key">The secret key for <see cref="T:System.Security.Cryptography.HMACSHA1" /> encryption. The key can be any length, but if it is more than 64 bytes long it is hashed (using SHA-1) to derive a 64-byte key. Therefore, the recommended size of the secret key is 64 bytes.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="key" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA1.#ctor(System.Byte[],System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.HMACSHA1" /> class with the specified key data and a value that specifies whether to use the managed version of the SHA1 algorithm.</summary>
      <param name="key">The secret key for <see cref="T:System.Security.Cryptography.HMACSHA1" /> encryption. The key can be any length, but if it is more than 64 bytes long, it is hashed (using SHA-1) to derive a 64-byte key. Therefore, the recommended size of the secret key is 64 bytes.</param>
      <param name="useManagedSha1">
        <see langword="true" /> to use the managed implementation of the SHA1 algorithm (the <see cref="T:System.Security.Cryptography.SHA1Managed" /> class); <see langword="false" /> to use the unmanaged implementation (the <see cref="T:System.Security.Cryptography.SHA1CryptoServiceProvider" /> class).</param>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA1.Dispose(System.Boolean)">
      <summary>This member overrides <see cref="M:System.Security.Cryptography.KeyedHashAlgorithm.Dispose(System.Boolean)" />, and more complete documentation might be available in that topic.

Releases the unmanaged resources used by the <see cref="T:System.Security.Cryptography.KeyedHashAlgorithm" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA1.HashCore(System.Byte[],System.Int32,System.Int32)">
      <summary>Routes data written to the object into the SHA1 hash algorithm for computing the Hash-based Message Authentication Code (HMAC).</summary>
      <param name="rgb">The input data.</param>
      <param name="ib">The offset into the byte array from which to begin using data.</param>
      <param name="cb">The number of bytes in the array to use as data.</param>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA1.HashCore(System.ReadOnlySpan{System.Byte})">
      <summary>Routes data written to the object into the HMAC algorithm for computing the HMAC.</summary>
      <param name="source">The input to compute the HMAC for.</param>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA1.HashData(System.Byte[],System.Byte[])">
      <summary>Computes the HMAC of data using the SHA1 algorithm.</summary>
      <param name="key">The HMAC key.</param>
      <param name="source">The data to HMAC.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> or <paramref name="source" /> is <see langword="null" />.</exception>
      <returns>The HMAC of the data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA1.HashData(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte})">
      <summary>Computes the HMAC of data using the SHA1 algorithm.</summary>
      <param name="key">The HMAC key.</param>
      <param name="source">The data to HMAC.</param>
      <returns>The HMAC of the data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA1.HashData(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Span{System.Byte})">
      <summary>Computes the HMAC of data using the SHA1 algorithm.</summary>
      <param name="key">The HMAC key.</param>
      <param name="source">The data to HMAC.</param>
      <param name="destination">The buffer to receive the HMAC value.</param>
      <exception cref="T:System.ArgumentException">The buffer in <paramref name="destination" /> is too small to hold the calculated hash size. The SHA1 algorithm always produces a 160-bit HMAC, or 20 bytes.</exception>
      <returns>The total number of bytes written to <paramref name="destination" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA1.HashFinal">
      <summary>Finalizes the HMAC computation after the last data is processed by the algorithm.</summary>
      <returns>The computed HMAC value in a byte array.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA1.Initialize">
      <summary>Resets the hash algorithm to its initial state.</summary>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA1.TryHashData(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to compute the HMAC of data using the SHA1 algorithm.</summary>
      <param name="key">The HMAC key.</param>
      <param name="source">The data to HMAC.</param>
      <param name="destination">The buffer to receive the HMAC value.</param>
      <param name="bytesWritten">When this method returns, contains the total number of bytes written into <paramref name="destination" />.</param>
      <returns>
        <see langword="false" /> if <paramref name="destination" /> is too small to hold the calculated hash, <see langword="true" /> otherwise.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA1.TryHashFinal(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to finalize the HMAC computation after the last data is processed by the HMAC algorithm.</summary>
      <param name="destination">The buffer to receive the HMAC value.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is long enough to receive the HMAC value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.HMACSHA1.Key">
      <summary>Gets or sets the key to use in the hash algorithm.</summary>
      <returns>The key to use in the hash algorithm.</returns>
    </member>
    <member name="T:System.Security.Cryptography.HMACSHA256">
      <summary>Computes a Hash-based Message Authentication Code (HMAC) by using the <see cref="T:System.Security.Cryptography.SHA256" /> hash function.</summary>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA256.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.HMACSHA256" /> class with a randomly generated key.</summary>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA256.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.HMACSHA256" /> class with the specified key data.</summary>
      <param name="key">The secret key for <see cref="T:System.Security.Cryptography.HMACSHA256" /> encryption. The key can be any length. However, the recommended size is 64 bytes. If the key is more than 64 bytes long, it is hashed (using SHA-256) to derive a 64-byte key. If it is less than 64 bytes long, it is padded to 64 bytes.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="key" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA256.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Security.Cryptography.HMACSHA256" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA256.HashCore(System.Byte[],System.Int32,System.Int32)">
      <summary>Routes data written to the object into the HMAC algorithm for computing the HMAC.</summary>
      <param name="rgb">The input to compute the HMAC for.</param>
      <param name="ib">The offset into the byte array from which to begin using data.</param>
      <param name="cb">The number of bytes in the byte array to use as data.</param>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA256.HashCore(System.ReadOnlySpan{System.Byte})">
      <summary>Routes data written to the object into the HMAC algorithm for computing the HMAC.</summary>
      <param name="source">The input to compute the HMAC for.</param>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA256.HashData(System.Byte[],System.Byte[])">
      <summary>Computes the HMAC of data using the SHA256 algorithm.</summary>
      <param name="key">The HMAC key.</param>
      <param name="source">The data to HMAC.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> or <paramref name="source" /> is <see langword="null" />.</exception>
      <returns>The HMAC of the data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA256.HashData(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte})">
      <summary>Computes the HMAC of data using the SHA256 algorithm.</summary>
      <param name="key">The HMAC key.</param>
      <param name="source">The data to HMAC.</param>
      <returns>The HMAC of the data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA256.HashData(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Span{System.Byte})">
      <summary>Computes the HMAC of data using the SHA256 algorithm.</summary>
      <param name="key">The HMAC key.</param>
      <param name="source">The data to HMAC.</param>
      <param name="destination">The buffer to receive the HMAC value.</param>
      <exception cref="T:System.ArgumentException">The buffer in <paramref name="destination" /> is too small to hold the calculated hash size. The SHA256 algorithm always produces a 256-bit HMAC, or 32 bytes.</exception>
      <returns>The total number of bytes written to <paramref name="destination" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA256.HashFinal">
      <summary>Finalizes the HMAC computation after the last data is processed by the algorithm.</summary>
      <returns>The computed HMAC value in a byte array.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA256.Initialize">
      <summary>Resets the hash algorithm to its initial state.</summary>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA256.TryHashData(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to compute the HMAC of data using the SHA256 algorithm.</summary>
      <param name="key">The HMAC key.</param>
      <param name="source">The data to HMAC.</param>
      <param name="destination">The buffer to receive the HMAC value.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />.</param>
      <returns>
        <see langword="false" /> if <paramref name="destination" /> is too small to hold the calculated hash, <see langword="true" /> otherwise.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA256.TryHashFinal(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to finalize the HMAC computation after the last data is processed by the HMAC algorithm.</summary>
      <param name="destination">The buffer to receive the HMAC value.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is long enough to receive the HMAC value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.HMACSHA256.Key">
      <summary>Gets or sets the key to use in the HMAC calculation.</summary>
      <returns>The key to use in the HMAC calculation.</returns>
    </member>
    <member name="T:System.Security.Cryptography.HMACSHA384">
      <summary>Computes a Hash-based Message Authentication Code (HMAC) using the <see cref="T:System.Security.Cryptography.SHA384" /> hash function.</summary>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA384.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.HMACSHA384" /> class by using a randomly generated key.</summary>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA384.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.HMACSHA384" /> class by using the specified key data.</summary>
      <param name="key">The secret key for <see cref="T:System.Security.Cryptography.HMACSHA384" /> encryption. The key can be any length. However, the recommended size is 128 bytes. If the key is more than 128 bytes long, it is hashed (using SHA-384) to derive a 128-byte key. If it is less than 128 bytes long, it is padded to 128 bytes.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="key" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA384.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Security.Cryptography.HMACSHA384" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA384.HashCore(System.Byte[],System.Int32,System.Int32)">
      <summary>Routes data written to the object into the HMAC algorithm for computing the HMAC.</summary>
      <param name="rgb">The input to compute the HMAC for.</param>
      <param name="ib">The offset into the byte array from which to begin using data.</param>
      <param name="cb">The number of bytes in the byte array to use as data.</param>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA384.HashCore(System.ReadOnlySpan{System.Byte})">
      <summary>Routes data written to the object into the HMAC algorithm for computing the HMAC.</summary>
      <param name="source">The input to compute the HMAC for.</param>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA384.HashData(System.Byte[],System.Byte[])">
      <summary>Computes the HMAC of data using the SHA384 algorithm.</summary>
      <param name="key">The HMAC key.</param>
      <param name="source">The data to HMAC.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> or <paramref name="source" /> is <see langword="null" />.</exception>
      <returns>The HMAC of the data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA384.HashData(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte})">
      <summary>Computes the HMAC of data using the SHA384 algorithm.</summary>
      <param name="key">The HMAC key.</param>
      <param name="source">The data to HMAC.</param>
      <returns>The HMAC of the data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA384.HashData(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Span{System.Byte})">
      <summary>Computes the HMAC of data using the SHA384 algorithm.</summary>
      <param name="key">The HMAC key.</param>
      <param name="source">The data to HMAC.</param>
      <param name="destination">The buffer to receive the HMAC value.</param>
      <exception cref="T:System.ArgumentException">The buffer in <paramref name="destination" /> is too small to hold the calculated hash size. The SHA384 algorithm always produces a 384-bit HMAC, or 48 bytes.</exception>
      <returns>The total number of bytes written to <paramref name="destination" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA384.HashFinal">
      <summary>Finalizes the HMAC computation after the last data is processed by the algorithm.</summary>
      <returns>The computed HMAC value in a byte array.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA384.Initialize">
      <summary>Resets the hash algorithm to its initial state.</summary>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA384.TryHashData(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to compute the HMAC of data using the SHA384 algorithm.</summary>
      <param name="key">The HMAC key.</param>
      <param name="source">The data to HMAC.</param>
      <param name="destination">The buffer to receive the HMAC value.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />.</param>
      <returns>
        <see langword="false" /> if <paramref name="destination" /> is too small to hold the calculated hash, <see langword="true" /> otherwise.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA384.TryHashFinal(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to finalize the HMAC computation after the last data is processed by the HMAC algorithm.</summary>
      <param name="destination">The buffer to receive the HMAC value.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is long enough to receive the HMAC value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.HMACSHA384.Key">
      <summary>Gets or sets the key to use in the HMAC calculation.</summary>
      <returns>The key to use in the HMAC calculation.</returns>
    </member>
    <member name="P:System.Security.Cryptography.HMACSHA384.ProduceLegacyHmacValues">
      <summary>Provides a workaround for the .NET Framework 2.0 implementation of the <see cref="T:System.Security.Cryptography.HMACSHA384" /> algorithm, which is inconsistent with the .NET Framework 2.0 Service Pack 1 implementation of the algorithm.</summary>
      <returns>
        <see langword="true" /> to enable .NET Framework 2.0 Service Pack 1 applications to interact with .NET Framework 2.0 applications; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.HMACSHA512">
      <summary>Computes a Hash-based Message Authentication Code (HMAC) using the <see cref="T:System.Security.Cryptography.SHA512" /> hash function.</summary>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA512.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.HMACSHA512" /> class with a randomly generated key.</summary>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA512.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.HMACSHA512" /> class with the specified key data.</summary>
      <param name="key">The secret key for <see cref="T:System.Security.Cryptography.HMACSHA512" /> encryption. The key can be any length. However, the recommended size is 128 bytes. If the key is more than 128 bytes long, it is hashed (using SHA-512) to derive a 128-byte key. If it is less than 128 bytes long, it is padded to 128 bytes.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="key" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA512.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Security.Cryptography.HMACSHA512" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA512.HashCore(System.Byte[],System.Int32,System.Int32)">
      <summary>Routes data written to the object into the HMAC algorithm for computing the HMAC.</summary>
      <param name="rgb">The input to compute the HMAC for.</param>
      <param name="ib">The offset into the byte array from which to begin using data.</param>
      <param name="cb">The number of bytes in the byte array to use as data.</param>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA512.HashCore(System.ReadOnlySpan{System.Byte})">
      <summary>Routes data written to the object into the HMAC algorithm for computing the HMAC.</summary>
      <param name="source">The input to compute the HMAC for.</param>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA512.HashData(System.Byte[],System.Byte[])">
      <summary>Computes the HMAC of data using the SHA512 algorithm.</summary>
      <param name="key">The HMAC key.</param>
      <param name="source">The data to HMAC.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> or <paramref name="source" /> is <see langword="null" />.</exception>
      <returns>The HMAC of the data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA512.HashData(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte})">
      <summary>Computes the HMAC of data using the SHA512 algorithm.</summary>
      <param name="key">The HMAC key.</param>
      <param name="source">The data to HMAC.</param>
      <returns>The HMAC of the data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA512.HashData(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Span{System.Byte})">
      <summary>Computes the HMAC of data using the SHA512 algorithm.</summary>
      <param name="key">The HMAC key.</param>
      <param name="source">The data to HMAC.</param>
      <param name="destination">The buffer to receive the HMAC value.</param>
      <exception cref="T:System.ArgumentException">The buffer in <paramref name="destination" /> is too small to hold the calculated hash size. The SHA512 algorithm always produces a 512-bit HMAC, or 64 bytes.</exception>
      <returns>The total number of bytes written to <paramref name="destination" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA512.HashFinal">
      <summary>Finalizes the HMAC computation after the last data is processed by the algorithm.</summary>
      <returns>The computed HMAC value in a byte array.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA512.Initialize">
      <summary>Resets the hash algorithm to its initial state.</summary>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA512.TryHashData(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to compute the HMAC of data using the SHA512 algorithm.</summary>
      <param name="key">The HMAC key.</param>
      <param name="source">The data to HMAC.</param>
      <param name="destination">The buffer to receive the HMAC value.</param>
      <param name="bytesWritten">When this method returns, contains the total number of bytes written into <paramref name="destination" />.</param>
      <returns>
        <see langword="false" /> if <paramref name="destination" /> is too small to hold the calculated hash, <see langword="true" /> otherwise.</returns>
    </member>
    <member name="M:System.Security.Cryptography.HMACSHA512.TryHashFinal(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to finalize the HMAC computation after the last data is processed by the HMAC algorithm.</summary>
      <param name="destination">The buffer to receive the HMAC value.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is long enough to receive the HMAC value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.HMACSHA512.Key">
      <summary>Gets or sets the key to use in the HMAC calculation.</summary>
      <returns>The key to use in the HMAC calculation.</returns>
    </member>
    <member name="P:System.Security.Cryptography.HMACSHA512.ProduceLegacyHmacValues">
      <summary>Provides a workaround for the .NET Framework 2.0 implementation of the <see cref="T:System.Security.Cryptography.HMACSHA512" /> algorithm, which is inconsistent with the .NET Framework 2.0 Service Pack 1 implementation.</summary>
      <returns>
        <see langword="true" /> to enable .NET Framework 2.0 Service Pack 1 applications to interact with .NET Framework 2.0 applications; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.IncrementalHash">
      <summary>Provides support for computing a hash or Hash-based Message Authentication Code (HMAC) value incrementally across several segments.</summary>
    </member>
    <member name="M:System.Security.Cryptography.IncrementalHash.AppendData(System.Byte[])">
      <summary>Appends the specified data to the data already processed in the hash or HMAC.</summary>
      <param name="data">The data to process.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Security.Cryptography.IncrementalHash" /> object has already been disposed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.IncrementalHash.AppendData(System.Byte[],System.Int32,System.Int32)">
      <summary>Appends the specified number of bytes from the specified data, starting at the specified offset, to the data already processed in the hash or Hash-based Message Authentication Code (HMAC).</summary>
      <param name="data">The data to process.</param>
      <param name="offset">The offset into the byte array from which to begin using data.</param>
      <param name="count">The number of bytes to use from <paramref name="data" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> or <paramref name="offset" /> is negative.  
  
 -or-  
  
 <paramref name="count" /> is larger than the length of <paramref name="data" />.</exception>
      <exception cref="T:System.ArgumentException">The sum of <paramref name="offset" /> and <paramref name="count" /> is larger than the data length.</exception>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Security.Cryptography.IncrementalHash" /> object has already been disposed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.IncrementalHash.AppendData(System.ReadOnlySpan{System.Byte})">
      <summary>Appends the specified data to the data already processed in the hash or Hash-based Message Authentication Code (HMAC).</summary>
      <param name="data">The data to process.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Security.Cryptography.IncrementalHash" /> object has already been disposed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.IncrementalHash.CreateHash(System.Security.Cryptography.HashAlgorithmName)">
      <summary>Creates an <see cref="T:System.Security.Cryptography.IncrementalHash" /> for the specified algorithm.</summary>
      <param name="hashAlgorithm">The name of the hash algorithm to perform.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or an empty string.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="hashAlgorithm" /> is not a known hash algorithm.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.IncrementalHash" /> instance ready to compute the hash algorithm specified by <paramref name="hashAlgorithm" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.IncrementalHash.CreateHMAC(System.Security.Cryptography.HashAlgorithmName,System.Byte[])">
      <summary>Creates an <see cref="T:System.Security.Cryptography.IncrementalHash" /> for the Hash-based Message Authentication Code (HMAC) algorithm using the specified hash algorithm and key.</summary>
      <param name="hashAlgorithm">The name of the hash algorithm to perform within the HMAC.</param>
      <param name="key">The secret key for the HMAC. The key can be of any length, but a key longer than the output size of the specified hash algorithm will be hashed to derive a correctly-sized key. Therefore, the recommended size of the secret key is the output size of the specified hash algorithm.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or an empty string.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="hashAlgorithm" /> is not a known hash algorithm.</exception>
      <returns>An instance of the <see cref="T:System.Security.Cryptography.IncrementalHash" /> class ready to compute the specified hash algorithm.</returns>
    </member>
    <member name="M:System.Security.Cryptography.IncrementalHash.CreateHMAC(System.Security.Cryptography.HashAlgorithmName,System.ReadOnlySpan{System.Byte})">
      <summary>Create an <see cref="T:System.Security.Cryptography.IncrementalHash" /> for the Hash-based Message Authentication Code (HMAC) algorithm utilizing the hash algorithm specified by <paramref name="hashAlgorithm" />, and a key specified by <paramref name="key" />.</summary>
      <param name="hashAlgorithm">The name of the hash algorithm to perform within the HMAC.</param>
      <param name="key">The secret key for the HMAC. The key can be any length, but a key longer than the output size of the hash algorithm specified by <paramref name="hashAlgorithm" /> will be hashed (using the algorithm specified by <paramref name="hashAlgorithm" />) to derive a correctly-sized key. Therefore, the recommended size of the secret key is the output size of the hash specified by <paramref name="hashAlgorithm" />.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or the empty string.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="hashAlgorithm" /> is not a known hash algorithm.</exception>
      <returns>A hash instance to compute the hash algorithm specified by <paramref name="hashAlgorithm" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.IncrementalHash.Dispose">
      <summary>Releases the resources used by the current instance of the <see cref="T:System.Security.Cryptography.IncrementalHash" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.IncrementalHash.GetCurrentHash">
      <summary>Retrieves the hash or Hash-based Message Authentication Code (HMAC) for the data accumulated from prior calls to the <see cref="M:System.Security.Cryptography.IncrementalHash.AppendData(System.ReadOnlySpan{System.Byte})" /> methods, without resetting the object to its initial state.</summary>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed.</exception>
      <returns>The computed hash or HMAC.</returns>
    </member>
    <member name="M:System.Security.Cryptography.IncrementalHash.GetCurrentHash(System.Span{System.Byte})">
      <summary>Retrieves the hash or Hash-based Message Authentication Code (HMAC) for the data accumulated from prior calls to the <see cref="M:System.Security.Cryptography.IncrementalHash.AppendData(System.ReadOnlySpan{System.Byte})" /> methods, without resetting the object to its initial state.</summary>
      <param name="destination">The buffer to receive the hash or HMAC value.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destination" /> has a <see cref="P:System.Span`1.Length" /> value less than <see cref="P:System.Security.Cryptography.IncrementalHash.HashLengthInBytes" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed.</exception>
      <returns>The number of bytes written to <paramref name="destination" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.IncrementalHash.GetHashAndReset">
      <summary>Retrieves the hash or Hash-based Message Authentication Code (HMAC) for the data accumulated from prior calls to the <see cref="Overload:System.Security.Cryptography.IncrementalHash.AppendData" /> methods,  and resets the object to its initial state.</summary>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Security.Cryptography.IncrementalHash" /> object has already been disposed.</exception>
      <returns>The computed hash or HMAC.</returns>
    </member>
    <member name="M:System.Security.Cryptography.IncrementalHash.GetHashAndReset(System.Span{System.Byte})">
      <summary>Retrieves the hash or Hash-based Message Authentication Code (HMAC) for the data accumulated from prior calls to the <see cref="M:System.Security.Cryptography.IncrementalHash.AppendData(System.ReadOnlySpan{System.Byte})" /> methods, and resets the object to its initial state.</summary>
      <param name="destination">The buffer to receive the hash or HMAC value.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="destination" /> has a <see cref="P:System.Span`1.Length" /> value less than <see cref="P:System.Security.Cryptography.IncrementalHash.HashLengthInBytes" />.</exception>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed.</exception>
      <returns>The number of bytes written to <paramref name="destination" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.IncrementalHash.TryGetCurrentHash(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to retrieve the hash or Hash-based Message Authentication Code (HMAC) for the data accumulated from prior calls to the <see cref="M:System.Security.Cryptography.IncrementalHash.AppendData(System.ReadOnlySpan{System.Byte})" /> methods, without resetting the object to its initial state.</summary>
      <param name="destination">The buffer to receive the hash or HMAC value.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />.
              This parameter is treated as uninitialized.</param>
      <exception cref="T:System.ObjectDisposedException">The object has already been disposed.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is long enough to receive the hash or HMAC value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.IncrementalHash.TryGetHashAndReset(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to finalize the hash computation after the last data is processed by the hash algorithm.</summary>
      <param name="destination">The buffer to receive the hash or Hash-Based Message Authentication Code (HMAC) value.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.ObjectDisposedException">The <see cref="T:System.Security.Cryptography.IncrementalHash" /> object has already been disposed.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is long enough to receive the hash or HMAC value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.IncrementalHash.AlgorithmName">
      <summary>Gets the name of the algorithm being performed. HMAC algorithms are prepended with "HMAC" to distinguish them from an unkeyed digest.</summary>
      <returns>The name of the algorithm being performed.</returns>
    </member>
    <member name="P:System.Security.Cryptography.IncrementalHash.HashLengthInBytes">
      <summary>Gets the output size of this hash or HMAC algorithm, in bytes.</summary>
      <returns>The output size of this hash or HMAC algorithm, in bytes.</returns>
    </member>
    <member name="T:System.Security.Cryptography.MaskGenerationMethod">
      <summary>Represents the abstract class from which all mask generator algorithms must derive.</summary>
    </member>
    <member name="M:System.Security.Cryptography.MaskGenerationMethod.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.MaskGenerationMethod" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.MaskGenerationMethod.GenerateMask(System.Byte[],System.Int32)">
      <summary>When overridden in a derived class, generates a mask with the specified length using the specified random seed.</summary>
      <param name="rgbSeed">The random seed to use to compute the mask.</param>
      <param name="cbReturn">The length of the generated mask in bytes.</param>
      <returns>A randomly generated mask whose length is equal to the <paramref name="cbReturn" /> parameter.</returns>
    </member>
    <member name="T:System.Security.Cryptography.MD5">
      <summary>Represents the abstract class from which all implementations of the <see cref="T:System.Security.Cryptography.MD5" /> hash algorithm inherit.</summary>
    </member>
    <member name="M:System.Security.Cryptography.MD5.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.Security.Cryptography.MD5" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.MD5.Create">
      <summary>Creates an instance of the default implementation of the <see cref="T:System.Security.Cryptography.MD5" /> hash algorithm.</summary>
      <exception cref="T:System.Reflection.TargetInvocationException">The algorithm was used with Federal Information Processing Standards (FIPS) mode enabled, but is not FIPS compatible.</exception>
      <returns>A new instance of the <see cref="T:System.Security.Cryptography.MD5" /> hash algorithm.</returns>
    </member>
    <member name="M:System.Security.Cryptography.MD5.Create(System.String)">
      <summary>Creates an instance of the specified implementation of the <see cref="T:System.Security.Cryptography.MD5" /> hash algorithm.</summary>
      <param name="algName">The name of the specific implementation of <see cref="T:System.Security.Cryptography.MD5" /> to use.</param>
      <exception cref="T:System.Reflection.TargetInvocationException">The algorithm described by the <paramref name="algName" /> parameter was used with Federal Information Processing Standards (FIPS) mode enabled, but is not FIPS compatible.</exception>
      <returns>A new instance of the specified implementation of <see cref="T:System.Security.Cryptography.MD5" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.MD5.HashData(System.Byte[])">
      <summary>Computes the hash of data using the MD5 algorithm.</summary>
      <param name="source">The data to hash.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <returns>The hash of the data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.MD5.HashData(System.ReadOnlySpan{System.Byte})">
      <summary>Computes the hash of data using the MD5 algorithm.</summary>
      <param name="source">The data to hash.</param>
      <returns>The hash of the data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.MD5.HashData(System.ReadOnlySpan{System.Byte},System.Span{System.Byte})">
      <summary>Computes the hash of data using the MD5 algorithm.</summary>
      <param name="source">The data to hash.</param>
      <param name="destination">The buffer to receive the hash value.</param>
      <exception cref="T:System.ArgumentException">The buffer in <paramref name="destination" /> is too small to hold the calculated hash size. The MD5 algorithm always produces a 128-bit hash, or 16 bytes.</exception>
      <returns>The total number of bytes written to <paramref name="destination" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.MD5.TryHashData(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to compute the hash of data using the MD5 algorithm.</summary>
      <param name="source">The data to hash.</param>
      <param name="destination">The buffer to receive the hash value.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />.</param>
      <returns>
        <see langword="false" /> if <paramref name="destination" /> is too small to hold the calculated hash, <see langword="true" /> otherwise.</returns>
    </member>
    <member name="T:System.Security.Cryptography.PKCS1MaskGenerationMethod">
      <summary>Computes masks according to PKCS #1 for use by key exchange algorithms.</summary>
    </member>
    <member name="M:System.Security.Cryptography.PKCS1MaskGenerationMethod.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.PKCS1MaskGenerationMethod" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.PKCS1MaskGenerationMethod.GenerateMask(System.Byte[],System.Int32)">
      <summary>Generates and returns a mask from the specified random seed of the specified length.</summary>
      <param name="rgbSeed">The random seed to use for computing the mask.</param>
      <param name="cbReturn">The length of the generated mask in bytes.</param>
      <returns>A randomly generated mask whose length is equal to the <paramref name="cbReturn" /> parameter.</returns>
    </member>
    <member name="P:System.Security.Cryptography.PKCS1MaskGenerationMethod.HashName">
      <summary>Gets or sets the name of the hash algorithm type to use for generating the mask.</summary>
      <returns>The name of the type that implements the hash algorithm to use for computing the mask.</returns>
    </member>
    <member name="T:System.Security.Cryptography.RandomNumberGenerator">
      <summary>Represents the abstract class from which all implementations of cryptographic random number generators derive.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RandomNumberGenerator.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.Security.Cryptography.RandomNumberGenerator" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RandomNumberGenerator.Create">
      <summary>Creates an instance of the default implementation of a cryptographic random number generator that can be used to generate random data.</summary>
      <returns>A new instance of a cryptographic random number generator.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RandomNumberGenerator.Create(System.String)">
      <summary>Creates an instance of the specified implementation of a cryptographic random number generator.</summary>
      <param name="rngName">The name of the random number generator implementation to use.</param>
      <returns>A new instance of a cryptographic random number generator.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RandomNumberGenerator.Dispose">
      <summary>When overridden in a derived class, releases all resources used by the current instance of the <see cref="T:System.Security.Cryptography.RandomNumberGenerator" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RandomNumberGenerator.Dispose(System.Boolean)">
      <summary>When overridden in a derived class, releases the unmanaged resources used by the <see cref="T:System.Security.Cryptography.RandomNumberGenerator" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Security.Cryptography.RandomNumberGenerator.Fill(System.Span{System.Byte})">
      <summary>Fills a span with cryptographically strong random bytes.</summary>
      <param name="data">The span to fill with cryptographically strong random bytes.</param>
    </member>
    <member name="M:System.Security.Cryptography.RandomNumberGenerator.GetBytes(System.Byte[])">
      <summary>When overridden in a derived class, fills an array of bytes with a cryptographically strong random sequence of values.</summary>
      <param name="data">The array to fill with cryptographically strong random bytes.</param>
    </member>
    <member name="M:System.Security.Cryptography.RandomNumberGenerator.GetBytes(System.Byte[],System.Int32,System.Int32)">
      <summary>Fills the specified byte array with a cryptographically strong random sequence of values.</summary>
      <param name="data">The array to fill with cryptographically strong random bytes.</param>
      <param name="offset">The index of the array to start the fill operation.</param>
      <param name="count">The number of bytes to fill.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> or <paramref name="count" /> is less than 0</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="offset" /> plus <paramref name="count" /> exceeds the length of <paramref name="data" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.RandomNumberGenerator.GetBytes(System.Int32)">
      <summary>Creates an array of bytes with a cryptographically strong random sequence of values.</summary>
      <param name="count">The number of bytes of random values to create.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" /> is less than zero.</exception>
      <returns>An array populated with cryptographically strong random values.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RandomNumberGenerator.GetBytes(System.Span{System.Byte})">
      <summary>Fills a span with cryptographically strong random bytes.</summary>
      <param name="data">The span to fill with cryptographically strong random bytes.</param>
    </member>
    <member name="M:System.Security.Cryptography.RandomNumberGenerator.GetInt32(System.Int32)">
      <summary>Generates a random integer between 0 (inclusive) and a specified exclusive upper bound using a cryptographically strong random number generator.</summary>
      <param name="toExclusive">The exclusive upper bound of the random range.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="toExclusive" /> parameter is less than or equal to 0.</exception>
      <returns>A random integer between 0 (inclusive) and <paramref name="toExclusive" /> (exclusive).</returns>
    </member>
    <member name="M:System.Security.Cryptography.RandomNumberGenerator.GetInt32(System.Int32,System.Int32)">
      <summary>Generates a random integer between a specified inclusive lower bound and a specified exclusive upper bound using a cryptographically strong random number generator.</summary>
      <param name="fromInclusive">The inclusive lower bound of the random range.</param>
      <param name="toExclusive">The exclusive upper bound of the random range.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="toExclusive" /> parameter is less than or equal to the <paramref name="fromInclusive" /> parameter.</exception>
      <returns>A random integer between <paramref name="fromInclusive" /> (inclusive) and <paramref name="toExclusive" /> (exclusive).</returns>
    </member>
    <member name="M:System.Security.Cryptography.RandomNumberGenerator.GetNonZeroBytes(System.Byte[])">
      <summary>When overridden in a derived class, fills an array of bytes with a cryptographically strong random sequence of nonzero values.</summary>
      <param name="data">The array to fill with cryptographically strong random nonzero bytes.</param>
    </member>
    <member name="M:System.Security.Cryptography.RandomNumberGenerator.GetNonZeroBytes(System.Span{System.Byte})">
      <summary>Fills a byte span with a cryptographically strong random sequence of nonzero values.</summary>
      <param name="data">The span to fill with cryptographically strong random nonzero bytes.</param>
    </member>
    <member name="T:System.Security.Cryptography.RC2">
      <summary>Represents the base class from which all implementations of the <see cref="T:System.Security.Cryptography.RC2" /> algorithm must derive.</summary>
    </member>
    <member name="F:System.Security.Cryptography.RC2.EffectiveKeySizeValue">
      <summary>Represents the effective size of the secret key used by the <see cref="T:System.Security.Cryptography.RC2" /> algorithm in bits.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RC2.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.Security.Cryptography.RC2" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RC2.Create">
      <summary>Creates an instance of a cryptographic object to perform the <see cref="T:System.Security.Cryptography.RC2" /> algorithm.</summary>
      <exception cref="T:System.Reflection.TargetInvocationException">The algorithm was used with Federal Information Processing Standards (FIPS) mode enabled, but is not FIPS compatible.</exception>
      <returns>An instance of a cryptographic object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RC2.Create(System.String)">
      <summary>Creates an instance of a cryptographic object to perform the specified implementation of the <see cref="T:System.Security.Cryptography.RC2" /> algorithm.</summary>
      <param name="AlgName">The name of the specific implementation of <see cref="T:System.Security.Cryptography.RC2" /> to use.</param>
      <exception cref="T:System.Reflection.TargetInvocationException">The algorithm described by the <paramref name="algName" /> parameter was used with Federal Information Processing Standards (FIPS) mode enabled, but is not FIPS compatible.</exception>
      <returns>An instance of a cryptographic object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RC2.EffectiveKeySize">
      <summary>Gets or sets the effective size of the secret key used by the <see cref="T:System.Security.Cryptography.RC2" /> algorithm in bits.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The effective key size is invalid.</exception>
      <returns>The effective key size used by the <see cref="T:System.Security.Cryptography.RC2" /> algorithm.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RC2.KeySize">
      <summary>Gets or sets the size of the secret key used by the <see cref="T:System.Security.Cryptography.RC2" /> algorithm in bits.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The value for the RC2 key size is less than the effective key size value.</exception>
      <returns>The size of the secret key used by the <see cref="T:System.Security.Cryptography.RC2" /> algorithm.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Rfc2898DeriveBytes">
      <summary>Implements password-based key derivation functionality, PBKDF2, by using a pseudo-random number generator based on <see cref="T:System.Security.Cryptography.HMACSHA1" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Rfc2898DeriveBytes.#ctor(System.Byte[],System.Byte[],System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Rfc2898DeriveBytes" /> class using a password, a salt, and number of iterations to derive the key.</summary>
      <param name="password">The password used to derive the key.</param>
      <param name="salt">The key salt used to derive the key.</param>
      <param name="iterations">The number of iterations for the operation.</param>
      <exception cref="T:System.ArgumentException">The specified salt size is smaller than 8 bytes or the iteration count is less than 1.</exception>
      <exception cref="T:System.ArgumentNullException">The password or salt is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Rfc2898DeriveBytes.#ctor(System.Byte[],System.Byte[],System.Int32,System.Security.Cryptography.HashAlgorithmName)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Rfc2898DeriveBytes" /> class using the specified password, salt, number of iterations and the hash algorithm name to derive the key.</summary>
      <param name="password">The password to use to derive the key.</param>
      <param name="salt">The key salt to use to derive the key.</param>
      <param name="iterations">The number of iterations for the operation.</param>
      <param name="hashAlgorithm">The hash algorithm to use to derive the key.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="saltSize" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> property of <paramref name="hashAlgorithm" /> is either <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Hash algorithm name is invalid.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Rfc2898DeriveBytes.#ctor(System.String,System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Rfc2898DeriveBytes" /> class using a password and salt to derive the key.</summary>
      <param name="password">The password used to derive the key.</param>
      <param name="salt">The key salt used to derive the key.</param>
      <exception cref="T:System.ArgumentException">The specified salt size is smaller than 8 bytes or the iteration count is less than 1.</exception>
      <exception cref="T:System.ArgumentNullException">The password or salt is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Rfc2898DeriveBytes.#ctor(System.String,System.Byte[],System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Rfc2898DeriveBytes" /> class using a password, a salt, and number of iterations to derive the key.</summary>
      <param name="password">The password used to derive the key.</param>
      <param name="salt">The key salt used to derive the key.</param>
      <param name="iterations">The number of iterations for the operation.</param>
      <exception cref="T:System.ArgumentException">The specified salt size is smaller than 8 bytes or the iteration count is less than 1.</exception>
      <exception cref="T:System.ArgumentNullException">The password or salt is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Rfc2898DeriveBytes.#ctor(System.String,System.Byte[],System.Int32,System.Security.Cryptography.HashAlgorithmName)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Rfc2898DeriveBytes" /> class using the specified password, salt, number of iterations and the hash algorithm name to derive the key.</summary>
      <param name="password">The password to use to derive the key.</param>
      <param name="salt">The key salt to use to derive the key.</param>
      <param name="iterations">The number of iterations for the operation.</param>
      <param name="hashAlgorithm">The hash algorithm to use to derive the key.</param>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> property of <paramref name="hashAlgorithm" /> is either <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Hash algorithm name is invalid.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Rfc2898DeriveBytes.#ctor(System.String,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Rfc2898DeriveBytes" /> class using the password and salt size to derive the key.</summary>
      <param name="password">The password used to derive the key.</param>
      <param name="saltSize">The size of the random salt that you want the class to generate.</param>
      <exception cref="T:System.ArgumentException">The specified salt size is smaller than 8 bytes.</exception>
      <exception cref="T:System.ArgumentNullException">The password or salt is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Rfc2898DeriveBytes.#ctor(System.String,System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Rfc2898DeriveBytes" /> class using a password, a salt size, and number of iterations to derive the key.</summary>
      <param name="password">The password used to derive the key.</param>
      <param name="saltSize">The size of the random salt that you want the class to generate.</param>
      <param name="iterations">The number of iterations for the operation.</param>
      <exception cref="T:System.ArgumentException">The specified salt size is smaller than 8 bytes or the iteration count is less than 1.</exception>
      <exception cref="T:System.ArgumentNullException">The password or salt is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="iterations" /> is out of range. This parameter requires a non-negative number.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Rfc2898DeriveBytes.#ctor(System.String,System.Int32,System.Int32,System.Security.Cryptography.HashAlgorithmName)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.Rfc2898DeriveBytes" /> class using the specified password, salt size, number of iterations and the hash algorithm name to derive the key.</summary>
      <param name="password">The password to use to derive the key.</param>
      <param name="saltSize">The size of the random salt that you want the class to generate.</param>
      <param name="iterations">The number of iterations for the operation.</param>
      <param name="hashAlgorithm">The hash algorithm to use to derive the key.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="saltSize" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> property of <paramref name="hashAlgorithm" /> is either <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Hash algorithm name is invalid.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Rfc2898DeriveBytes.CryptDeriveKey(System.String,System.String,System.Int32,System.Byte[])">
      <summary>Derives a cryptographic key from the <see cref="T:System.Security.Cryptography.Rfc2898DeriveBytes" /> object.</summary>
      <param name="algname">The algorithm name for which to derive the key.</param>
      <param name="alghashname">The hash algorithm name to use to derive the key.</param>
      <param name="keySize">The size of the key, in bits, to derive.</param>
      <param name="rgbIV">The initialization vector (IV) to use to derive the key.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="keySize" /> parameter is incorrect.  
  
 -or-  
  
 The cryptographic service provider (CSP) cannot be acquired.  
  
 -or-  
  
 The <paramref name="algname" /> parameter is not a valid algorithm name.  
  
 -or-  
  
 The <paramref name="alghashname" /> parameter is not a valid hash algorithm name.</exception>
      <returns>The derived key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Rfc2898DeriveBytes.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Security.Cryptography.Rfc2898DeriveBytes" /> class and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Security.Cryptography.Rfc2898DeriveBytes.GetBytes(System.Int32)">
      <summary>Returns the pseudo-random key for this object.</summary>
      <param name="cb">The number of pseudo-random key bytes to generate.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="cb" /> is out of range. This parameter requires a non-negative number.</exception>
      <returns>A byte array filled with pseudo-random key bytes.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Rfc2898DeriveBytes.Pbkdf2(System.Byte[],System.Byte[],System.Int32,System.Security.Cryptography.HashAlgorithmName,System.Int32)">
      <summary>Creates a PBKDF2 derived key from password bytes.</summary>
      <param name="password">The password used to derive the key.</param>
      <param name="salt">The key salt used to derive the key.</param>
      <param name="iterations">The number of iterations for the operation.</param>
      <param name="hashAlgorithm">The hash algorithm to use to derive the key.</param>
      <param name="outputLength">The size of key to derive.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="password" /> or <paramref name="salt" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="outputLength" /> is not zero or a positive value.

-or-

<paramref name="iterations" /> is not a positive value.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" /> has a <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> that is empty or <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="hashAlgorithm" /> is an unsupported hash algorithm. Supported algorithms are <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA1" />, <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA256" />, <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA384" />, and <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA512" />.</exception>
      <returns>A byte array containing the created PBKDF2 derived key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Rfc2898DeriveBytes.Pbkdf2(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Int32,System.Security.Cryptography.HashAlgorithmName,System.Int32)">
      <summary>Creates a PBKDF2 derived key from password bytes.</summary>
      <param name="password">The password used to derive the key.</param>
      <param name="salt">The key salt used to derive the key.</param>
      <param name="iterations">The number of iterations for the operation.</param>
      <param name="hashAlgorithm">The hash algorithm to use to derive the key.</param>
      <param name="outputLength">The size of key to derive.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="outputLength" /> is not zero or a positive value.

-or-

<paramref name="iterations" /> is not a positive value.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" /> has a <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> that is empty or <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="hashAlgorithm" /> is an unsupported hash algorithm. Supported algorithms are <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA1" />, <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA256" />, <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA384" />, and <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA512" />.</exception>
      <returns>A byte array containing the created PBKDF2 derived key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Rfc2898DeriveBytes.Pbkdf2(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32,System.Security.Cryptography.HashAlgorithmName)">
      <summary>Fills a buffer with a PBKDF2 derived key.</summary>
      <param name="password">The password used to derive the key.</param>
      <param name="salt">The key salt used to derive the key.</param>
      <param name="destination">The buffer to fill with a derived key.</param>
      <param name="iterations">The number of iterations for the operation.</param>
      <param name="hashAlgorithm">The hash algorithm to use to derive the key.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="iterations" /> is not a positive value.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" /> has a <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> that is empty or <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="hashAlgorithm" /> is an unsupported hash algorithm. Supported algorithms are <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA1" />, <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA256" />, <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA384" />, and <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA512" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Rfc2898DeriveBytes.Pbkdf2(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Byte},System.Int32,System.Security.Cryptography.HashAlgorithmName,System.Int32)">
      <summary>Creates a PBKDF2 derived key from a password.</summary>
      <param name="password">The password used to derive the key.</param>
      <param name="salt">The key salt used to derive the key.</param>
      <param name="iterations">The number of iterations for the operation.</param>
      <param name="hashAlgorithm">The hash algorithm to use to derive the key.</param>
      <param name="outputLength">The size of key to derive.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="outputLength" /> is not zero or a positive value.

-or-

<paramref name="iterations" /> is not a positive value.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" /> has a <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> that is empty or <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="hashAlgorithm" /> is an unsupported hash algorithm. Supported algorithms are <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA1" />, <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA256" />, <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA384" />, and <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA512" />.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">
        <paramref name="password" /> contains text that cannot be converted to UTF8.</exception>
      <returns>A byte array of length <paramref name="outputLength" /> that is filled with pseudo-random key bytes.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Rfc2898DeriveBytes.Pbkdf2(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32,System.Security.Cryptography.HashAlgorithmName)">
      <summary>Fills a buffer with a PBKDF2 derived key.</summary>
      <param name="password">The password used to derive the key.</param>
      <param name="salt">The key salt used to derive the key.</param>
      <param name="destination">The buffer to fill with a derived key.</param>
      <param name="iterations">The number of iterations for the operation.</param>
      <param name="hashAlgorithm">The hash algorithm to use to derive the key.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="iterations" /> is not a positive value.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" /> has a <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> that is empty or <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="hashAlgorithm" /> is an unsupported hash algorithm. Supported algorithms are <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA1" />, <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA256" />, <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA384" />, and <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA512" />.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">
        <paramref name="password" /> contains text that cannot be converted to UTF8.</exception>
    </member>
    <member name="M:System.Security.Cryptography.Rfc2898DeriveBytes.Pbkdf2(System.String,System.Byte[],System.Int32,System.Security.Cryptography.HashAlgorithmName,System.Int32)">
      <summary>Creates a PBKDF2 derived key from a password.</summary>
      <param name="password">The password used to derive the key.</param>
      <param name="salt">The key salt used to derive the key.</param>
      <param name="iterations">The number of iterations for the operation.</param>
      <param name="hashAlgorithm">The hash algorithm to use to derive the key.</param>
      <param name="outputLength">The size of key to derive.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="password" /> or <paramref name="salt" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="outputLength" /> is not zero or a positive value.

-or-

<paramref name="iterations" /> is not a positive value.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" /> has a <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> that is empty or <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="hashAlgorithm" /> is an unsupported hash algorithm. Supported algorithms are <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA1" />, <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA256" />, <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA384" />, and <see cref="P:System.Security.Cryptography.HashAlgorithmName.SHA512" />.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">
        <paramref name="password" /> contains text that cannot be converted to UTF8.</exception>
      <returns>A byte array of length <paramref name="outputLength" /> that is filled with pseudo-random key bytes.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Rfc2898DeriveBytes.Reset">
      <summary>Resets the state of the operation.</summary>
    </member>
    <member name="P:System.Security.Cryptography.Rfc2898DeriveBytes.HashAlgorithm">
      <summary>Gets the hash algorithm used for byte derivation.</summary>
      <returns>The hash algorithm used for byte derivation.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Rfc2898DeriveBytes.IterationCount">
      <summary>Gets or sets the number of iterations for the operation.</summary>
      <exception cref="T:System.ArgumentOutOfRangeException">The number of iterations is less than 1.</exception>
      <returns>The number of iterations for the operation.</returns>
    </member>
    <member name="P:System.Security.Cryptography.Rfc2898DeriveBytes.Salt">
      <summary>Gets or sets the key salt value for the operation.</summary>
      <exception cref="T:System.ArgumentException">The specified salt size is smaller than 8 bytes.</exception>
      <exception cref="T:System.ArgumentNullException">The salt is <see langword="null" />.</exception>
      <returns>The key salt value for the operation.</returns>
    </member>
    <member name="T:System.Security.Cryptography.Rijndael">
      <summary>Represents the base class from which all implementations of the <see cref="T:System.Security.Cryptography.Rijndael" /> symmetric encryption algorithm must inherit.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Rijndael.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.Security.Cryptography.Rijndael" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.Rijndael.Create">
      <summary>Creates a cryptographic object to perform the <see cref="T:System.Security.Cryptography.Rijndael" /> algorithm.</summary>
      <exception cref="T:System.Reflection.TargetInvocationException">The algorithm was used with Federal Information Processing Standards (FIPS) mode enabled, but is not FIPS compatible.</exception>
      <returns>A cryptographic object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.Rijndael.Create(System.String)">
      <summary>Creates a cryptographic object to perform the specified implementation of the <see cref="T:System.Security.Cryptography.Rijndael" /> algorithm.</summary>
      <param name="algName">The name of the specific implementation of <see cref="T:System.Security.Cryptography.Rijndael" /> to create.</param>
      <exception cref="T:System.Reflection.TargetInvocationException">The algorithm described by the <paramref name="algName" /> parameter was used with Federal Information Processing Standards (FIPS) mode enabled, but is not FIPS compatible.</exception>
      <returns>A cryptographic object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.RijndaelManaged">
      <summary>Accesses the managed version of the <see cref="T:System.Security.Cryptography.Rijndael" /> algorithm. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RijndaelManaged.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.RijndaelManaged" /> class.</summary>
      <exception cref="T:System.InvalidOperationException">This class is not compliant with the FIPS algorithm.</exception>
    </member>
    <member name="M:System.Security.Cryptography.RijndaelManaged.CreateDecryptor">
      <summary>Creates a symmetric decryptor object with the current <see cref="P:System.Security.Cryptography.SymmetricAlgorithm.Key" /> property and initialization vector (<see cref="P:System.Security.Cryptography.SymmetricAlgorithm.IV" />).</summary>
      <returns>A symmetric decryptor object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RijndaelManaged.CreateDecryptor(System.Byte[],System.Byte[])">
      <summary>Creates a symmetric <see cref="T:System.Security.Cryptography.Rijndael" /> decryptor object with the specified <see cref="P:System.Security.Cryptography.SymmetricAlgorithm.Key" /> and initialization vector (<see cref="P:System.Security.Cryptography.SymmetricAlgorithm.IV" />).</summary>
      <param name="rgbKey">The secret key to be used for the symmetric algorithm. The key size must be 128, 192, or 256 bits.</param>
      <param name="rgbIV">The IV to be used for the symmetric algorithm.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rgbKey" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The <paramref name="rgbIV" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The value of the <see cref="P:System.Security.Cryptography.SymmetricAlgorithm.Mode" /> property is not <see cref="F:System.Security.Cryptography.CipherMode.ECB" />, <see cref="F:System.Security.Cryptography.CipherMode.CBC" />, or <see cref="F:System.Security.Cryptography.CipherMode.CFB" />.</exception>
      <returns>A symmetric <see cref="T:System.Security.Cryptography.Rijndael" /> decryptor object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RijndaelManaged.CreateEncryptor">
      <summary>Creates a symmetric encryptor object with the current <see cref="P:System.Security.Cryptography.SymmetricAlgorithm.Key" /> property and initialization vector (<see cref="P:System.Security.Cryptography.SymmetricAlgorithm.IV" />).</summary>
      <returns>A symmetric encryptor object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RijndaelManaged.CreateEncryptor(System.Byte[],System.Byte[])">
      <summary>Creates a symmetric <see cref="T:System.Security.Cryptography.Rijndael" /> encryptor object with the specified <see cref="P:System.Security.Cryptography.SymmetricAlgorithm.Key" /> and initialization vector (<see cref="P:System.Security.Cryptography.SymmetricAlgorithm.IV" />).</summary>
      <param name="rgbKey">The secret key to be used for the symmetric algorithm. The key size must be 128, 192, or 256 bits.</param>
      <param name="rgbIV">The IV to be used for the symmetric algorithm.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rgbKey" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The <paramref name="rgbIV" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The value of the <see cref="P:System.Security.Cryptography.SymmetricAlgorithm.Mode" /> property is not <see cref="F:System.Security.Cryptography.CipherMode.ECB" />, <see cref="F:System.Security.Cryptography.CipherMode.CBC" />, or <see cref="F:System.Security.Cryptography.CipherMode.CFB" />.</exception>
      <returns>A symmetric <see cref="T:System.Security.Cryptography.Rijndael" /> encryptor object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RijndaelManaged.GenerateIV">
      <summary>Generates a random initialization vector (<see cref="P:System.Security.Cryptography.SymmetricAlgorithm.IV" />) to be used for the algorithm.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RijndaelManaged.GenerateKey">
      <summary>Generates a random <see cref="P:System.Security.Cryptography.SymmetricAlgorithm.Key" /> to be used for the algorithm.</summary>
    </member>
    <member name="P:System.Security.Cryptography.RijndaelManaged.BlockSize">
      <summary>Gets or sets the block size, in bits, of the cryptographic operation.</summary>
      <returns>The block size, in bits, of the cryptographic operation. The default is 128 bits.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RijndaelManaged.FeedbackSize">
      <summary>Gets or sets the feedback size, in bits, of the cryptographic operation for the Cipher Feedback (CFB) and Output Feedback (OFB) cipher modes.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The feedback size is larger than the block size.</exception>
      <returns>The feedback size in bits.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RijndaelManaged.IV">
      <summary>Gets or sets the initialization vector (IV) to use for the symmetric algorithm.</summary>
      <returns>The initialization vector (IV) to use for the symmetric algorithm.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RijndaelManaged.Key">
      <summary>Gets or sets the secret key used for the symmetric algorithm.</summary>
      <returns>The secret key used for the symmetric algorithm.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RijndaelManaged.KeySize">
      <summary>Gets or sets the size, in bits, of the secret key used for the symmetric algorithm.</summary>
      <returns>The size, in bits, of the secret key used for the symmetric algorithm. The default is 256 bits.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RijndaelManaged.LegalKeySizes">
      <summary>Gets the key sizes, in bits, that are supported by the symmetric algorithm.</summary>
      <returns>The key sizes, in bits, that are supported by the symmetric algorithm.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RijndaelManaged.Mode">
      <summary>Gets or sets the mode for operation of the symmetric algorithm.</summary>
      <returns>The mode for operation of the symmetric algorithm. The default is <see cref="F:System.Security.Cryptography.CipherMode.CBC" /></returns>
    </member>
    <member name="P:System.Security.Cryptography.RijndaelManaged.Padding">
      <summary>Gets or sets the padding mode used in the symmetric algorithm.</summary>
      <returns>The padding mode used in the symmetric algorithm. The default is <see cref="F:System.Security.Cryptography.PaddingMode.PKCS7" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.RSA">
      <summary>Represents the base class from which all implementations of the <see cref="T:System.Security.Cryptography.RSA" /> algorithm inherit.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RSA.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.RSA" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RSA.Create">
      <summary>Creates an instance of the default implementation of the <see cref="T:System.Security.Cryptography.RSA" /> algorithm.</summary>
      <returns>A new instance of the default implementation of <see cref="T:System.Security.Cryptography.RSA" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.Create(System.Int32)">
      <summary>Creates a new ephemeral RSA key with the specified key size.</summary>
      <param name="keySizeInBits">The key size, in bits.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="keySizeInBits" /> is not supported by the default implementation.</exception>
      <returns>A new ephemeral RSA key with the specified key size.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.Create(System.Security.Cryptography.RSAParameters)">
      <summary>Creates a new ephemeral RSA key with the specified RSA key parameters.</summary>
      <param name="parameters">The parameters for the <see cref="T:System.Security.Cryptography.RSA" /> algorithm.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="parameters" /> does not represent a valid RSA key.</exception>
      <returns>A new ephemeral RSA key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.Create(System.String)">
      <summary>Creates an instance of the specified implementation of <see cref="T:System.Security.Cryptography.RSA" />.</summary>
      <param name="algName">The name of the implementation of <see cref="T:System.Security.Cryptography.RSA" /> to use.</param>
      <returns>A new instance of the specified implementation of <see cref="T:System.Security.Cryptography.RSA" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.Decrypt(System.Byte[],System.Security.Cryptography.RSAEncryptionPadding)">
      <summary>When overridden in a derived class, decrypts the input data using the specified padding mode.</summary>
      <param name="data">The data to decrypt.</param>
      <param name="padding">The padding mode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> or <paramref name="padding" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotImplementedException">A derived class must override this method.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="padding" /> is unknown, or not supported by this implementation.

-or-

The length of <paramref name="data" /> is not equal to the number of bytes for <see cref="P:System.Security.Cryptography.AsymmetricAlgorithm.KeySize" />.

-or-

This instance represents only a public key. 

-or-

The decryption operation failed.</exception>
      <returns>The decrypted data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.DecryptValue(System.Byte[])">
      <summary>When overridden in a derived class, decrypts the input data using the private key.</summary>
      <param name="rgb">The cipher text to be decrypted.</param>
      <exception cref="T:System.NotSupportedException">This method call is not supported. This exception is thrown starting with the .NET Framework 4.6.</exception>
      <returns>The resulting decryption of the <paramref name="rgb" /> parameter in plain text.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.Encrypt(System.Byte[],System.Security.Cryptography.RSAEncryptionPadding)">
      <summary>When overridden in a derived class, encrypts the input data using the specified padding mode.</summary>
      <param name="data">The data to encrypt.</param>
      <param name="padding">The padding mode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> or <paramref name="padding" /> is <see langword="null" />.</exception>
      <exception cref="T:System.NotImplementedException">A derived class must override this method.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="padding" /> is unknown, or not supported by this implementation.

-or-

The length of <paramref name="data" /> is too long for the combination of <see cref="P:System.Security.Cryptography.AsymmetricAlgorithm.KeySize" /> and the selected padding.

-or-

The encryption operation failed.</exception>
      <returns>The encrypted data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.EncryptValue(System.Byte[])">
      <summary>When overridden in a derived class, encrypts the input data using the public key.</summary>
      <param name="rgb">The plain text to be encrypted.</param>
      <exception cref="T:System.NotSupportedException">This method call is not supported. This exception is thrown starting with the .NET Framework 4.6.</exception>
      <returns>The resulting encryption of the <paramref name="rgb" /> parameter as cipher text.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.ExportParameters(System.Boolean)">
      <summary>When overridden in a derived class, exports the <see cref="T:System.Security.Cryptography.RSAParameters" />.</summary>
      <param name="includePrivateParameters">
        <see langword="true" /> to include private parameters; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The parameters could not be exported.</exception>
      <returns>The parameters for <see cref="T:System.Security.Cryptography.RSA" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.ExportRSAPrivateKey">
      <summary>Exports the current key in the PKCS#1 RSAPrivateKey format.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.</exception>
      <returns>A byte array containing the PKCS#1 RSAPrivateKey representation of this key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.ExportRSAPublicKey">
      <summary>Exports the public-key portion of the current key in the PKCS#1 RSAPublicKey format.</summary>
      <returns>A byte array containing the PKCS#1 RSAPublicKey representation of this key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.FromXmlString(System.String)">
      <summary>Initializes an <see cref="T:System.Security.Cryptography.RSA" /> object from the key information from an XML string.</summary>
      <param name="xmlString">The XML string containing <see cref="T:System.Security.Cryptography.RSA" /> key information.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="xmlString" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The format of the <paramref name="xmlString" /> parameter is not valid.</exception>
      <exception cref="T:System.PlatformNotSupportedException">.NET Core and .NET 5+ only: In all cases.</exception>
    </member>
    <member name="M:System.Security.Cryptography.RSA.HashData(System.Byte[],System.Int32,System.Int32,System.Security.Cryptography.HashAlgorithmName)">
      <summary>When overridden in a derived class, computes the hash value of a specified portion of a byte array by using a specified hashing algorithm.</summary>
      <param name="data">The data to be hashed.</param>
      <param name="offset">The index of the first byte in <paramref name="data" /> that is to be hashed.</param>
      <param name="count">The number of bytes to hash.</param>
      <param name="hashAlgorithm">The algorithm to use in hash the data.</param>
      <exception cref="T:System.NotImplementedException">A derived class must override this method.</exception>
      <returns>The hashed data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.HashData(System.IO.Stream,System.Security.Cryptography.HashAlgorithmName)">
      <summary>When overridden in a derived class, computes the hash value of a specified binary stream by using a specified hashing algorithm.</summary>
      <param name="data">The binary stream to hash.</param>
      <param name="hashAlgorithm">The hash algorithm.</param>
      <exception cref="T:System.NotImplementedException">A derived class must override this method.</exception>
      <returns>The hashed data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.ImportEncryptedPkcs8PrivateKey(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Imports the public/private keypair from a PKCS#8 EncryptedPrivateKeyInfo structure after decrypting with a byte-based password, replacing the keys for this object.</summary>
      <param name="passwordBytes">The bytes to use as a password when decrypting the key material.</param>
      <param name="source">The bytes of a PKCS#8 EncryptedPrivateKeyInfo structure in the ASN.1-BER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The password is incorrect.

-or-

The contents of <paramref name="source" /> indicate the Key Derivation Function (KDF) to apply is the legacy PKCS#12 KDF, which requires <see cref="T:System.Char" />-based passwords.

-or-

The contents of <paramref name="source" /> do not represent an ASN.1-BER-encoded PKCS#8 EncryptedPrivateKeyInfo structure.

-or-

The contents of <paramref name="source" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

-or-

The contents of <paramref name="source" /> represent the key in a format that is not supported.

-or-

The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.RSA.ImportEncryptedPkcs8PrivateKey(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Imports the public/private keypair from a PKCS#8 EncryptedPrivateKeyInfo structure after decrypting with a char-based password, replacing the keys for this object.</summary>
      <param name="password">The password to use for decrypting the key material.</param>
      <param name="source">The bytes of a PKCS#8 EncryptedPrivateKeyInfo structure in the ASN.1-BER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The password is incorrect.

-or-

The contents of <paramref name="source" /> do not represent an ASN.1-BER-encoded PKCS#8 EncryptedPrivateKeyInfo structure.

-or-

The contents of <paramref name="source" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

-or-

The contents of <paramref name="source" /> represent the key in a format that is not supported.

-or-

The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.RSA.ImportFromEncryptedPem(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Byte})">
      <summary>Imports an encrypted RFC 7468 PEM-encoded private key, replacing the keys for this object.</summary>
      <param name="input">The PEM text of the encrypted key to import.</param>
      <param name="passwordBytes">The bytes to use as a password when decrypting the key material.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="input" /> does not contain a PEM-encoded key with a recognized label.

-or-

<paramref name="input" /> contains multiple PEM-encoded keys with a recognized label.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
The password is incorrect.

-or-

The base-64 decoded contents of the PEM text from <paramref name="input" /> do not represent an ASN.1-BER-encoded PKCS#8 EncryptedPrivateKeyInfo structure.

-or-

The base-64 decoded contents of the PEM text from <paramref name="input" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

-or-

The base-64 decoded contents of the PEM text from <paramref name="input" /> represent the key in a format that is not supported.

-or-

The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.RSA.ImportFromEncryptedPem(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char})">
      <summary>Imports an encrypted RFC 7468 PEM-encoded private key, replacing the keys for this object.</summary>
      <param name="input">The PEM text of the encrypted key to import.</param>
      <param name="password">The password to use for decrypting the key material.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="input" /> does not contain a PEM-encoded key with a recognized label.

-or-

<paramref name="input" /> contains multiple PEM-encoded keys with a recognized label.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
The password is incorrect.

-or-

The base-64 decoded contents of the PEM text from <paramref name="input" /> do not represent an ASN.1-BER-encoded PKCS#8 EncryptedPrivateKeyInfo structure.

-or-

The base-64 decoded contents of the PEM text from <paramref name="input" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

-or-

The base-64 decoded contents of the PEM text from <paramref name="input" /> represent the key in a format that is not supported.

-or-

The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.RSA.ImportFromPem(System.ReadOnlySpan{System.Char})">
      <summary>Imports an RFC 7468 PEM-encoded key, replacing the keys for this object.</summary>
      <param name="input">The PEM text of the key to import.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="input" /> does not contain a PEM-encoded key with a recognized label.

-or-

<paramref name="input" /> contains multiple PEM-encoded keys with a recognized label.

-or-

<paramref name="input" /> contains an encrypted PEM-encoded key.</exception>
    </member>
    <member name="M:System.Security.Cryptography.RSA.ImportParameters(System.Security.Cryptography.RSAParameters)">
      <summary>When overridden in a derived class, imports the specified <see cref="T:System.Security.Cryptography.RSAParameters" />.</summary>
      <param name="parameters">The parameters for <see cref="T:System.Security.Cryptography.RSA" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.RSA.ImportPkcs8PrivateKey(System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Imports the public/private keypair from a PKCS#8 PrivateKeyInfo structure after decryption, replacing the keys for this object.</summary>
      <param name="source">The bytes of a PKCS#8 PrivateKeyInfo structure in the ASN.1-BER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The contents of <paramref name="source" /> do not represent an ASN.1-BER-encoded PKCS#8 PrivateKeyInfo structure.

-or-

The contents of <paramref name="source" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

-or-

The contents of <paramref name="source" /> represent the key in a format that is not supported.

-or-

The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.RSA.ImportRSAPrivateKey(System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Imports the public/private keypair from a PKCS#1 RSAPrivateKey structure after decryption, replacing the keys for this object.</summary>
      <param name="source">The bytes of a PKCS#1 RSAPrivateKey structure in the ASN.1-BER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The contents of <paramref name="source" /> do not represent an ASN.1-BER-encoded PKCS#1 RSAPrivateKey structure.

-or-

The key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.RSA.ImportRSAPublicKey(System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Imports the public key from a PKCS#1 RSAPublicKey structure after decryption, replacing the keys for this object.</summary>
      <param name="source">The bytes of a PKCS#1 RSAPublicKey structure in the ASN.1-BER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The contents of <paramref name="source" /> do not represent an ASN.1-BER-encoded PKCS#1 RSAPublicKey structure.

-or-

The key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.RSA.ImportSubjectPublicKeyInfo(System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Imports the public key from an X.509 SubjectPublicKeyInfo structure after decryption, replacing the keys for this object.</summary>
      <param name="source">The bytes of an X.509 SubjectPublicKeyInfo structure in the ASN.1-DER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The contents of <paramref name="source" /> do not represent an ASN.1-DER-encoded X.509 SubjectPublicKeyInfo structure.

-or-

The contents of <paramref name="source" /> indicate the key is for an algorithm other than the algorithm represented by this instance.

-or-

The contents of <paramref name="source" /> represent the key in a format that is not supported.

-or-

The algorithm-specific key import failed.</exception>
    </member>
    <member name="M:System.Security.Cryptography.RSA.SignData(System.Byte[],System.Int32,System.Int32,System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.RSASignaturePadding)">
      <summary>Computes the hash value of a portion of the specified byte array using the specified hash algorithm and padding mode, and signs the resulting hash value.</summary>
      <param name="data">The input data to hash and sign.</param>
      <param name="offset">The offset into the array at which to begin using data.</param>
      <param name="count">The number of bytes in the array to use as data.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <param name="padding">The padding mode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="padding" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than zero.  
  
 -or-  
  
 <paramref name="count" /> is less than zero.  
  
 -or-  
  
 <paramref name="offset" /> + <paramref name="count" /> - 1 results in an index that is beyond the upper bound of <paramref name="data" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="padding" /> is unknown, or not supported by this implementation.

-or-

This instance represents only a public key.

-or-

An error occurred creating the signature.</exception>
      <returns>The RSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.SignData(System.Byte[],System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.RSASignaturePadding)">
      <summary>Computes the hash value of the specified byte array using the specified hash algorithm and padding mode, and signs the resulting hash value.</summary>
      <param name="data">The input data to hash and sign.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <param name="padding">The padding mode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="padding" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="padding" /> is unknown, or not supported by this implementation.

-or-

This instance represents only a public key.

-or-

An error occurred creating the signature.</exception>
      <returns>The RSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.SignData(System.IO.Stream,System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.RSASignaturePadding)">
      <summary>Computes the hash value of the specified stream using the specified hash algorithm and padding mode, and signs the resulting hash value.</summary>
      <param name="data">The input stream to hash and sign.</param>
      <param name="hashAlgorithm">The hash algorithm to use to create the hash value.</param>
      <param name="padding">The padding mode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="padding" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="padding" /> is unknown, or not supported by this implementation.

-or-

This instance represents only a public key.

-or-

An error occurred creating the signature.</exception>
      <returns>The RSA signature for the specified data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.SignHash(System.Byte[],System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.RSASignaturePadding)">
      <summary>When overridden in a derived class, computes the signature for the specified hash value using the specified padding.</summary>
      <param name="hash">The hash value of the data to be signed.</param>
      <param name="hashAlgorithm">The hash algorithm used to create the hash value of the data.</param>
      <param name="padding">The padding.</param>
      <exception cref="T:System.NotImplementedException">A derived class must override this method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> or <paramref name="padding" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="padding" /> is unknown, or not supported by this implementation.

-or-

This instance represents only a public key.

-or-

An error occurred creating the signature.</exception>
      <returns>The RSA signature for the specified hash value.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.ToXmlString(System.Boolean)">
      <summary>Creates and returns an XML string containing the key of the current <see cref="T:System.Security.Cryptography.RSA" /> object.</summary>
      <param name="includePrivateParameters">
        <see langword="true" /> to include a public and private RSA key; <see langword="false" /> to include only the public key.</param>
      <exception cref="T:System.PlatformNotSupportedException">.NET Core and .NET 5+ only: In all cases.</exception>
      <returns>An XML string containing the key of the current <see cref="T:System.Security.Cryptography.RSA" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.TryDecrypt(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Security.Cryptography.RSAEncryptionPadding,System.Int32@)">
      <summary>Attempts to decrypt the input data using the specified padding mode, writing the result into a provided buffer.</summary>
      <param name="data">The data to decrypt.</param>
      <param name="destination">The buffer to receive the decrypted data.</param>
      <param name="padding">The padding mode.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="padding" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="padding" /> is unknown, or not supported by this implementation.

-or-

The length of <paramref name="data" /> is not equal to the number of bytes for <see cref="P:System.Security.Cryptography.AsymmetricAlgorithm.KeySize" />.

-or-

This instance represents only a public key. 

-or-

The decryption operation failed.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is long enough to receive the decrypted data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.TryEncrypt(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Security.Cryptography.RSAEncryptionPadding,System.Int32@)">
      <summary>Attempts to encrypt the input data with a specified padding mode into a provided buffer.</summary>
      <param name="data">The data to encrypt.</param>
      <param name="destination">The buffer to receive the encrypted data.</param>
      <param name="padding">The padding mode.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="padding" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="padding" /> is unknown, or not supported by this implementation.

-or-

The length of <paramref name="data" /> is too long for the combination of <see cref="P:System.Security.Cryptography.AsymmetricAlgorithm.KeySize" /> and the selected padding.

-or-

The encryption operation failed.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is long enough to receive the encrypted data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.TryExportEncryptedPkcs8PrivateKey(System.ReadOnlySpan{System.Byte},System.Security.Cryptography.PbeParameters,System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to export the current key in the PKCS#8 EncryptedPrivateKeyInfo format into a provided buffer, using a byte-based password.</summary>
      <param name="passwordBytes">The bytes to use as a password when encrypting the key material.</param>
      <param name="pbeParameters">The password-based encryption (PBE) parameters to use when encrypting the key material.</param>
      <param name="destination">The byte span to receive the PKCS#8 EncryptedPrivateKeyInfo data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.

-or-

<paramref name="pbeParameters" /> indicates that <see cref="F:System.Security.Cryptography.PbeEncryptionAlgorithm.TripleDes3KeyPkcs12" /> should be used, which requires <see cref="T:System.Char" />-based passwords.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.TryExportEncryptedPkcs8PrivateKey(System.ReadOnlySpan{System.Char},System.Security.Cryptography.PbeParameters,System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to export the current key in the PKCS#8 EncryptedPrivateKeyInfo format into a provided buffer, using a char-based password.</summary>
      <param name="password">The password to use when encrypting the key material.</param>
      <param name="pbeParameters">The password-based encryption (PBE) parameters to use when encrypting the key material.</param>
      <param name="destination">The byte span to receive the PKCS#8 EncryptedPrivateKeyInfo data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.TryExportPkcs8PrivateKey(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to export the current key in the PKCS#8 PrivateKeyInfo format into a provided buffer.</summary>
      <param name="destination">The byte span to receive the PKCS#8 PrivateKeyInfo data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.TryExportRSAPrivateKey(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to export the current key in the PKCS#1 RSAPrivateKey format into a provided buffer.</summary>
      <param name="destination">The byte span to receive the PKCS#1 RSAPrivateKey data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.TryExportRSAPublicKey(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to export the current key in the PKCS#1 RSAPublicKey format into a provided buffer.</summary>
      <param name="destination">The byte span to receive the PKCS#1 RSAPublicKey data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.TryExportSubjectPublicKeyInfo(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to export the current key in the X.509 SubjectPublicKeyInfo format into a provided buffer.</summary>
      <param name="destination">The byte span to receive the X.509 SubjectPublicKeyInfo data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key could not be exported.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.TryHashData(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Int32@)">
      <summary>Attempts to compute the hash of the provided data by using the specified algorithm, writing the results into a provided buffer.</summary>
      <param name="data">The data to be hashed.</param>
      <param name="destination">The buffer to receive the hash value.</param>
      <param name="hashAlgorithm">The algorithm to use in hash the data.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is long enough to receive the hash value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.TrySignData(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.RSASignaturePadding,System.Int32@)">
      <summary>Attempts to hash the provided data with the specified algorithm and sign the hash with the current key, writing the signature into a provided buffer.</summary>
      <param name="data">The input data to hash and sign.</param>
      <param name="destination">The buffer to receive the RSA signature.</param>
      <param name="hashAlgorithm">The hash algorithm used to create the hash value of the data.</param>
      <param name="padding">The padding mode.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="padding" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">This instance represents only a public key.

-or-

An error occurred creating the signature.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is long enough to receive the RSA signature; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.TrySignHash(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.RSASignaturePadding,System.Int32@)">
      <summary>Attempts to sign the hash with the current key, writing the signature into a provided buffer.</summary>
      <param name="hash">The hash value of the data to be signed.</param>
      <param name="destination">The buffer to receive the RSA signature.</param>
      <param name="hashAlgorithm">The hash algorithm used to create the hash value of the data.</param>
      <param name="padding">The padding.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="padding" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="padding" /> is unknown, or not supported by this implementation.

-or-

This instance represents only a public key.

-or-

An error occurred creating the signature.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is long enough to receive the RSA signature; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.VerifyData(System.Byte[],System.Byte[],System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.RSASignaturePadding)">
      <summary>Verifies that a digital signature is valid by calculating the hash value of the specified data using the specified hash algorithm and padding, and comparing it to the provided signature.</summary>
      <param name="data">The signed data.</param>
      <param name="signature">The signature data to be verified.</param>
      <param name="hashAlgorithm">The hash algorithm used to create the hash value of the data.</param>
      <param name="padding">The padding mode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="signature" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="padding" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="padding" /> is unknown, or not supported by this implementation.</exception>
      <returns>
        <see langword="true" /> if the signature is valid; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.VerifyData(System.Byte[],System.Int32,System.Int32,System.Byte[],System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.RSASignaturePadding)">
      <summary>Verifies that a digital signature is valid by calculating the hash value of the data in a portion of a byte array using the specified hash algorithm and padding, and comparing it to the provided signature.</summary>
      <param name="data">The signed data.</param>
      <param name="offset">The starting index at which to compute the hash.</param>
      <param name="count">The number of bytes to hash.</param>
      <param name="signature">The signature data to be verified.</param>
      <param name="hashAlgorithm">The hash algorithm used to create the hash value of the data.</param>
      <param name="padding">The padding mode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="signature" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="padding" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> is less than zero.  
  
 -or-  
  
 <paramref name="count" /> is less than zero.  
  
 -or-  
  
 <paramref name="offset" /> + <paramref name="count" /> - 1 results in an index that is beyond the upper bound of <paramref name="data" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="padding" /> is unknown, or not supported by this implementation.</exception>
      <returns>
        <see langword="true" /> if the signature is valid; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.VerifyData(System.IO.Stream,System.Byte[],System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.RSASignaturePadding)">
      <summary>Verifies that a digital signature is valid by calculating the hash value of the specified stream using the specified hash algorithm and padding, and comparing it to the provided signature.</summary>
      <param name="data">The signed data.</param>
      <param name="signature">The signature data to be verified.</param>
      <param name="hashAlgorithm">The hash algorithm used to create the hash value of the data.</param>
      <param name="padding">The padding mode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="data" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="signature" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="padding" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="padding" /> is unknown, or not supported by this implementation.</exception>
      <returns>
        <see langword="true" /> if the signature is valid; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.VerifyData(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.RSASignaturePadding)">
      <summary>Verifies that a digital signature is valid by calculating the hash value of the specified data using the specified hash algorithm and padding, and comparing it to the provided signature.</summary>
      <param name="data">The signed data.</param>
      <param name="signature">The signature data to be verified.</param>
      <param name="hashAlgorithm">The hash algorithm used to create the hash value of the data.</param>
      <param name="padding">The padding mode.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="padding" /> is unknown, or not supported by this implementation.</exception>
      <returns>
        <see langword="true" /> if the signature is valid; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.VerifyHash(System.Byte[],System.Byte[],System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.RSASignaturePadding)">
      <summary>Verifies that a digital signature is valid by determining the hash value in the signature using the specified hash algorithm and padding, and comparing it to the provided hash value.</summary>
      <param name="hash">The hash value of the signed data.</param>
      <param name="signature">The signature data to be verified.</param>
      <param name="hashAlgorithm">The hash algorithm used to create the hash value.</param>
      <param name="padding">The padding mode.</param>
      <exception cref="T:System.NotImplementedException">A derived class must override this method.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="hash" /> or <paramref name="padding" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="padding" /> is unknown, or not supported by this implementation.</exception>
      <returns>
        <see langword="true" /> if the signature is valid; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSA.VerifyHash(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Byte},System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.RSASignaturePadding)">
      <summary>Verifies that a digital signature is valid by determining the hash value in the signature using the specified hash algorithm and padding, and comparing it to the provided hash value.</summary>
      <param name="hash">The hash value of the signed data.</param>
      <param name="signature">The signature data to be verified.</param>
      <param name="hashAlgorithm">The hash algorithm used to create the hash value.</param>
      <param name="padding">The padding mode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="hash" /> or <paramref name="padding" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="padding" /> is unknown, or not supported by this implementation.</exception>
      <returns>
        <see langword="true" /> if the signature is valid; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RSA.KeyExchangeAlgorithm">
      <summary>Gets the name of the key exchange algorithm available with this implementation of <see cref="T:System.Security.Cryptography.RSA" />.</summary>
      <returns>Returns "RSA".</returns>
    </member>
    <member name="P:System.Security.Cryptography.RSA.SignatureAlgorithm">
      <summary>Gets the name of the signature algorithm available with this implementation of <see cref="T:System.Security.Cryptography.RSA" />.</summary>
      <returns>Returns "RSA".</returns>
    </member>
    <member name="T:System.Security.Cryptography.RSAEncryptionPadding">
      <summary>Specifies the padding mode and parameters to use with RSA encryption or decryption operations.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RSAEncryptionPadding.CreateOaep(System.Security.Cryptography.HashAlgorithmName)">
      <summary>Creates a new <see cref="T:System.Security.Cryptography.RSAEncryptionPadding" /> instance whose <see cref="P:System.Security.Cryptography.RSAEncryptionPadding.Mode" /> is <see cref="F:System.Security.Cryptography.RSAEncryptionPaddingMode.Oaep" /> with the given hash algorithm.</summary>
      <param name="hashAlgorithm">The hash algorithm.</param>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> property of <paramref name="hashAlgorithm" /> is either <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
      <returns>An object whose mode is <see cref="P:System.Security.Cryptography.RSAEncryptionPadding.Mode" /> is <see cref="F:System.Security.Cryptography.RSAEncryptionPaddingMode.Oaep" /> with the hash algorithm specified by <paramref name="hashAlgorithm" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSAEncryptionPadding.Equals(System.Object)">
      <summary>Determines whether the current instance is equal to the specified object.</summary>
      <param name="obj">The object to compare.</param>
      <returns>
        <see langword="true" /> if <paramref name="obj" /> is equal to the current instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSAEncryptionPadding.Equals(System.Security.Cryptography.RSAEncryptionPadding)">
      <summary>Determines whether the current instance is equal to the specified <see cref="T:System.Security.Cryptography.RSAEncryptionPadding" /> object.</summary>
      <param name="other">The object to compare.</param>
      <returns>
        <see langword="true" /> if <paramref name="other" /> is equal to the current instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSAEncryptionPadding.GetHashCode">
      <summary>Returns the hash code of this <see cref="T:System.Security.Cryptography.RSAEncryptionPadding" /> object.</summary>
      <returns>The hash code of this instance.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSAEncryptionPadding.op_Equality(System.Security.Cryptography.RSAEncryptionPadding,System.Security.Cryptography.RSAEncryptionPadding)">
      <summary>Indicates whether two specified <see cref="T:System.Security.Cryptography.RSAEncryptionPadding" /> objects are equal.</summary>
      <param name="left">The first object to compare.</param>
      <param name="right">The second object to compare.</param>
      <returns>
        <see langword="true" /> if <see langword="left" /> and <see langword="right" /> are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSAEncryptionPadding.op_Inequality(System.Security.Cryptography.RSAEncryptionPadding,System.Security.Cryptography.RSAEncryptionPadding)">
      <summary>Indicates whether two specified <see cref="T:System.Security.Cryptography.RSAEncryptionPadding" /> objects are unequal.</summary>
      <param name="left">The first object to compare.</param>
      <param name="right">The second object to compare.</param>
      <returns>
        <see langword="true" /> if <see langword="left" /> and <see langword="right" /> are not equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSAEncryptionPadding.ToString">
      <summary>Returns the string representation of the current <see cref="T:System.Security.Cryptography.RSAEncryptionPadding" /> instance.</summary>
      <returns>The string representation of the current object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RSAEncryptionPadding.Mode">
      <summary>Gets the padding mode represented by this <see cref="T:System.Security.Cryptography.RSAEncryptionPadding" /> instance.</summary>
      <returns>A padding mode.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RSAEncryptionPadding.OaepHashAlgorithm">
      <summary>Gets the hash algorithm used in conjunction with the <see cref="F:System.Security.Cryptography.RSAEncryptionPaddingMode.Oaep" /> padding mode.</summary>
      <returns>The hash algorithm.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RSAEncryptionPadding.OaepSHA1">
      <summary>Gets an object that represents the Optimal Asymmetric Encryption Padding (OAEP) encryption standard with a SHA1 hash algorithm.</summary>
      <returns>An object that represents the OAEP encryption standard with a SHA1 hash algorithm.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RSAEncryptionPadding.OaepSHA256">
      <summary>Gets an object that represents the Optimal Asymmetric Encryption Padding (OAEP) encryption standard with a SHA256 hash algorithm.</summary>
      <returns>An object that represents the OAEP encryption standard with a SHA256 hash algorithm.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RSAEncryptionPadding.OaepSHA384">
      <summary>Gets an object that represents the Optimal Asymmetric Encryption Padding (OAEP) encryption standard with a SHA-384 hash algorithm.</summary>
      <returns>An object that represents the OAEP encryption standard with a SHA384 hash algorithm.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RSAEncryptionPadding.OaepSHA512">
      <summary>Gets an object that represents the Optimal Asymmetric Encryption Padding (OAEP) encryption standard with a SHA512 hash algorithm.</summary>
      <returns>An object that represents the OAEP encryption standard with a SHA512 hash algorithm.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RSAEncryptionPadding.Pkcs1">
      <summary>Gets an object that represents the PKCS #1 encryption standard.</summary>
      <returns>An object that represents the PKCS #1 encryption standard.</returns>
    </member>
    <member name="T:System.Security.Cryptography.RSAEncryptionPaddingMode">
      <summary>Specifies the padding mode to use with RSA encryption or decryption operations.</summary>
    </member>
    <member name="F:System.Security.Cryptography.RSAEncryptionPaddingMode.Oaep">
      <summary>Optimal Asymmetric Encryption Padding. It is recommended for new applications.</summary>
    </member>
    <member name="F:System.Security.Cryptography.RSAEncryptionPaddingMode.Pkcs1">
      <summary>PKCS #1 v1.5. It is supported for compatibility with existing applications.</summary>
    </member>
    <member name="T:System.Security.Cryptography.RSAOAEPKeyExchangeDeformatter">
      <summary>Decrypts Optimal Asymmetric Encryption Padding (OAEP) key exchange data.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RSAOAEPKeyExchangeDeformatter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.RSAOAEPKeyExchangeDeformatter" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RSAOAEPKeyExchangeDeformatter.#ctor(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.RSAOAEPKeyExchangeDeformatter" /> class with the specified key.</summary>
      <param name="key">The instance of the <see cref="T:System.Security.Cryptography.RSA" /> algorithm that holds the private key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.RSAOAEPKeyExchangeDeformatter.DecryptKeyExchange(System.Byte[])">
      <summary>Extracts secret information from the encrypted key exchange data.</summary>
      <param name="rgbData">The key exchange data within which the secret information is hidden.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key exchange data verification has failed.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicUnexpectedOperationException">The key is missing.</exception>
      <returns>The secret information derived from the key exchange data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSAOAEPKeyExchangeDeformatter.SetKey(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Sets the private key to use for decrypting the secret information.</summary>
      <param name="key">The instance of the <see cref="T:System.Security.Cryptography.RSA" /> algorithm that holds the private key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.Cryptography.RSAOAEPKeyExchangeDeformatter.Parameters">
      <summary>Gets the parameters for the Optimal Asymmetric Encryption Padding (OAEP) key exchange.</summary>
      <returns>An XML string containing the parameters of the OAEP key exchange operation.</returns>
    </member>
    <member name="T:System.Security.Cryptography.RSAOAEPKeyExchangeFormatter">
      <summary>Creates Optimal Asymmetric Encryption Padding (OAEP) key exchange data using <see cref="T:System.Security.Cryptography.RSA" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RSAOAEPKeyExchangeFormatter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.RSAOAEPKeyExchangeFormatter" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RSAOAEPKeyExchangeFormatter.#ctor(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.RSAOAEPKeyExchangeFormatter" /> class with the specified key.</summary>
      <param name="key">The instance of the <see cref="T:System.Security.Cryptography.RSA" /> algorithm that holds the public key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.RSAOAEPKeyExchangeFormatter.CreateKeyExchange(System.Byte[])">
      <summary>Creates the encrypted key exchange data from the specified input data.</summary>
      <param name="rgbData">The secret information to be passed in the key exchange.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicUnexpectedOperationException">The key is missing.</exception>
      <returns>The encrypted key exchange data to be sent to the intended recipient.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSAOAEPKeyExchangeFormatter.CreateKeyExchange(System.Byte[],System.Type)">
      <summary>Creates the encrypted key exchange data from the specified input data.</summary>
      <param name="rgbData">The secret information to be passed in the key exchange.</param>
      <param name="symAlgType">This parameter is not used in the current version.</param>
      <returns>The encrypted key exchange data to be sent to the intended recipient.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSAOAEPKeyExchangeFormatter.SetKey(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Sets the public key to use for encrypting the key exchange data.</summary>
      <param name="key">The instance of the <see cref="T:System.Security.Cryptography.RSA" /> algorithm that holds the public key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.Cryptography.RSAOAEPKeyExchangeFormatter.Parameter">
      <summary>Gets or sets the parameter used to create padding in the key exchange creation process.</summary>
      <returns>The parameter value.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RSAOAEPKeyExchangeFormatter.Parameters">
      <summary>Gets the parameters for the Optimal Asymmetric Encryption Padding (OAEP) key exchange.</summary>
      <returns>An XML string containing the parameters of the OAEP key exchange operation.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RSAOAEPKeyExchangeFormatter.Rng">
      <summary>Gets or sets the random number generator algorithm to use in the creation of the key exchange.</summary>
      <returns>The instance of a random number generator algorithm to use.</returns>
    </member>
    <member name="T:System.Security.Cryptography.RSAParameters">
      <summary>Represents the standard parameters for the <see cref="T:System.Security.Cryptography.RSA" /> algorithm.</summary>
    </member>
    <member name="F:System.Security.Cryptography.RSAParameters.D">
      <summary>Represents the <see langword="D" /> parameter for the <see cref="T:System.Security.Cryptography.RSA" /> algorithm.</summary>
    </member>
    <member name="F:System.Security.Cryptography.RSAParameters.DP">
      <summary>Represents the <see langword="DP" /> parameter for the <see cref="T:System.Security.Cryptography.RSA" /> algorithm.</summary>
    </member>
    <member name="F:System.Security.Cryptography.RSAParameters.DQ">
      <summary>Represents the <see langword="DQ" /> parameter for the <see cref="T:System.Security.Cryptography.RSA" /> algorithm.</summary>
    </member>
    <member name="F:System.Security.Cryptography.RSAParameters.Exponent">
      <summary>Represents the <see langword="Exponent" /> parameter for the <see cref="T:System.Security.Cryptography.RSA" /> algorithm.</summary>
    </member>
    <member name="F:System.Security.Cryptography.RSAParameters.InverseQ">
      <summary>Represents the <see langword="InverseQ" /> parameter for the <see cref="T:System.Security.Cryptography.RSA" /> algorithm.</summary>
    </member>
    <member name="F:System.Security.Cryptography.RSAParameters.Modulus">
      <summary>Represents the <see langword="Modulus" /> parameter for the <see cref="T:System.Security.Cryptography.RSA" /> algorithm.</summary>
    </member>
    <member name="F:System.Security.Cryptography.RSAParameters.P">
      <summary>Represents the <see langword="P" /> parameter for the <see cref="T:System.Security.Cryptography.RSA" /> algorithm.</summary>
    </member>
    <member name="F:System.Security.Cryptography.RSAParameters.Q">
      <summary>Represents the <see langword="Q" /> parameter for the <see cref="T:System.Security.Cryptography.RSA" /> algorithm.</summary>
    </member>
    <member name="T:System.Security.Cryptography.RSAPKCS1KeyExchangeDeformatter">
      <summary>Decrypts the PKCS #1 key exchange data.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RSAPKCS1KeyExchangeDeformatter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.RSAPKCS1KeyExchangeDeformatter" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RSAPKCS1KeyExchangeDeformatter.#ctor(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.RSAPKCS1KeyExchangeDeformatter" /> class with the specified key.</summary>
      <param name="key">The instance of the <see cref="T:System.Security.Cryptography.RSA" /> algorithm that holds the private key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.RSAPKCS1KeyExchangeDeformatter.DecryptKeyExchange(System.Byte[])">
      <summary>Extracts secret information from the encrypted key exchange data.</summary>
      <param name="rgbIn">The key exchange data within which the secret information is hidden.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicUnexpectedOperationException">The key is missing.</exception>
      <returns>The secret information derived from the key exchange data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSAPKCS1KeyExchangeDeformatter.SetKey(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Sets the private key to use for decrypting the secret information.</summary>
      <param name="key">The instance of the <see cref="T:System.Security.Cryptography.RSA" /> algorithm that holds the private key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.Cryptography.RSAPKCS1KeyExchangeDeformatter.Parameters">
      <summary>Gets the parameters for the PKCS #1 key exchange.</summary>
      <returns>An XML string containing the parameters of the PKCS #1 key exchange operation.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RSAPKCS1KeyExchangeDeformatter.RNG">
      <summary>Gets or sets the random number generator algorithm to use in the creation of the key exchange.</summary>
      <returns>The instance of a random number generator algorithm to use.</returns>
    </member>
    <member name="T:System.Security.Cryptography.RSAPKCS1KeyExchangeFormatter">
      <summary>Creates the PKCS#1 key exchange data using <see cref="T:System.Security.Cryptography.RSA" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RSAPKCS1KeyExchangeFormatter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.RSAPKCS1KeyExchangeFormatter" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RSAPKCS1KeyExchangeFormatter.#ctor(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.RSAPKCS1KeyExchangeFormatter" /> class with the specified key.</summary>
      <param name="key">The instance of the <see cref="T:System.Security.Cryptography.RSA" /> algorithm that holds the public key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.RSAPKCS1KeyExchangeFormatter.CreateKeyExchange(System.Byte[])">
      <summary>Creates the encrypted key exchange data from the specified input data.</summary>
      <param name="rgbData">The secret information to be passed in the key exchange.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="rgbData" /> is too big.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicUnexpectedOperationException">The key is <see langword="null" />.</exception>
      <returns>The encrypted key exchange data to be sent to the intended recipient.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSAPKCS1KeyExchangeFormatter.CreateKeyExchange(System.Byte[],System.Type)">
      <summary>Creates the encrypted key exchange data from the specified input data.</summary>
      <param name="rgbData">The secret information to be passed in the key exchange.</param>
      <param name="symAlgType">This parameter is not used in the current version.</param>
      <returns>The encrypted key exchange data to be sent to the intended recipient.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSAPKCS1KeyExchangeFormatter.SetKey(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Sets the public key to use for encrypting the key exchange data.</summary>
      <param name="key">The instance of the <see cref="T:System.Security.Cryptography.RSA" /> algorithm that holds the public key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Security.Cryptography.RSAPKCS1KeyExchangeFormatter.Parameters">
      <summary>Gets the parameters for the PKCS #1 key exchange.</summary>
      <returns>An XML string containing the parameters of the PKCS #1 key exchange operation.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RSAPKCS1KeyExchangeFormatter.Rng">
      <summary>Gets or sets the random number generator algorithm to use in the creation of the key exchange.</summary>
      <returns>The instance of a random number generator algorithm to use.</returns>
    </member>
    <member name="T:System.Security.Cryptography.RSAPKCS1SignatureDeformatter">
      <summary>Verifies an <see cref="T:System.Security.Cryptography.RSA" /> PKCS #1 version 1.5 signature.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RSAPKCS1SignatureDeformatter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.RSAPKCS1SignatureDeformatter" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RSAPKCS1SignatureDeformatter.#ctor(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.RSAPKCS1SignatureDeformatter" /> class with the specified key.</summary>
      <param name="key">The instance of <see cref="T:System.Security.Cryptography.RSA" /> that holds the public key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.RSAPKCS1SignatureDeformatter.SetHashAlgorithm(System.String)">
      <summary>Sets the hash algorithm to use for verifying the signature.</summary>
      <param name="strName">The name of the hash algorithm to use for verifying the signature.</param>
    </member>
    <member name="M:System.Security.Cryptography.RSAPKCS1SignatureDeformatter.SetKey(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Sets the public key to use for verifying the signature.</summary>
      <param name="key">The instance of <see cref="T:System.Security.Cryptography.RSA" /> that holds the public key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.RSAPKCS1SignatureDeformatter.VerifySignature(System.Byte[],System.Byte[])">
      <summary>Verifies the <see cref="T:System.Security.Cryptography.RSA" /> PKCS#1 signature for the specified data.</summary>
      <param name="rgbHash">The data signed with <paramref name="rgbSignature" />.</param>
      <param name="rgbSignature">The signature to be verified for <paramref name="rgbHash" />.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicUnexpectedOperationException">The key is <see langword="null" />.  
  
 -or-  
  
 The hash algorithm is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rgbHash" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The <paramref name="rgbSignature" /> parameter is <see langword="null" />.</exception>
      <returns>
        <see langword="true" /> if <paramref name="rgbSignature" /> matches the signature computed using the specified hash algorithm and key on <paramref name="rgbHash" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.RSAPKCS1SignatureFormatter">
      <summary>Creates an <see cref="T:System.Security.Cryptography.RSA" /> PKCS #1 version 1.5 signature.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RSAPKCS1SignatureFormatter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.RSAPKCS1SignatureFormatter" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RSAPKCS1SignatureFormatter.#ctor(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.RSAPKCS1SignatureFormatter" /> class with the specified key.</summary>
      <param name="key">The instance of the <see cref="T:System.Security.Cryptography.RSA" /> algorithm that holds the private key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.RSAPKCS1SignatureFormatter.CreateSignature(System.Byte[])">
      <summary>Creates the <see cref="T:System.Security.Cryptography.RSA" /> PKCS #1 signature for the specified data.</summary>
      <param name="rgbHash">The data to be signed.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicUnexpectedOperationException">The key is <see langword="null" />.  
  
 -or-  
  
 The hash algorithm is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="rgbHash" /> parameter is <see langword="null" />.</exception>
      <returns>The digital signature for <paramref name="rgbHash" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSAPKCS1SignatureFormatter.SetHashAlgorithm(System.String)">
      <summary>Sets the hash algorithm to use for creating the signature.</summary>
      <param name="strName">The name of the hash algorithm to use for creating the signature.</param>
    </member>
    <member name="M:System.Security.Cryptography.RSAPKCS1SignatureFormatter.SetKey(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Sets the private key to use for creating the signature.</summary>
      <param name="key">The instance of the <see cref="T:System.Security.Cryptography.RSA" /> algorithm that holds the private key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
    </member>
    <member name="T:System.Security.Cryptography.RSASignaturePadding">
      <summary>Specifies the padding mode and parameters to use with RSA signature creation or verification operations.</summary>
    </member>
    <member name="M:System.Security.Cryptography.RSASignaturePadding.Equals(System.Object)">
      <summary>Returns a value that indicates whether this instance is equal to a specified object.</summary>
      <param name="obj">The object to compare with the current instance.</param>
      <returns>
        <see langword="true" /> if the specified object is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSASignaturePadding.Equals(System.Security.Cryptography.RSASignaturePadding)">
      <summary>Returns a value that indicates whether this instance is equal to a specified <see cref="T:System.Security.Cryptography.RSASignaturePadding" /> object.</summary>
      <param name="other">The object to compare with the current instance.</param>
      <returns>
        <see langword="true" /> if the specified object is equal to the current object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSASignaturePadding.GetHashCode">
      <summary>Returns the hash code for this <see cref="T:System.Security.Cryptography.RSASignaturePadding" /> instance.</summary>
      <returns>The hash code for this <see cref="T:System.Security.Cryptography.RSASignaturePadding" /> instance.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSASignaturePadding.op_Equality(System.Security.Cryptography.RSASignaturePadding,System.Security.Cryptography.RSASignaturePadding)">
      <summary>Indicates whether two specified <see cref="T:System.Security.Cryptography.RSASignaturePadding" /> objects are equal.</summary>
      <param name="left">The first object to compare.</param>
      <param name="right">The second object to compare.</param>
      <returns>
        <see langword="true" /> if <see langword="left" /> and <see langword="right" /> are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSASignaturePadding.op_Inequality(System.Security.Cryptography.RSASignaturePadding,System.Security.Cryptography.RSASignaturePadding)">
      <summary>Indicates whether two specified <see cref="T:System.Security.Cryptography.RSASignaturePadding" /> objects are unequal.</summary>
      <param name="left">The first object to compare.</param>
      <param name="right">The second object to compare.</param>
      <returns>
        <see langword="true" /> if <see langword="left" /> and <see langword="right" /> are unequal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.RSASignaturePadding.ToString">
      <summary>Returns the string representation of the current <see cref="T:System.Security.Cryptography.RSASignaturePadding" /> instance.</summary>
      <returns>The string representation of the current object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RSASignaturePadding.Mode">
      <summary>Gets the padding mode of this <see cref="T:System.Security.Cryptography.RSASignaturePadding" /> instance.</summary>
      <returns>The padding mode (either <see cref="F:System.Security.Cryptography.RSASignaturePaddingMode.Pkcs1" /> or <see cref="F:System.Security.Cryptography.RSASignaturePaddingMode.Pss" />) of this instance.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RSASignaturePadding.Pkcs1">
      <summary>Gets an object that uses the PKCS #1 v1.5 padding mode.</summary>
      <returns>An object that uses the <see cref="F:System.Security.Cryptography.RSASignaturePaddingMode.Pkcs1" /> padding mode.</returns>
    </member>
    <member name="P:System.Security.Cryptography.RSASignaturePadding.Pss">
      <summary>Gets an object that uses PSS padding mode.</summary>
      <returns>An object that uses the <see cref="F:System.Security.Cryptography.RSASignaturePaddingMode.Pss" /> padding mode with the number of salt bytes equal to the size of the hash.</returns>
    </member>
    <member name="T:System.Security.Cryptography.RSASignaturePaddingMode">
      <summary>Specifies the padding mode to use with RSA signature creation or verification operations.</summary>
    </member>
    <member name="F:System.Security.Cryptography.RSASignaturePaddingMode.Pkcs1">
      <summary>PKCS #1 v1.5.</summary>
    </member>
    <member name="F:System.Security.Cryptography.RSASignaturePaddingMode.Pss">
      <summary>Probabilistic Signature Scheme.</summary>
    </member>
    <member name="T:System.Security.Cryptography.SHA1">
      <summary>Computes the <see cref="T:System.Security.Cryptography.SHA1" /> hash for the input data.</summary>
    </member>
    <member name="M:System.Security.Cryptography.SHA1.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.Security.Cryptography.SHA1" />.</summary>
      <exception cref="T:System.InvalidOperationException">The policy on this object is not compliant with the FIPS algorithm.</exception>
    </member>
    <member name="M:System.Security.Cryptography.SHA1.Create">
      <summary>Creates an instance of the default implementation of <see cref="T:System.Security.Cryptography.SHA1" />.</summary>
      <returns>A new instance of <see cref="T:System.Security.Cryptography.SHA1" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.SHA1.Create(System.String)">
      <summary>Creates an instance of the specified implementation of <see cref="T:System.Security.Cryptography.SHA1" />.</summary>
      <param name="hashName">The name of the specific implementation of <see cref="T:System.Security.Cryptography.SHA1" /> to be used.</param>
      <returns>A new instance of <see cref="T:System.Security.Cryptography.SHA1" /> using the specified implementation.</returns>
    </member>
    <member name="M:System.Security.Cryptography.SHA1.HashData(System.Byte[])">
      <summary>Computes the hash of data using the SHA1 algorithm.</summary>
      <param name="source">The data to hash.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <returns>The hash of the data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.SHA1.HashData(System.ReadOnlySpan{System.Byte})">
      <summary>Computes the hash of data using the SHA1 algorithm.</summary>
      <param name="source">The data to hash.</param>
      <returns>The hash of the data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.SHA1.HashData(System.ReadOnlySpan{System.Byte},System.Span{System.Byte})">
      <summary>Computes the hash of data using the SHA1 algorithm.</summary>
      <param name="source">The data to hash.</param>
      <param name="destination">The buffer to receive the hash value.</param>
      <exception cref="T:System.ArgumentException">The buffer in <paramref name="destination" /> is too small to hold the calculated hash size. The SHA1 algorithm always produces a 160-bit hash, or 20 bytes.</exception>
      <returns>The total number of bytes written to <paramref name="destination" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.SHA1.TryHashData(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to compute the hash of data using the SHA1 algorithm.</summary>
      <param name="source">The data to hash.</param>
      <param name="destination">The buffer to receive the hash value.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />.</param>
      <returns>
        <see langword="false" /> if <paramref name="destination" /> is too small to hold the calculated hash, <see langword="true" /> otherwise.</returns>
    </member>
    <member name="T:System.Security.Cryptography.SHA1Managed">
      <summary>Computes the <see cref="T:System.Security.Cryptography.SHA1" /> hash for the input data using the managed library.</summary>
    </member>
    <member name="M:System.Security.Cryptography.SHA1Managed.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.SHA1Managed" /> class.</summary>
      <exception cref="T:System.InvalidOperationException">This class is not compliant with the FIPS algorithm.</exception>
    </member>
    <member name="M:System.Security.Cryptography.SHA1Managed.Initialize">
      <summary>Initializes an instance of <see cref="T:System.Security.Cryptography.SHA1Managed" />.</summary>
    </member>
    <member name="T:System.Security.Cryptography.SHA256">
      <summary>Computes the <see cref="T:System.Security.Cryptography.SHA256" /> hash for the input data.</summary>
    </member>
    <member name="M:System.Security.Cryptography.SHA256.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.Security.Cryptography.SHA256" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.SHA256.Create">
      <summary>Creates an instance of the default implementation of <see cref="T:System.Security.Cryptography.SHA256" />.</summary>
      <exception cref="T:System.Reflection.TargetInvocationException">On the .NET Framework 4.6.1 and earlier versions only: The algorithm was used with Federal Information Processing Standards (FIPS) mode enabled, but is not FIPS compatible.</exception>
      <returns>A new instance of <see cref="T:System.Security.Cryptography.SHA256" />. On the .NET Framework, this method creates an instance of the <see cref="T:System.Security.Cryptography.SHA256Managed" /> class if FIPS mode is not active; if FIPS mode is active, it creates an instance of the <see cref="T:System.Security.Cryptography.SHA256Cng" /> class. On .NET Core, it returns an instance of a private class derived from <see cref="T:System.Security.Cryptography.SHA256" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.SHA256.Create(System.String)">
      <summary>Creates an instance of a specified implementation of <see cref="T:System.Security.Cryptography.SHA256" />.</summary>
      <param name="hashName">The name of the specific implementation of <see cref="T:System.Security.Cryptography.SHA256" /> to be used.</param>
      <exception cref="T:System.Reflection.TargetInvocationException">On the .NET Framework only: FIPS mode is enabled, but <paramref name="hashName" /> requests <see cref="T:System.Security.Cryptography.SHA256Managed" />, which is not FIPS compatible.</exception>
      <returns>A new instance of <see cref="T:System.Security.Cryptography.SHA256" /> using the specified implementation.</returns>
    </member>
    <member name="M:System.Security.Cryptography.SHA256.HashData(System.Byte[])">
      <summary>Computes the hash of data using the SHA256 algorithm.</summary>
      <param name="source">The data to hash.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <returns>The hash of the data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.SHA256.HashData(System.ReadOnlySpan{System.Byte})">
      <summary>Computes the hash of data using the SHA256 algorithm.</summary>
      <param name="source">The data to hash.</param>
      <returns>The hash of the data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.SHA256.HashData(System.ReadOnlySpan{System.Byte},System.Span{System.Byte})">
      <summary>Computes the hash of data using the SHA256 algorithm.</summary>
      <param name="source">The data to hash.</param>
      <param name="destination">The buffer to receive the hash value.</param>
      <exception cref="T:System.ArgumentException">The buffer in <paramref name="destination" /> is too small to hold the calculated hash size. The SHA256 algorithm always produces a 256-bit hash, or 32 bytes.</exception>
      <returns>The total number of bytes written to <paramref name="destination" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.SHA256.TryHashData(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to compute the hash of data using the SHA256 algorithm.</summary>
      <param name="source">The data to hash.</param>
      <param name="destination">The buffer to receive the hash value.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />.</param>
      <returns>
        <see langword="false" /> if <paramref name="destination" /> is too small to hold the calculated hash, <see langword="true" /> otherwise.</returns>
    </member>
    <member name="T:System.Security.Cryptography.SHA256Managed">
      <summary>Computes the <see cref="T:System.Security.Cryptography.SHA256" /> hash for the input data using the managed library.</summary>
    </member>
    <member name="M:System.Security.Cryptography.SHA256Managed.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.SHA256Managed" /> class using the managed library.</summary>
      <exception cref="T:System.InvalidOperationException">The Federal Information Processing Standards (FIPS) security setting is enabled. This implementation is not part of the Windows Platform FIPS-validated cryptographic algorithms.</exception>
    </member>
    <member name="M:System.Security.Cryptography.SHA256Managed.Initialize">
      <summary>Initializes an instance of <see cref="T:System.Security.Cryptography.SHA256Managed" />.</summary>
    </member>
    <member name="T:System.Security.Cryptography.SHA384">
      <summary>Computes the <see cref="T:System.Security.Cryptography.SHA384" /> hash for the input data.</summary>
    </member>
    <member name="M:System.Security.Cryptography.SHA384.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.Security.Cryptography.SHA384" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.SHA384.Create">
      <summary>Creates an instance of the default implementation of <see cref="T:System.Security.Cryptography.SHA384" />.</summary>
      <exception cref="T:System.Reflection.TargetInvocationException">The algorithm was used with Federal Information Processing Standards (FIPS) mode enabled, but is not FIPS compatible.</exception>
      <returns>A new instance of <see cref="T:System.Security.Cryptography.SHA384" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.SHA384.Create(System.String)">
      <summary>Creates an instance of a specified implementation of <see cref="T:System.Security.Cryptography.SHA384" />.</summary>
      <param name="hashName">The name of the specific implementation of <see cref="T:System.Security.Cryptography.SHA384" /> to be used.</param>
      <exception cref="T:System.Reflection.TargetInvocationException">The algorithm described by the <paramref name="hashName" /> parameter was used with Federal Information Processing Standards (FIPS) mode enabled, but is not FIPS compatible.</exception>
      <returns>A new instance of <see cref="T:System.Security.Cryptography.SHA384" /> using the specified implementation.</returns>
    </member>
    <member name="M:System.Security.Cryptography.SHA384.HashData(System.Byte[])">
      <summary>Computes the hash of data using the SHA384 algorithm.</summary>
      <param name="source">The data to hash.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <returns>The hash of the data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.SHA384.HashData(System.ReadOnlySpan{System.Byte})">
      <summary>Computes the hash of data using the SHA384 algorithm.</summary>
      <param name="source">The data to hash.</param>
      <returns>The hash of the data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.SHA384.HashData(System.ReadOnlySpan{System.Byte},System.Span{System.Byte})">
      <summary>Computes the hash of data using the SHA384 algorithm.</summary>
      <param name="source">The data to hash.</param>
      <param name="destination">The buffer to receive the hash value.</param>
      <exception cref="T:System.ArgumentException">The buffer in <paramref name="destination" /> is too small to hold the calculated hash size. The SHA384 algorithm always produces a 384-bit hash, or 48 bytes.</exception>
      <returns>The total number of bytes written to <paramref name="destination" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.SHA384.TryHashData(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to compute the hash of data using the SHA384 algorithm.</summary>
      <param name="source">The data to hash.</param>
      <param name="destination">The buffer to receive the hash value.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />.</param>
      <returns>
        <see langword="false" /> if <paramref name="destination" /> is too small to hold the calculated hash, <see langword="true" /> otherwise.</returns>
    </member>
    <member name="T:System.Security.Cryptography.SHA384Managed">
      <summary>Computes the <see cref="T:System.Security.Cryptography.SHA384" /> hash for the input data using the managed library.</summary>
    </member>
    <member name="M:System.Security.Cryptography.SHA384Managed.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.SHA384Managed" /> class.</summary>
      <exception cref="T:System.InvalidOperationException">The Federal Information Processing Standards (FIPS) security setting is enabled. This implementation is not part of the Windows Platform FIPS-validated cryptographic algorithms.</exception>
    </member>
    <member name="M:System.Security.Cryptography.SHA384Managed.Initialize">
      <summary>Initializes an instance of <see cref="T:System.Security.Cryptography.SHA384Managed" />.</summary>
    </member>
    <member name="T:System.Security.Cryptography.SHA512">
      <summary>Computes the <see cref="T:System.Security.Cryptography.SHA512" /> hash for the input data.</summary>
    </member>
    <member name="M:System.Security.Cryptography.SHA512.#ctor">
      <summary>Initializes a new instance of <see cref="T:System.Security.Cryptography.SHA512" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.SHA512.Create">
      <summary>Creates an instance of the default implementation of <see cref="T:System.Security.Cryptography.SHA512" />.</summary>
      <exception cref="T:System.Reflection.TargetInvocationException">The algorithm was used with Federal Information Processing Standards (FIPS) mode enabled, but is not FIPS compatible.</exception>
      <returns>A new instance of <see cref="T:System.Security.Cryptography.SHA512" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.SHA512.Create(System.String)">
      <summary>Creates an instance of a specified implementation of <see cref="T:System.Security.Cryptography.SHA512" />.</summary>
      <param name="hashName">The name of the specific implementation of <see cref="T:System.Security.Cryptography.SHA512" /> to be used.</param>
      <exception cref="T:System.Reflection.TargetInvocationException">The algorithm described by the <paramref name="hashName" /> parameter was used with Federal Information Processing Standards (FIPS) mode enabled, but is not FIPS compatible.</exception>
      <returns>A new instance of <see cref="T:System.Security.Cryptography.SHA512" /> using the specified implementation.</returns>
    </member>
    <member name="M:System.Security.Cryptography.SHA512.HashData(System.Byte[])">
      <summary>Computes the hash of data using the SHA512 algorithm.</summary>
      <param name="source">The data to hash.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is <see langword="null" />.</exception>
      <returns>The hash of the data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.SHA512.HashData(System.ReadOnlySpan{System.Byte})">
      <summary>Computes the hash of data using the SHA512 algorithm.</summary>
      <param name="source">The data to hash.</param>
      <returns>The hash of the data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.SHA512.HashData(System.ReadOnlySpan{System.Byte},System.Span{System.Byte})">
      <summary>Computes the hash of data using the SHA512 algorithm.</summary>
      <param name="source">The data to hash.</param>
      <param name="destination">The buffer to receive the hash value.</param>
      <exception cref="T:System.ArgumentException">The buffer in <paramref name="destination" /> is too small to hold the calculated hash size. The SHA1 algorithm always produces a 512-bit hash, or 64 bytes.</exception>
      <returns>The total number of bytes written to <paramref name="destination" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.SHA512.TryHashData(System.ReadOnlySpan{System.Byte},System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to compute the hash of data using the SHA512 algorithm.</summary>
      <param name="source">The data to hash.</param>
      <param name="destination">The buffer to receive the hash value.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />.</param>
      <returns>
        <see langword="false" /> if <paramref name="destination" /> is too small to hold the calculated hash, <see langword="true" /> otherwise.</returns>
    </member>
    <member name="T:System.Security.Cryptography.SHA512Managed">
      <summary>Computes the <see cref="T:System.Security.Cryptography.SHA512" /> hash algorithm for the input data using the managed library.</summary>
    </member>
    <member name="M:System.Security.Cryptography.SHA512Managed.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.SHA512Managed" /> class.</summary>
      <exception cref="T:System.InvalidOperationException">The Federal Information Processing Standards (FIPS) security setting is enabled. This implementation is not part of the Windows Platform FIPS-validated cryptographic algorithms.</exception>
    </member>
    <member name="M:System.Security.Cryptography.SHA512Managed.Initialize">
      <summary>Initializes an instance of the <see cref="T:System.Security.Cryptography.SHA512Managed" /> class using the managed library.</summary>
    </member>
    <member name="T:System.Security.Cryptography.SignatureDescription">
      <summary>Contains information about the properties of a digital signature.</summary>
    </member>
    <member name="M:System.Security.Cryptography.SignatureDescription.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.SignatureDescription" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.SignatureDescription.#ctor(System.Security.SecurityElement)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.SignatureDescription" /> class from the specified <see cref="T:System.Security.SecurityElement" />.</summary>
      <param name="el">The <see cref="T:System.Security.SecurityElement" /> from which to get the algorithms for the signature description.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="el" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.SignatureDescription.CreateDeformatter(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Creates an <see cref="T:System.Security.Cryptography.AsymmetricSignatureDeformatter" /> instance with the specified key using the <see cref="P:System.Security.Cryptography.SignatureDescription.DeformatterAlgorithm" /> property.</summary>
      <param name="key">The key to use in the <see cref="T:System.Security.Cryptography.AsymmetricSignatureDeformatter" />.</param>
      <returns>The newly created <see cref="T:System.Security.Cryptography.AsymmetricSignatureDeformatter" /> instance.</returns>
    </member>
    <member name="M:System.Security.Cryptography.SignatureDescription.CreateDigest">
      <summary>Creates a <see cref="T:System.Security.Cryptography.HashAlgorithm" /> instance using the <see cref="P:System.Security.Cryptography.SignatureDescription.DigestAlgorithm" /> property.</summary>
      <returns>The newly created <see cref="T:System.Security.Cryptography.HashAlgorithm" /> instance.</returns>
    </member>
    <member name="M:System.Security.Cryptography.SignatureDescription.CreateFormatter(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Creates an <see cref="T:System.Security.Cryptography.AsymmetricSignatureFormatter" /> instance with the specified key using the <see cref="P:System.Security.Cryptography.SignatureDescription.FormatterAlgorithm" /> property.</summary>
      <param name="key">The key to use in the <see cref="T:System.Security.Cryptography.AsymmetricSignatureFormatter" />.</param>
      <returns>The newly created <see cref="T:System.Security.Cryptography.AsymmetricSignatureFormatter" /> instance.</returns>
    </member>
    <member name="P:System.Security.Cryptography.SignatureDescription.DeformatterAlgorithm">
      <summary>Gets or sets the deformatter algorithm for the signature description.</summary>
      <returns>The deformatter algorithm for the signature description.</returns>
    </member>
    <member name="P:System.Security.Cryptography.SignatureDescription.DigestAlgorithm">
      <summary>Gets or sets the digest algorithm for the signature description.</summary>
      <returns>The digest algorithm for the signature description.</returns>
    </member>
    <member name="P:System.Security.Cryptography.SignatureDescription.FormatterAlgorithm">
      <summary>Gets or sets the formatter algorithm for the signature description.</summary>
      <returns>The formatter algorithm for the signature description.</returns>
    </member>
    <member name="P:System.Security.Cryptography.SignatureDescription.KeyAlgorithm">
      <summary>Gets or sets the key algorithm for the signature description.</summary>
      <returns>The key algorithm for the signature description.</returns>
    </member>
    <member name="T:System.Security.Cryptography.TripleDES">
      <summary>Represents the base class for Triple Data Encryption Standard algorithms from which all <see cref="T:System.Security.Cryptography.TripleDES" /> implementations must derive.</summary>
    </member>
    <member name="M:System.Security.Cryptography.TripleDES.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.TripleDES" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.TripleDES.Create">
      <summary>Creates an instance of a cryptographic object to perform the <see cref="T:System.Security.Cryptography.TripleDES" /> algorithm.</summary>
      <returns>An instance of a cryptographic object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.TripleDES.Create(System.String)">
      <summary>Creates an instance of a cryptographic object to perform the specified implementation of the <see cref="T:System.Security.Cryptography.TripleDES" /> algorithm.</summary>
      <param name="str">The name of the specific implementation of <see cref="T:System.Security.Cryptography.TripleDES" /> to use.</param>
      <returns>An instance of a cryptographic object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.TripleDES.IsWeakKey(System.Byte[])">
      <summary>Determines whether the specified key is weak.</summary>
      <param name="rgbKey">The secret key to test for weakness.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The size of the <paramref name="rgbKey" /> parameter is not valid.</exception>
      <returns>
        <see langword="true" /> if the key is weak; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.TripleDES.Key">
      <summary>Gets or sets the secret key for the <see cref="T:System.Security.Cryptography.TripleDES" /> algorithm.</summary>
      <exception cref="T:System.ArgumentNullException">An attempt was made to set the key to <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An attempt was made to set a key whose length is invalid.  
  
 -or-  
  
 An attempt was made to set a weak key (see <see cref="M:System.Security.Cryptography.TripleDES.IsWeakKey(System.Byte[])" />).</exception>
      <returns>The secret key for the <see cref="T:System.Security.Cryptography.TripleDES" /> algorithm.</returns>
    </member>
  </members>
</doc>