using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace FlightPig.Models
{
    /// <summary>
    /// Represents an airport, airfield, or helipad for landing challenges
    /// </summary>
    public class Airport
    {
        [JsonPropertyName("icaoCode")]
        public string IcaoCode { get; set; }

        [JsonPropertyName("iataCode")]
        public string IataCode { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("latitude")]
        public double Latitude { get; set; }

        [JsonPropertyName("longitude")]
        public double Longitude { get; set; }

        [JsonPropertyName("elevation")]
        public double ElevationFeet { get; set; }

        [JsonPropertyName("type")]
        public AirportType Type { get; set; }

        [JsonPropertyName("runways")]
        public List<Runway> Runways { get; set; } = new List<Runway>();

        [JsonPropertyName("country")]
        public string Country { get; set; }

        [JsonPropertyName("region")]
        public string Region { get; set; }

        [JsonPropertyName("city")]
        public string City { get; set; }

        [JsonPropertyName("isControlled")]
        public bool IsControlled { get; set; }

        [JsonPropertyName("hasILS")]
        public bool HasILS { get; set; }

        [JsonPropertyName("difficulty")]
        public LandingDifficulty Difficulty { get; set; }

        [JsonPropertyName("suitableAircraft")]
        public List<string> SuitableAircraft { get; set; } = new List<string>();

        [JsonPropertyName("challenges")]
        public List<string> Challenges { get; set; } = new List<string>();

        [JsonPropertyName("description")]
        public string Description { get; set; }

        /// <summary>
        /// Get the primary runway for landing challenges
        /// </summary>
        public Runway GetPrimaryRunway()
        {
            if (Runways.Count == 0) return null;
            
            // Prefer the longest runway
            Runway longest = Runways[0];
            foreach (var runway in Runways)
            {
                if (runway.LengthFeet > longest.LengthFeet)
                    longest = runway;
            }
            return longest;
        }

        /// <summary>
        /// Check if airport is suitable for aircraft type
        /// </summary>
        public bool IsSuitableForAircraft(string aircraftType)
        {
            if (SuitableAircraft.Count == 0) return true; // No restrictions
            
            foreach (var suitable in SuitableAircraft)
            {
                if (aircraftType.Contains(suitable, System.StringComparison.OrdinalIgnoreCase))
                    return true;
            }
            return false;
        }
    }

    /// <summary>
    /// Represents a runway at an airport
    /// </summary>
    public class Runway
    {
        [JsonPropertyName("designation")]
        public string Designation { get; set; }

        [JsonPropertyName("heading")]
        public double Heading { get; set; }

        [JsonPropertyName("lengthFeet")]
        public double LengthFeet { get; set; }

        [JsonPropertyName("widthFeet")]
        public double WidthFeet { get; set; }

        [JsonPropertyName("surface")]
        public string Surface { get; set; }

        [JsonPropertyName("hasLights")]
        public bool HasLights { get; set; }

        [JsonPropertyName("hasILS")]
        public bool HasILS { get; set; }

        [JsonPropertyName("thresholdLatitude")]
        public double? ThresholdLatitude { get; set; }

        [JsonPropertyName("thresholdLongitude")]
        public double? ThresholdLongitude { get; set; }

        [JsonPropertyName("thresholdElevation")]
        public double? ThresholdElevation { get; set; }
    }

    /// <summary>
    /// Airport types for different aircraft
    /// </summary>
    public enum AirportType
    {
        International,
        Regional,
        Municipal,
        Private,
        Military,
        Heliport,
        Seaplane,
        Gliderport
    }

    /// <summary>
    /// Landing difficulty levels
    /// </summary>
    public enum LandingDifficulty
    {
        Easy,
        Medium,
        Hard,
        Expert
    }
}
