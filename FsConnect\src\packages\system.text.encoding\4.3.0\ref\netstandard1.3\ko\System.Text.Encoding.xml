﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Text.Encoding</name>
  </assembly>
  <members>
    <member name="T:System.Text.Decoder">
      <summary>인코딩된 바이트 시퀀스를 문자 집합으로 변환합니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Decoder.#ctor">
      <summary>
        <see cref="T:System.Text.Decoder" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Text.Decoder.Convert(System.Byte[],System.Int32,System.Int32,System.Char[],System.Int32,System.Int32,System.Boolean,System.Int32@,System.Int32@,System.Boolean@)">
      <summary>인코딩된 바이트 배열을 UTF-16 인코딩된 문자로 변환하고 그 결과를 문자 배열로 저장합니다.</summary>
      <param name="bytes">변환할 바이트 배열입니다.</param>
      <param name="byteIndex">변환할 <paramref name="bytes" />의 첫 번째 요소입니다.</param>
      <param name="byteCount">
        <paramref name="bytes" />의 요소 중에서 변환할 요소의 수입니다.</param>
      <param name="chars">변환된 문자를 저장할 배열입니다.</param>
      <param name="charIndex">데이터가 저장된 <paramref name="chars" />의 첫 번째 요소입니다.</param>
      <param name="charCount">변환에 사용할 <paramref name="chars" />의 최대 요소 수입니다.</param>
      <param name="flush">더 이상 변환할 데이터가 없음을 나타내려면 true이고, 그렇지 않으면 false입니다.</param>
      <param name="bytesUsed">이 메서드가 반환할 때 변환에 사용된 바이트 수를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="charsUsed">이 메서드가 반환할 때 변환에 의해 생성된 <paramref name="chars" />의 문자 수를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="completed">이 메서드가 반환할 때 <paramref name="byteCount" />에서 지정한 모든 문자가 변환되었으면 true를 포함하고, 그렇지 않으면 false를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> 또는 <paramref name="bytes" />가 null(Nothing)인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charIndex" />, <paramref name="charCount" />, <paramref name="byteIndex" /> 또는 <paramref name="byteCount" />가 0보다 작은 경우또는<paramref name="chars" />의 길이입니다. - <paramref name="charIndex" />가 <paramref name="charCount" />보다 작은 경우또는<paramref name="bytes" />의 길이입니다. - <paramref name="byteIndex" />가 <paramref name="byteCount" />보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">출력 버퍼가 너무 작아 변환된 입력을 모두 포함할 수 없는 경우.출력 버퍼는 <see cref="Overload:System.Text.Decoder.GetCharCount" /> 메서드에서 지시하는 크기보다 크거나 같아야 합니다.</exception>
      <exception cref="T:System.Text.DecoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.-및-<see cref="P:System.Text.Decoder.Fallback" />이 <see cref="T:System.Text.DecoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Text.Decoder.Fallback">
      <summary>현재 <see cref="T:System.Text.Decoder" /> 개체에 대한 <see cref="T:System.Text.DecoderFallback" /> 개체를 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Text.DecoderFallback" /> 개체</returns>
      <exception cref="T:System.ArgumentNullException">set 작업의 값이 null(Nothing)인 경우</exception>
      <exception cref="T:System.ArgumentException">현재 <see cref="T:System.Text.DecoderFallbackBuffer" /> 개체에 아직 디코딩되지 않은 데이터가 들어 있어, 설정 작업에서 새 값을 할당할 수 없는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Text.Decoder.FallbackBuffer">
      <summary>현재 <see cref="T:System.Text.Decoder" /> 개체와 관련된 <see cref="T:System.Text.DecoderFallbackBuffer" /> 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Text.DecoderFallbackBuffer" /> 개체</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Decoder.GetCharCount(System.Byte[],System.Int32,System.Int32)">
      <summary>파생 클래스에서 재정의될 때 지정한 바이트 배열의 바이트 시퀀스를 디코딩하여 생성되는 문자 수를 계산합니다.</summary>
      <returns>지정한 바이트 시퀀스 및 내부 버퍼의 바이트를 디코딩하여 생성되는 문자 수입니다.</returns>
      <param name="bytes">디코딩할 바이트 시퀀스를 포함하는 바이트 배열입니다. </param>
      <param name="index">디코딩할 첫 번째 바이트의 인덱스입니다. </param>
      <param name="count">디코딩할 바이트 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" />가 null (Nothing)인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 또는 <paramref name="count" />가 0보다 작은 경우또는 <paramref name="index" /> 및 <paramref name="count" />가 <paramref name="bytes" />의 유효한 범위를 나타내지 않는 경우 </exception>
      <exception cref="T:System.Text.DecoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.-및-<see cref="P:System.Text.Decoder.Fallback" />이 <see cref="T:System.Text.DecoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.Decoder.GetCharCount(System.Byte[],System.Int32,System.Int32,System.Boolean)">
      <summary>파생 클래스에서 재정의될 때 지정한 바이트 배열의 바이트 시퀀스를 디코딩하여 생성되는 문자 수를 계산합니다.매개 변수는 계산 후 디코더의 내부 상태를 지울지 여부를 나타냅니다.</summary>
      <returns>지정한 바이트 시퀀스 및 내부 버퍼의 바이트를 디코딩하여 생성되는 문자 수입니다.</returns>
      <param name="bytes">디코딩할 바이트 시퀀스를 포함하는 바이트 배열입니다. </param>
      <param name="index">디코딩할 첫 번째 바이트의 인덱스입니다. </param>
      <param name="count">디코딩할 바이트 수입니다. </param>
      <param name="flush">계산 후 인코더의 내부 상태를 지우도록 시뮬레이션하려면 true이고, 그렇지 않으면 false입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" />가 null (Nothing)인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 또는 <paramref name="count" />가 0보다 작은 경우또는 <paramref name="index" /> 및 <paramref name="count" />가 <paramref name="bytes" />의 유효한 범위를 나타내지 않는 경우 </exception>
      <exception cref="T:System.Text.DecoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.-및-<see cref="P:System.Text.Decoder.Fallback" />이 <see cref="T:System.Text.DecoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.Decoder.GetChars(System.Byte[],System.Int32,System.Int32,System.Char[],System.Int32)">
      <summary>파생 클래스에서 재정의될 때 지정한 바이트 배열의 바이트 시퀀스 및 내부 버퍼의 모든 바이트를 지정한 문자 배열로 디코딩합니다.</summary>
      <returns>
        <paramref name="chars" />에 써지는 실제 문자 수입니다.</returns>
      <param name="bytes">디코딩할 바이트 시퀀스를 포함하는 바이트 배열입니다. </param>
      <param name="byteIndex">디코딩할 첫 번째 바이트의 인덱스입니다. </param>
      <param name="byteCount">디코딩할 바이트 수입니다. </param>
      <param name="chars">결과 문자 집합을 포함할 문자 배열입니다. </param>
      <param name="charIndex">결과 문자 집합을 쓰기 시작할 인덱스입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" />가 null (Nothing)인 경우또는 <paramref name="chars" /> 가 null (Nothing)인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteIndex" />, <paramref name="byteCount" /> 또는 <paramref name="charIndex" />가 0보다 작은 경우또는 <paramref name="byteindex" /> 및 <paramref name="byteCount" />가 <paramref name="bytes" />의 유효한 범위를 나타내지 않는 경우또는 <paramref name="charIndex" />가 <paramref name="chars" />의 유효한 인덱스가 아닌 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="chars" />의 용량(<paramref name="charIndex" /> ~ 배열 끝)이 부족해서 결과 문자를 수용할 수 없는 경우 </exception>
      <exception cref="T:System.Text.DecoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.-및-<see cref="P:System.Text.Decoder.Fallback" />이 <see cref="T:System.Text.DecoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.Decoder.GetChars(System.Byte[],System.Int32,System.Int32,System.Char[],System.Int32,System.Boolean)">
      <summary>파생 클래스에서 재정의될 때 지정한 바이트 배열의 바이트 시퀀스 및 내부 버퍼의 모든 바이트를 지정한 문자 배열로 디코딩합니다.매개 변수는 변환 후 디코더의 내부 상태를 지울지 여부를 나타냅니다.</summary>
      <returns>
        <paramref name="chars" /> 매개 변수에 써지는 실제 문자 수입니다.</returns>
      <param name="bytes">디코딩할 바이트 시퀀스를 포함하는 바이트 배열입니다. </param>
      <param name="byteIndex">디코딩할 첫 번째 바이트의 인덱스입니다. </param>
      <param name="byteCount">디코딩할 바이트 수입니다. </param>
      <param name="chars">결과 문자 집합을 포함할 문자 배열입니다. </param>
      <param name="charIndex">결과 문자 집합을 쓰기 시작할 인덱스입니다. </param>
      <param name="flush">변환 후 디코더의 내부 상태를 지우려면 true이고, 그렇지 않으면 false입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" />가 null (Nothing)인 경우또는 <paramref name="chars" /> 가 null (Nothing)인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteIndex" />, <paramref name="byteCount" /> 또는 <paramref name="charIndex" />가 0보다 작은 경우또는 <paramref name="byteindex" /> 및 <paramref name="byteCount" />가 <paramref name="bytes" />의 유효한 범위를 나타내지 않는 경우또는 <paramref name="charIndex" />가 <paramref name="chars" />의 유효한 인덱스가 아닌 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="chars" />의 용량(<paramref name="charIndex" /> ~ 배열 끝)이 부족해서 결과 문자를 수용할 수 없는 경우 </exception>
      <exception cref="T:System.Text.DecoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.-및-<see cref="P:System.Text.Decoder.Fallback" />이 <see cref="T:System.Text.DecoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.Decoder.Reset">
      <summary>파생 클래스에서 재정의될 때 디코더를 다시 초기 상태로 설정합니다.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Text.DecoderExceptionFallback">
      <summary>입력 문자로 변환할 수 없는 인코딩된 입력 바이트 시퀀스에 대해 대체(fallback)라고 하는 오류 처리 메커니즘을 제공합니다.대체(fallback)는 입력 바이트 시퀀스를 디코딩하는 대신 예외를 throw합니다.이 클래스는 상속될 수 없습니다.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.DecoderExceptionFallback.#ctor">
      <summary>
        <see cref="T:System.Text.DecoderExceptionFallback" />클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="M:System.Text.DecoderExceptionFallback.CreateFallbackBuffer">
      <summary>바이트 시퀀스를 문자로 변환할 수 없으면 예외를 throw하는 디코더 대체(fallback) 버퍼를 반환합니다. </summary>
      <returns>바이트 시퀀스를 디코딩할 수 없으면 디코더 대체(fallback) 버퍼는 예외를 throw합니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.DecoderExceptionFallback.Equals(System.Object)">
      <summary>현재 <see cref="T:System.Text.DecoderExceptionFallback" /> 개체와 지정된 개체가 같은지를 나타냅니다.</summary>
      <returns>
        <paramref name="value" />가 null이 아니고 <see cref="T:System.Text.DecoderExceptionFallback" /> 개체이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">
        <see cref="T:System.Text.DecoderExceptionFallback" /> 클래스에서 파생되는 개체입니다.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.DecoderExceptionFallback.GetHashCode">
      <summary>이 인스턴스의 해시 코드를 검색합니다.</summary>
      <returns>반환 값은 항상 같은 임의의 값이며 특별한 의미가 없습니다. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Text.DecoderExceptionFallback.MaxCharCount">
      <summary>이 인스턴스가 반환할 수 있는 최대 문자 수를 가져옵니다.</summary>
      <returns>반환 값은 항상 0입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Text.DecoderFallback">
      <summary>출력 문자로 변환할 수 없는 인코딩된 입력 바이트 시퀀스에 대해 대체(fallback)라고 하는 오류 처리 메커니즘을 제공합니다. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.DecoderFallback.#ctor">
      <summary>
        <see cref="T:System.Text.DecoderFallback" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="M:System.Text.DecoderFallback.CreateFallbackBuffer">
      <summary>파생 클래스에서 재정의된 경우 <see cref="T:System.Text.DecoderFallbackBuffer" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <returns>디코더에 대해 fallback 버퍼를 제공하는 개체입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Text.DecoderFallback.ExceptionFallback">
      <summary>입력 바이트 시퀀스를 디코딩할 수 없는 경우 예외를 throw하는 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Text.DecoderFallback" /> 클래스에서 파생된 형식입니다.기본값은 <see cref="T:System.Text.DecoderExceptionFallback" /> 개체입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Text.DecoderFallback.MaxCharCount">
      <summary>파생 클래스에서 재정의될 때 현재 <see cref="T:System.Text.DecoderFallback" /> 개체가 반환할 수 있는 최대 문자 수를 가져옵니다.</summary>
      <returns>현재 <see cref="T:System.Text.DecoderFallback" /> 개체가 반환할 수 있는 최대 문자 수입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Text.DecoderFallback.ReplacementFallback">
      <summary>디코딩할 수 없는 입력 바이트 시퀀스 대신 대체 문자열을 출력하는 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Text.DecoderFallback" /> 클래스에서 파생된 형식입니다.기본값은 알 수 없는 바이트 시퀀스 대신 물음표 문자("?", U+003F)를 내보내는 <see cref="T:System.Text.DecoderReplacementFallback" /> 개체입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Text.DecoderFallbackBuffer">
      <summary>입력 바이트 시퀀스를 디코드할 수 없을 경우 fallback 처리기가 디코더로 대체 문자열을 반환할 수 있는 버퍼를 제공합니다. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.DecoderFallbackBuffer.#ctor">
      <summary>
        <see cref="T:System.Text.DecoderFallbackBuffer" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="M:System.Text.DecoderFallbackBuffer.Fallback(System.Byte[],System.Int32)">
      <summary>파생 클래스에서 재정의될 때 지정된 입력 바이트 시퀀스를 처리하도록 대체 버퍼를 준비합니다.</summary>
      <returns>대체 버퍼에서 <paramref name="bytesUnknown" />을 처리할 수 있으면 true이고, 대체 버퍼에서 <paramref name="bytesUnknown" />을 무시하면 false입니다.</returns>
      <param name="bytesUnknown">입력 바이트 배열입니다.</param>
      <param name="index">
        <paramref name="bytesUnknown" />에 있는 바이트의 인덱스 위치입니다.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.DecoderFallbackBuffer.GetNextChar">
      <summary>파생 클래스에서 재정의될 때 대체 버퍼에서 다음 문자를 검색합니다.</summary>
      <returns>대체 버퍼에 있는 다음 문자입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.DecoderFallbackBuffer.MovePrevious">
      <summary>파생 클래스에서 재정의될 때 <see cref="M:System.Text.DecoderFallbackBuffer.GetNextChar" /> 메서드에 대한 다음 호출에서 데이터 버퍼의 현재 문자 위치 이전의 문자 위치에 액세스하도록 합니다. </summary>
      <returns>
        <see cref="M:System.Text.DecoderFallbackBuffer.MovePrevious" /> 작업에 성공하면 true이고, 그렇지 않으면 false입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Text.DecoderFallbackBuffer.Remaining">
      <summary>파생 클래스에서 재정의될 때 처리되기 위해 남아 있는 현재 <see cref="T:System.Text.DecoderFallbackBuffer" /> 개체의 문자 수를 가져옵니다.</summary>
      <returns>아직 처리되지 않은 현재 대체 버퍼의 문자 수입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.DecoderFallbackBuffer.Reset">
      <summary>이 대체 버퍼와 관련된 모든 데이터 및 상태 정보를 초기화합니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Text.DecoderFallbackException">
      <summary>디코더 대체(fallback) 작업이 실패하면 throw되는 예외입니다.이 클래스는 상속될 수 없습니다.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.DecoderFallbackException.#ctor">
      <summary>
        <see cref="T:System.Text.DecoderFallbackException" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="M:System.Text.DecoderFallbackException.#ctor(System.String)">
      <summary>
        <see cref="T:System.Text.DecoderFallbackException" /> 클래스의 새 인스턴스를 초기화합니다.오류 메시지를 지정하는 매개 변수입니다.</summary>
      <param name="message">오류 메시지입니다.</param>
    </member>
    <member name="M:System.Text.DecoderFallbackException.#ctor(System.String,System.Byte[],System.Int32)">
      <summary>
        <see cref="T:System.Text.DecoderFallbackException" /> 클래스의 새 인스턴스를 초기화합니다.매개 변수는 오류 메시지, 디코딩되는 바이트 배열 및 디코딩할 수 없는 바이트의 인덱스를 지정합니다.</summary>
      <param name="message">오류 메시지입니다.</param>
      <param name="bytesUnknown">입력 바이트 배열입니다.</param>
      <param name="index">바이트의 <paramref name="bytesUnknown" />에서 디코딩할 수 없는 인덱스 위치입니다.</param>
    </member>
    <member name="M:System.Text.DecoderFallbackException.#ctor(System.String,System.Exception)">
      <summary>
        <see cref="T:System.Text.DecoderFallbackException" /> 클래스의 새 인스턴스를 초기화합니다.매개 변수는 이 예외를 발생시킨 내부 예외와 오류 메시지를 지정합니다.</summary>
      <param name="message">오류 메시지입니다.</param>
      <param name="innerException">이 예외를 발생시킨 예외입니다.</param>
    </member>
    <member name="P:System.Text.DecoderFallbackException.BytesUnknown">
      <summary>예외를 발생시킨 입력 바이트 시퀀스를 가져옵니다.</summary>
      <returns>디코딩할 수 없는 입력 바이트 배열입니다. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Text.DecoderFallbackException.Index">
      <summary>입력 바이트 시퀀스에서 예외를 발생시킨 바이트의 인덱스 위치를 가져옵니다.</summary>
      <returns>입력 바이트 배열에서 디코딩할 수 없는 바이트의 인덱스 위치입니다.인덱스 위치는 0부터 시작합니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Text.DecoderReplacementFallback">
      <summary>출력 문자로 변환할 수 없는 인코딩된 입력 바이트 시퀀스에 대해 대체(fallback)라고 하는 오류 처리 메커니즘을 제공합니다.대체(fallback)는 디코딩된 입력 바이트 시퀀스 대신 사용자가 지정한 대체 문자열을 내보냅니다.이 클래스는 상속될 수 없습니다.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.DecoderReplacementFallback.#ctor">
      <summary>
        <see cref="T:System.Text.DecoderReplacementFallback" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="M:System.Text.DecoderReplacementFallback.#ctor(System.String)">
      <summary>지정된 대체 문자열을 사용하여 <see cref="T:System.Text.DecoderReplacementFallback" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="replacement">디코딩 작업에서 디코딩할 수 없는 입력 바이트 시퀀스 대신 내보내는 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="replacement" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="replacement" />에 잘못된 서로게이트 쌍이 들어 있는 경우.즉, 서로게이트 쌍이 하나의 high surrogate 구성 요소와 하나의 low surrogate 구성 요소로 구성되어 있지 않은 경우</exception>
    </member>
    <member name="M:System.Text.DecoderReplacementFallback.CreateFallbackBuffer">
      <summary>이 <see cref="T:System.Text.DecoderReplacementFallback" /> 개체의 대체 문자열로 초기화되는 <see cref="T:System.Text.DecoderFallbackBuffer" /> 개체를 만듭니다.</summary>
      <returns>원래 디코딩 작업의 입력 대신 사용할 문자열을 지정하는 <see cref="T:System.Text.DecoderFallbackBuffer" /> 개체입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Text.DecoderReplacementFallback.DefaultString">
      <summary>
        <see cref="T:System.Text.DecoderReplacementFallback" /> 개체의 값인 대체 문자열을 가져옵니다.</summary>
      <returns>디코딩할 수 없는 입력 바이트 시퀀스 대신 내보내는 대체 문자열입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.DecoderReplacementFallback.Equals(System.Object)">
      <summary>지정한 개체의 값이 <see cref="T:System.Text.DecoderReplacementFallback" /> 개체와 같은지 여부를 나타냅니다.</summary>
      <returns>
        <paramref name="value" />가 <see cref="P:System.Text.DecoderReplacementFallback.DefaultString" /> 속성이 현재 <see cref="T:System.Text.DecoderReplacementFallback" /> 개체의 <see cref="P:System.Text.DecoderReplacementFallback.DefaultString" /> 속성과 같은 <see cref="T:System.Text.DecoderReplacementFallback" /> 개체이면 true이고, 그렇지 않으면 false입니다. </returns>
      <param name="value">
        <see cref="T:System.Text.DecoderReplacementFallback" /> 개체</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.DecoderReplacementFallback.GetHashCode">
      <summary>
        <see cref="T:System.Text.DecoderReplacementFallback" /> 개체의 값에 대한 해시 코드를 검색합니다.</summary>
      <returns>개체의 값에 대한 해시 코드입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Text.DecoderReplacementFallback.MaxCharCount">
      <summary>
        <see cref="T:System.Text.DecoderReplacementFallback" /> 개체에 대한 대체 문자열의 문자 수를 가져옵니다.</summary>
      <returns>디코딩할 수 없는 바이트 시퀀스 대신 내보내는 문자열의 문자 수, 즉 <see cref="P:System.Text.DecoderReplacementFallback.DefaultString" /> 속성에서 반환된 문자열의 길이입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Text.Encoder">
      <summary>문자 집합을 바이트 시퀀스로 변환합니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoder.#ctor">
      <summary>
        <see cref="T:System.Text.Encoder" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Text.Encoder.Convert(System.Char[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Int32,System.Boolean,System.Int32@,System.Int32@,System.Boolean@)">
      <summary>유니코드 문자 배열을 인코딩된 바이트 시퀀스로 변환하고 그 결과를 바이트 배열로 저장합니다.</summary>
      <param name="chars">변환할 문자 배열입니다.</param>
      <param name="charIndex">변환할 <paramref name="chars" />의 첫 번째 요소입니다.</param>
      <param name="charCount">
        <paramref name="chars" />의 요소 중에서 변환할 요소의 수입니다.</param>
      <param name="bytes">변환된 바이트가 저장되는 배열입니다.</param>
      <param name="byteIndex">데이터가 저장된 <paramref name="bytes" />의 첫 번째 요소입니다.</param>
      <param name="byteCount">변환에 사용할 <paramref name="bytes" />의 최대 요소 수입니다.</param>
      <param name="flush">더 이상 변환할 데이터가 없음을 나타내려면 true이고, 그렇지 않으면 false입니다.</param>
      <param name="charsUsed">이 메서드가 반환할 때 변환에 사용된 <paramref name="chars" />의 문자 수를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="bytesUsed">이 메서드가 반환할 때 변환에 의해 생성된 바이트 수를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <param name="completed">이 메서드가 반환할 때 <paramref name="charCount" />에서 지정한 모든 문자가 변환되었으면 true를 포함하고, 그렇지 않으면 false를 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> 또는 <paramref name="bytes" />가 null(Nothing)인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charIndex" />, <paramref name="charCount" />, <paramref name="byteIndex" /> 또는 <paramref name="byteCount" />가 0보다 작은 경우또는<paramref name="chars" />의 길이입니다. - <paramref name="charIndex" />가 <paramref name="charCount" />보다 작은 경우또는<paramref name="bytes" />의 길이입니다. - <paramref name="byteIndex" />가 <paramref name="byteCount" />보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">출력 버퍼가 너무 작아 변환된 입력을 모두 포함할 수 없는 경우.출력 버퍼는 <see cref="Overload:System.Text.Encoder.GetByteCount" /> 메서드에서 지시하는 크기보다 크거나 같아야 합니다.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.-및-<see cref="P:System.Text.Encoder.Fallback" />이 <see cref="T:System.Text.EncoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Text.Encoder.Fallback">
      <summary>현재 <see cref="T:System.Text.Encoder" /> 개체에 대한 <see cref="T:System.Text.EncoderFallback" /> 개체를 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Text.EncoderFallback" /> 개체</returns>
      <exception cref="T:System.ArgumentNullException">set 작업의 값이 null(Nothing)인 경우</exception>
      <exception cref="T:System.ArgumentException">현재 <see cref="T:System.Text.EncoderFallbackBuffer" /> 개체에 아직 인코딩되지 않은 데이터가 들어 있어, 설정 작업에서 새 값을 할당할 수 없는 경우 </exception>
      <exception cref="T:System.Text.EncoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.-및-<see cref="P:System.Text.Encoder.Fallback" />이 <see cref="T:System.Text.EncoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Text.Encoder.FallbackBuffer">
      <summary>현재 <see cref="T:System.Text.Encoder" /> 개체와 관련된 <see cref="T:System.Text.EncoderFallbackBuffer" /> 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Text.EncoderFallbackBuffer" /> 개체</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoder.GetByteCount(System.Char[],System.Int32,System.Int32,System.Boolean)">
      <summary>파생 클래스에서 재정의될 때 지정한 문자 배열의 문자 집합을 인코딩하여 생성되는 바이트 수를 계산합니다.매개 변수는 계산 후 인코더의 내부 상태를 지울지 여부를 나타냅니다.</summary>
      <returns>지정한 문자 및 내부 버퍼의 문자를 인코딩하여 생성되는 바이트 수입니다.</returns>
      <param name="chars">인코딩할 문자 집합이 들어 있는 문자 배열입니다. </param>
      <param name="index">인코딩할 첫 번째 문자의 인덱스입니다. </param>
      <param name="count">인코딩할 문자 수입니다. </param>
      <param name="flush">계산 후 인코더의 내부 상태를 지우도록 시뮬레이션하려면 true이고, 그렇지 않으면 false입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 또는 <paramref name="count" />가 0보다 작은 경우또는 <paramref name="index" /> 및 <paramref name="count" />가 <paramref name="chars" />의 유효한 범위를 나타내지 않는 경우 </exception>
      <exception cref="T:System.Text.EncoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.-및-<see cref="P:System.Text.Encoder.Fallback" />이 <see cref="T:System.Text.EncoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.Encoder.GetBytes(System.Char[],System.Int32,System.Int32,System.Byte[],System.Int32,System.Boolean)">
      <summary>파생 클래스에서 재정의될 때 지정한 문자 배열의 문자 집합 및 내부 버퍼의 모든 문자를 지정한 바이트 배열로 인코딩합니다.매개 변수는 변환 후 인코더의 내부 상태를 지울지 여부를 나타냅니다.</summary>
      <returns>
        <paramref name="bytes" />에 써지는 실제 바이트 수입니다.</returns>
      <param name="chars">인코딩할 문자 집합이 들어 있는 문자 배열입니다. </param>
      <param name="charIndex">인코딩할 첫 번째 문자의 인덱스입니다. </param>
      <param name="charCount">인코딩할 문자 수입니다. </param>
      <param name="bytes">결과 바이트 시퀀스를 포함할 바이트 배열입니다. </param>
      <param name="byteIndex">결과 바이트 시퀀스를 쓰기 시작할 인덱스입니다. </param>
      <param name="flush">변환 후 인코더의 내부 상태를 지우려면 true이고, 그렇지 않으면 false입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" /> 가 null (Nothing)인 경우또는 <paramref name="bytes" />가 null (Nothing)인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charIndex" />, <paramref name="charCount" /> 또는 <paramref name="byteIndex" />가 0보다 작은 경우또는 <paramref name="charIndex" /> 및 <paramref name="charCount" />가 <paramref name="chars" />의 유효한 범위를 나타내지 않는 경우또는 <paramref name="byteIndex" />가 <paramref name="bytes" />의 유효한 인덱스가 아닌 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="bytes" />의 용량(<paramref name="byteIndex" /> ~ 배열 끝)이 부족해서 결과 바이트를 수용할 수 없는 경우 </exception>
      <exception cref="T:System.Text.EncoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.-및-<see cref="P:System.Text.Encoder.Fallback" />이 <see cref="T:System.Text.EncoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.Encoder.Reset">
      <summary>파생 클래스에서 재정의될 때 인코더를 다시 초기 상태로 설정합니다.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Text.EncoderExceptionFallback">
      <summary>출력 바이트 시퀀스로 변환할 수 없는 입력 문자에 대체(fallback)라고 하는 오류 처리 메커니즘을 제공합니다.입력 문자를 출력 바이트 시퀀스로 변환할 수 없으면 대체(fallback)는 예외를 throw합니다.이 클래스는 상속될 수 없습니다.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.EncoderExceptionFallback.#ctor">
      <summary>
        <see cref="T:System.Text.EncoderExceptionFallback" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Text.EncoderExceptionFallback.CreateFallbackBuffer">
      <summary>문자 시퀀스를 바이트 시퀀스로 변환할 수 없으면 인코더 대체(fallback) 버퍼는 예외를 throw합니다.</summary>
      <returns>문자 시퀀스를 인코딩할 수 없으면 인코더 대체(fallback) 버퍼는 예외를 throw합니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.EncoderExceptionFallback.Equals(System.Object)">
      <summary>현재 <see cref="T:System.Text.EncoderExceptionFallback" /> 개체와 지정된 개체가 같은지를 나타냅니다.</summary>
      <returns>
        <paramref name="value" />가 null(Visual Basic .NET에서는 Nothing)이 아니고 <see cref="T:System.Text.EncoderExceptionFallback" /> 개체이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">
        <see cref="T:System.Text.EncoderExceptionFallback" /> 클래스에서 파생되는 개체입니다.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.EncoderExceptionFallback.GetHashCode">
      <summary>이 인스턴스의 해시 코드를 검색합니다.</summary>
      <returns>반환 값은 항상 같은 임의의 값이며 특별한 의미가 없습니다. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Text.EncoderExceptionFallback.MaxCharCount">
      <summary>이 인스턴스가 반환할 수 있는 최대 문자 수를 가져옵니다.</summary>
      <returns>반환 값은 항상 0입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Text.EncoderFallback">
      <summary>인코딩된 출력 바이트 시퀀스로 변환할 수 없는 입력 문자에 대해 대체(fallback)라는 실패 처리 메커니즘을 제공합니다. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.EncoderFallback.#ctor">
      <summary>
        <see cref="T:System.Text.EncoderFallback" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Text.EncoderFallback.CreateFallbackBuffer">
      <summary>파생 클래스에서 재정의된 경우 <see cref="T:System.Text.EncoderFallbackBuffer" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <returns>인코더에 대해 fallback 버퍼를 제공하는 개체입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Text.EncoderFallback.ExceptionFallback">
      <summary>입력 문자를 인코딩할 수 없는 경우 예외를 throw하는 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Text.EncoderFallback" /> 클래스에서 파생된 형식입니다.기본값은 <see cref="T:System.Text.EncoderExceptionFallback" /> 개체입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Text.EncoderFallback.MaxCharCount">
      <summary>파생 클래스에서 재정의될 때 현재 <see cref="T:System.Text.EncoderFallback" /> 개체가 반환할 수 있는 최대 문자 수를 가져옵니다.</summary>
      <returns>현재 <see cref="T:System.Text.EncoderFallback" /> 개체가 반환할 수 있는 최대 문자 수입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Text.EncoderFallback.ReplacementFallback">
      <summary>인코딩할 수 없는 입력 문자 대신 대체 문자열을 출력하는 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Text.EncoderFallback" /> 클래스에서 파생된 형식입니다.기본값은 알 수 없는 입력 문자를 물음표 문자("?", U+003F)로 대체하는 <see cref="T:System.Text.EncoderReplacementFallback" /> 개체입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Text.EncoderFallbackBuffer">
      <summary>입력 문자를 인코드를 할 수 없을 경우 fallback 처리기가 인코더로 대체 문자열을 반환할 수 있는 버퍼를 제공합니다. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.EncoderFallbackBuffer.#ctor">
      <summary>
        <see cref="T:System.Text.EncoderFallbackBuffer" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Text.EncoderFallbackBuffer.Fallback(System.Char,System.Char,System.Int32)">
      <summary>파생 클래스에서 재정의될 때 지정된 서로게이트 쌍을 처리하도록 대체 버퍼를 준비합니다.</summary>
      <returns>대체 버퍼에서 <paramref name="charUnknownHigh" /> 및 <paramref name="charUnknownLow" />를 처리할 수 있으면 true이고, 대체 버퍼에서 서로게이트 쌍을 무시하면 false입니다.</returns>
      <param name="charUnknownHigh">입력 쌍의 high surrogate입니다.</param>
      <param name="charUnknownLow">입력 쌍의 low surrogate입니다.</param>
      <param name="index">입력 버퍼에 있는 서로게이트 쌍의 인덱스 위치입니다.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.EncoderFallbackBuffer.Fallback(System.Char,System.Int32)">
      <summary>파생 클래스에서 재정의될 때 지정된 입력 문자를 처리하도록 대체 버퍼를 준비합니다. </summary>
      <returns>대체 버퍼에서 <paramref name="charUnknown" />을 처리할 수 있으면 true이고, 대체 버퍼에서 <paramref name="charUnknown" />을 무시하면 false입니다.</returns>
      <param name="charUnknown">입력 문자입니다.</param>
      <param name="index">입력 버퍼에 있는 문자의 인덱스 위치입니다.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.EncoderFallbackBuffer.GetNextChar">
      <summary>파생 클래스에서 재정의될 때 대체 버퍼에서 다음 문자를 검색합니다.</summary>
      <returns>대체 버퍼에 있는 다음 문자입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.EncoderFallbackBuffer.MovePrevious">
      <summary>파생 클래스에서 재정의될 때 <see cref="M:System.Text.EncoderFallbackBuffer.GetNextChar" /> 메서드에 대한 다음 호출에서 데이터 버퍼의 현재 문자 위치 이전의 문자 위치에 액세스하도록 합니다. </summary>
      <returns>
        <see cref="M:System.Text.EncoderFallbackBuffer.MovePrevious" /> 작업에 성공하면 true이고, 그렇지 않으면 false입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Text.EncoderFallbackBuffer.Remaining">
      <summary>파생 클래스에서 재정의될 때 처리되기 위해 남아 있는 현재 <see cref="T:System.Text.EncoderFallbackBuffer" /> 개체의 문자 수를 가져옵니다.</summary>
      <returns>아직 처리되지 않은 현재 대체 버퍼의 문자 수입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.EncoderFallbackBuffer.Reset">
      <summary>이 대체 버퍼와 관련된 모든 데이터 및 상태 정보를 초기화합니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Text.EncoderFallbackException">
      <summary>인코더 대체(fallback) 작업이 실패하면 throw되는 예외입니다.이 클래스는 상속될 수 없습니다.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.EncoderFallbackException.#ctor">
      <summary>
        <see cref="T:System.Text.EncoderFallbackException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Text.EncoderFallbackException.#ctor(System.String)">
      <summary>
        <see cref="T:System.Text.EncoderFallbackException" /> 클래스의 새 인스턴스를 초기화합니다.오류 메시지를 지정하는 매개 변수입니다.</summary>
      <param name="message">오류 메시지입니다.</param>
    </member>
    <member name="M:System.Text.EncoderFallbackException.#ctor(System.String,System.Exception)">
      <summary>
        <see cref="T:System.Text.EncoderFallbackException" /> 클래스의 새 인스턴스를 초기화합니다.매개 변수는 이 예외를 발생시킨 내부 예외와 오류 메시지를 지정합니다.</summary>
      <param name="message">오류 메시지입니다.</param>
      <param name="innerException">이 예외를 발생시킨 예외입니다.</param>
    </member>
    <member name="P:System.Text.EncoderFallbackException.CharUnknown">
      <summary>예외를 발생시킨 입력 문자를 가져옵니다.</summary>
      <returns>인코딩할 수 없는 문자입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Text.EncoderFallbackException.CharUnknownHigh">
      <summary>예외를 발생시킨 서로게이트 쌍의 상위 구성 요소 문자를 가져옵니다.</summary>
      <returns>인코딩할 수 없는 서로게이트 쌍의 상위 구성 요소 문자입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Text.EncoderFallbackException.CharUnknownLow">
      <summary>예외를 발생시킨 서로게이트 쌍의 하위 구성 요소 문자를 가져옵니다.</summary>
      <returns>인코딩할 수 없는 서로게이트 쌍의 하위 구성 요소 문자입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Text.EncoderFallbackException.Index">
      <summary>입력 버퍼에서 예외를 발생시킨 문자의 인덱스 위치를 가져옵니다.</summary>
      <returns>입력 버퍼에서 인코딩할 수 없는 문자의 인덱스 위치입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.EncoderFallbackException.IsUnknownSurrogate">
      <summary>예외를 발생시킨 입력이 서로게이트 쌍인지 여부를 나타냅니다.</summary>
      <returns>입력이 서로게이트 쌍이면 true이고, 그렇지 않으면 false입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Text.EncoderReplacementFallback">
      <summary>출력 바이트 시퀀스로 변환할 수 없는 입력 문자에 대체(fallback)라고 하는 오류 처리 메커니즘을 제공합니다.대체(fallback)는 원래 입력 문자 대신 사용자가 지정한 대체 문자열을 사용합니다.이 클래스는 상속될 수 없습니다.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.EncoderReplacementFallback.#ctor">
      <summary>
        <see cref="T:System.Text.EncoderReplacementFallback" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Text.EncoderReplacementFallback.#ctor(System.String)">
      <summary>지정된 대체 문자열을 사용하여 <see cref="T:System.Text.EncoderReplacementFallback" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="replacement">인코딩 작업에서 인코딩할 수 없는 입력 문자 대신 변환되는 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="replacement" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="replacement" />에 잘못된 서로게이트 쌍이 들어 있는 경우.즉, 서로게이트가 하나의 high surrogate 구성 요소와 하나의 low surrogate 구성 요소로 구성되어 있지 않은 경우</exception>
    </member>
    <member name="M:System.Text.EncoderReplacementFallback.CreateFallbackBuffer">
      <summary>이 <see cref="T:System.Text.EncoderReplacementFallback" /> 개체의 대체 문자열로 초기화되는 <see cref="T:System.Text.EncoderFallbackBuffer" /> 개체를 만듭니다.</summary>
      <returns>이 <see cref="T:System.Text.EncoderReplacementFallback" /> 개체와 같은 <see cref="T:System.Text.EncoderFallbackBuffer" /> 개체입니다. </returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Text.EncoderReplacementFallback.DefaultString">
      <summary>
        <see cref="T:System.Text.EncoderReplacementFallback" /> 개체의 값인 대체 문자열을 가져옵니다.</summary>
      <returns>인코딩할 수 없는 입력 문자 대신 사용되는 대체 문자열입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.EncoderReplacementFallback.Equals(System.Object)">
      <summary>지정한 개체의 값이 <see cref="T:System.Text.EncoderReplacementFallback" /> 개체와 같은지 여부를 나타냅니다.</summary>
      <returns>
        <paramref name="value" /> 매개 변수가 <see cref="T:System.Text.EncoderReplacementFallback" /> 개체를 지정하고 해당 개체의 대체 문자열이 이 <see cref="T:System.Text.EncoderReplacementFallback" /> 개체의 대체 문자열과 같으면 true이고, 그렇지 않으면 false입니다. </returns>
      <param name="value">
        <see cref="T:System.Text.EncoderReplacementFallback" /> 개체</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.EncoderReplacementFallback.GetHashCode">
      <summary>
        <see cref="T:System.Text.EncoderReplacementFallback" /> 개체의 값에 대한 해시 코드를 검색합니다.</summary>
      <returns>개체의 값에 대한 해시 코드입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Text.EncoderReplacementFallback.MaxCharCount">
      <summary>
        <see cref="T:System.Text.EncoderReplacementFallback" /> 개체에 대한 대체 문자열의 문자 수를 가져옵니다.</summary>
      <returns>인코딩할 수 없는 입력 문자 대신 사용되는 문자열의 문자 수입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Text.Encoding">
      <summary>문자 인코딩을 나타냅니다.이 형식에 대 한.NET Framework 소스 코드를 찾아보려면 참조는 참조 원본.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.#ctor">
      <summary>
        <see cref="T:System.Text.Encoding" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Text.Encoding.#ctor(System.Int32)">
      <summary>지정한 코드 페이지에 해당하는 <see cref="T:System.Text.Encoding" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="codePage">기본 설정 인코딩의 코드 페이지 식별자입니다.또는 기본 인코딩을 사용하려면 0을 사용합니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="codePage" />가 0보다 작은 경우 </exception>
    </member>
    <member name="M:System.Text.Encoding.#ctor(System.Int32,System.Text.EncoderFallback,System.Text.DecoderFallback)">
      <summary>새 인스턴스를 초기화는 <see cref="T:System.Text.Encoding" /> 지정된 인코더 및 디코더 대체 (fallback) 전략을 지정한 코드 페이지에 해당 하는 클래스입니다. </summary>
      <param name="codePage">인코딩 코드 페이지 식별자입니다. </param>
      <param name="encoderFallback">현재 인코딩으로 문자를 인코딩할 수 없는 경우 오류 처리 프로시저를 제공하는 개체입니다. </param>
      <param name="decoderFallback">현재 인코딩으로 바이트 시퀀스를 디코딩할 수 없는 경우 오류 처리 프로시저를 제공하는 개체입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="codePage" />가 0보다 작은 경우 </exception>
    </member>
    <member name="P:System.Text.Encoding.ASCII">
      <summary>ASCII(7비트) 문자 집합에 대한 인코딩을 가져옵니다.</summary>
      <returns>ASCII(7비트) 문자 집합에 대한 인코딩입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Text.Encoding.BigEndianUnicode">
      <summary>big endian 바이트 순서를 사용하는 UTF-16 형식에 대한 인코딩을 가져옵니다.</summary>
      <returns>Big-Endian 바이트 순서를 사용하는 UTF-16 형식에 대한 인코딩 개체입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.Clone">
      <summary>파생 클래스에서 재정의될 때 현재 <see cref="T:System.Text.Encoding" /> 개체의 단순 복사본을 만듭니다.</summary>
      <returns>현재 <see cref="T:System.Text.Encoding" /> 개체의 복사본입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Text.Encoding.CodePage">
      <summary>파생 클래스에서 재정의될 때 현재 <see cref="T:System.Text.Encoding" />의 코드 페이지 식별자를 가져옵니다.</summary>
      <returns>현재 <see cref="T:System.Text.Encoding" />의 코드 페이지 식별자입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.Convert(System.Text.Encoding,System.Text.Encoding,System.Byte[])">
      <summary>전체 바이트 배열의 인코딩을 변환합니다.</summary>
      <returns>
        <paramref name="bytes" />를 <paramref name="srcEncoding" />에서 <paramref name="dstEncoding" />으로 변환한 결과를 포함하는 <see cref="T:System.Byte" /> 형식의 배열입니다.</returns>
      <param name="srcEncoding">
        <paramref name="bytes" />의 인코딩 형식입니다. </param>
      <param name="dstEncoding">대상 인코딩 형식입니다. </param>
      <param name="bytes">변환할 바이트입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="srcEncoding" />가 null인 경우또는 <paramref name="dstEncoding" />가 null인 경우또는 <paramref name="bytes" />가 null인 경우 </exception>
      <exception cref="T:System.Text.DecoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및srcEncoding 합니다.<see cref="P:System.Text.Encoding.DecoderFallback" />이 <see cref="T:System.Text.DecoderExceptionFallback" />로 설정됩니다.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및dstEncoding 합니다.<see cref="P:System.Text.Encoding.EncoderFallback" />이 <see cref="T:System.Text.EncoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.Convert(System.Text.Encoding,System.Text.Encoding,System.Byte[],System.Int32,System.Int32)">
      <summary>바이트 배열의 바이트 범위를 한 인코딩에서 다른 인코딩으로 변환합니다.</summary>
      <returns>
        <paramref name="bytes" />의 바이트 범위를 <paramref name="srcEncoding" />에서 <paramref name="dstEncoding" />으로 변환한 결과를 포함하는 <see cref="T:System.Byte" /> 형식의 배열입니다.</returns>
      <param name="srcEncoding">소스 배열 <paramref name="bytes" />의 인코딩입니다. </param>
      <param name="dstEncoding">출력 배열의 인코딩입니다. </param>
      <param name="bytes">변환할 바이트 배열입니다. </param>
      <param name="index">변환할 <paramref name="bytes" />에 있는 첫째 요소의 인덱스입니다. </param>
      <param name="count">변환할 바이트 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="srcEncoding" />가 null인 경우또는 <paramref name="dstEncoding" />가 null인 경우또는 <paramref name="bytes" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 및 <paramref name="count" />가 바이트 배열의 유효한 범위를 지정하지 않는 경우 </exception>
      <exception cref="T:System.Text.DecoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및srcEncoding 합니다.<see cref="P:System.Text.Encoding.DecoderFallback" />이 <see cref="T:System.Text.DecoderExceptionFallback" />로 설정됩니다.</exception>
      <exception cref="T:System.Text.EncoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및dstEncoding 합니다.<see cref="P:System.Text.Encoding.EncoderFallback" />이 <see cref="T:System.Text.EncoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Text.Encoding.DecoderFallback">
      <summary>현재 <see cref="T:System.Text.Encoding" /> 개체에 대한 <see cref="T:System.Text.DecoderFallback" /> 개체를 가져오거나 설정합니다.</summary>
      <returns>현재 <see cref="T:System.Text.Encoding" /> 개체에 대한 디코더 fallback 개체입니다. </returns>
      <exception cref="T:System.ArgumentNullException">set 작업의 값이 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">현재 <see cref="T:System.Text.Encoding" /> 개체가 읽기 전용이기 때문에 set 작업에 값을 할당할 수 없습니다.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Text.Encoding.EncoderFallback">
      <summary>현재 <see cref="T:System.Text.Encoding" /> 개체에 대한 <see cref="T:System.Text.EncoderFallback" /> 개체를 가져오거나 설정합니다.</summary>
      <returns>현재 <see cref="T:System.Text.Encoding" /> 개체에 대한 인코더 fallback 개체입니다. </returns>
      <exception cref="T:System.ArgumentNullException">set 작업의 값이 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">현재 <see cref="T:System.Text.Encoding" /> 개체가 읽기 전용이기 때문에 set 작업에 값을 할당할 수 없습니다.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Text.Encoding.EncodingName">
      <summary>파생 클래스에서 재정의되면 현재 인코딩에 대해 사람이 읽을 수 있는 설명을 가져옵니다.</summary>
      <returns>현재 <see cref="T:System.Text.Encoding" />에 대해 사람이 읽을 수 있는 설명입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.Equals(System.Object)">
      <summary>지정한 <see cref="T:System.Object" />가 현재 인스턴스와 같은지 여부를 확인합니다.</summary>
      <returns>
        <paramref name="value" />가 <see cref="T:System.Text.Encoding" />의 인스턴스이고 현재 인스턴스와 같으면 true이고, 그렇지 않으면 false입니다. </returns>
      <param name="value">현재 인스턴스와 비교할 <see cref="T:System.Object" />입니다. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetByteCount(System.Char*,System.Int32)">
      <summary>파생 클래스에서 재정의되면 지정한 문자 포인터에서 시작하는 문자 집합을 인코딩하여 생성되는 바이트 수를 계산합니다.</summary>
      <returns>지정한 문자를 인코딩할 경우 생성되는 바이트 수입니다.</returns>
      <param name="chars">인코딩할 첫째 문자를 가리키는 포인터입니다. </param>
      <param name="count">인코딩할 문자 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" />가 0보다 작은 경우 </exception>
      <exception cref="T:System.Text.EncoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및<see cref="P:System.Text.Encoding.EncoderFallback" />이 <see cref="T:System.Text.EncoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetByteCount(System.Char[])">
      <summary>파생 클래스에서 재정의되면 지정한 문자 배열의 모든 문자를 인코딩하여 생성되는 바이트 수를 계산합니다.</summary>
      <returns>지정한 문자 배열의 모든 문자를 인코딩하여 생성되는 바이트 수입니다.</returns>
      <param name="chars">인코딩할 문자가 들어 있는 문자 배열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" />가 null인 경우 </exception>
      <exception cref="T:System.Text.EncoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및<see cref="P:System.Text.Encoding.EncoderFallback" />이 <see cref="T:System.Text.EncoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetByteCount(System.Char[],System.Int32,System.Int32)">
      <summary>파생 클래스에서 재정의되면 지정한 문자 배열의 문자 집합을 인코딩하여 생성되는 바이트 수를 계산합니다.</summary>
      <returns>지정한 문자를 인코딩할 경우 생성되는 바이트 수입니다.</returns>
      <param name="chars">인코딩할 문자 집합이 들어 있는 문자 배열입니다. </param>
      <param name="index">인코딩할 첫 번째 문자의 인덱스입니다. </param>
      <param name="count">인코딩할 문자 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 또는 <paramref name="count" />가 0보다 작은 경우또는 <paramref name="index" /> 및 <paramref name="count" />가 <paramref name="chars" />의 유효한 범위를 나타내지 않는 경우 </exception>
      <exception cref="T:System.Text.EncoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및<see cref="P:System.Text.Encoding.EncoderFallback" />이 <see cref="T:System.Text.EncoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetByteCount(System.String)">
      <summary>파생 클래스에서 재정의되면 지정된 문자열의 문자를 인코딩하여 생성되는 바이트 수를 계산합니다.</summary>
      <returns>지정한 문자를 인코딩할 경우 생성되는 바이트 수입니다.</returns>
      <param name="s">인코딩할 문자 집합이 포함된 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" />가 null인 경우 </exception>
      <exception cref="T:System.Text.EncoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및<see cref="P:System.Text.Encoding.EncoderFallback" />이 <see cref="T:System.Text.EncoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetBytes(System.Char*,System.Int32,System.Byte*,System.Int32)">
      <summary>파생 클래스에서 재정의되면 지정한 문자 포인터에서 시작하는 문자 집합을 지정한 바이트 포인터에서 시작하여 저장되는 바이트 시퀀스로 인코딩합니다.</summary>
      <returns>
        <paramref name="bytes" /> 매개 변수가 가리키는 위치에 써지는 실제 바이트 수입니다.</returns>
      <param name="chars">인코딩할 첫째 문자를 가리키는 포인터입니다. </param>
      <param name="charCount">인코딩할 문자 수입니다. </param>
      <param name="bytes">결과 바이트 시퀀스를 쓰기 시작할 위치를 가리키는 포인터입니다. </param>
      <param name="byteCount">쓸 최대 바이트 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" />가 null인 경우또는 <paramref name="bytes" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charCount" /> 또는 <paramref name="byteCount" />가 0보다 작은 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="byteCount" />가 결과 바이트 수보다 작은 경우 </exception>
      <exception cref="T:System.Text.EncoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및<see cref="P:System.Text.Encoding.EncoderFallback" />이 <see cref="T:System.Text.EncoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetBytes(System.Char[])">
      <summary>파생 클래스에서 재정의되면 지정한 문자 배열의 모든 문자를 바이트 시퀀스로 인코딩합니다.</summary>
      <returns>지정한 문자 집합을 인코딩한 결과가 포함된 바이트 배열입니다.</returns>
      <param name="chars">인코딩할 문자가 들어 있는 문자 배열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" />가 null인 경우 </exception>
      <exception cref="T:System.Text.EncoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및<see cref="P:System.Text.Encoding.EncoderFallback" />이 <see cref="T:System.Text.EncoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetBytes(System.Char[],System.Int32,System.Int32)">
      <summary>파생 클래스에서 재정의되면 지정한 문자 배열의 문자 집합을 바이트 시퀀스로 인코딩합니다.</summary>
      <returns>지정한 문자 집합을 인코딩한 결과가 포함된 바이트 배열입니다.</returns>
      <param name="chars">인코딩할 문자 집합이 들어 있는 문자 배열입니다. </param>
      <param name="index">인코딩할 첫 번째 문자의 인덱스입니다. </param>
      <param name="count">인코딩할 문자 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 또는 <paramref name="count" />가 0보다 작은 경우또는 <paramref name="index" /> 및 <paramref name="count" />가 <paramref name="chars" />의 유효한 범위를 나타내지 않는 경우 </exception>
      <exception cref="T:System.Text.EncoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및<see cref="P:System.Text.Encoding.EncoderFallback" />이 <see cref="T:System.Text.EncoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetBytes(System.Char[],System.Int32,System.Int32,System.Byte[],System.Int32)">
      <summary>파생 클래스에서 재정의되면 지정한 문자 배열의 문자 집합을 지정한 바이트 배열로 인코딩합니다.</summary>
      <returns>
        <paramref name="bytes" />에 써지는 실제 바이트 수입니다.</returns>
      <param name="chars">인코딩할 문자 집합이 들어 있는 문자 배열입니다. </param>
      <param name="charIndex">인코딩할 첫 번째 문자의 인덱스입니다. </param>
      <param name="charCount">인코딩할 문자 수입니다. </param>
      <param name="bytes">결과 바이트 시퀀스를 포함할 바이트 배열입니다. </param>
      <param name="byteIndex">결과 바이트 시퀀스를 쓰기 시작할 인덱스입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="chars" />가 null인 경우또는 <paramref name="bytes" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charIndex" />, <paramref name="charCount" /> 또는 <paramref name="byteIndex" />가 0보다 작은 경우또는 <paramref name="charIndex" /> 및 <paramref name="charCount" />가 <paramref name="chars" />의 유효한 범위를 나타내지 않는 경우또는 <paramref name="byteIndex" />가 <paramref name="bytes" />의 유효한 인덱스가 아닌 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="bytes" />의 용량(<paramref name="byteIndex" /> ~ 배열 끝)이 부족해서 결과 바이트를 수용할 수 없는 경우 </exception>
      <exception cref="T:System.Text.EncoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및<see cref="P:System.Text.Encoding.EncoderFallback" />이 <see cref="T:System.Text.EncoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetBytes(System.String)">
      <summary>파생 클래스에서 재정의되면 지정한 문자열의 모든 문자를 바이트 시퀀스로 인코딩합니다.</summary>
      <returns>지정한 문자 집합을 인코딩한 결과가 포함된 바이트 배열입니다.</returns>
      <param name="s">인코딩할 문자가 들어 있는 문자열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" />가 null인 경우 </exception>
      <exception cref="T:System.Text.EncoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및<see cref="P:System.Text.Encoding.EncoderFallback" />이 <see cref="T:System.Text.EncoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetBytes(System.String,System.Int32,System.Int32,System.Byte[],System.Int32)">
      <summary>파생 클래스에서 재정의되면 지정한 문자열의 문자 집합을 지정한 바이트 배열로 인코딩합니다.</summary>
      <returns>
        <paramref name="bytes" />에 써지는 실제 바이트 수입니다.</returns>
      <param name="s">인코딩할 문자 집합이 포함된 문자열입니다. </param>
      <param name="charIndex">인코딩할 첫 번째 문자의 인덱스입니다. </param>
      <param name="charCount">인코딩할 문자 수입니다. </param>
      <param name="bytes">결과 바이트 시퀀스를 포함할 바이트 배열입니다. </param>
      <param name="byteIndex">결과 바이트 시퀀스를 쓰기 시작할 인덱스입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="s" />가 null인 경우또는 <paramref name="bytes" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charIndex" />, <paramref name="charCount" /> 또는 <paramref name="byteIndex" />가 0보다 작은 경우또는 <paramref name="charIndex" /> 및 <paramref name="charCount" />가 <paramref name="chars" />의 유효한 범위를 나타내지 않는 경우또는 <paramref name="byteIndex" />가 <paramref name="bytes" />의 유효한 인덱스가 아닌 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="bytes" />의 용량(<paramref name="byteIndex" /> ~ 배열 끝)이 부족해서 결과 바이트를 수용할 수 없는 경우 </exception>
      <exception cref="T:System.Text.EncoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및<see cref="P:System.Text.Encoding.EncoderFallback" />이 <see cref="T:System.Text.EncoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetCharCount(System.Byte*,System.Int32)">
      <summary>파생 클래스에서 재정의되면 지정한 바이트 포인터에서 시작하는 바이트 시퀀스를 디코딩하여 생성되는 문자 수를 계산합니다.</summary>
      <returns>지정한 바이트 시퀀스를 디코딩할 경우 생성되는 문자 수입니다.</returns>
      <param name="bytes">디코딩할 첫째 바이트를 가리키는 포인터입니다. </param>
      <param name="count">디코딩할 바이트 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="count" />가 0보다 작은 경우 </exception>
      <exception cref="T:System.Text.DecoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및<see cref="P:System.Text.Encoding.DecoderFallback" />이 <see cref="T:System.Text.DecoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetCharCount(System.Byte[])">
      <summary>파생 클래스에서 재정의되면 지정한 바이트 배열의 모든 바이트를 디코딩하여 생성되는 문자 수를 계산합니다.</summary>
      <returns>지정한 바이트 시퀀스를 디코딩할 경우 생성되는 문자 수입니다.</returns>
      <param name="bytes">디코딩할 바이트 시퀀스를 포함하는 바이트 배열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" />가 null인 경우 </exception>
      <exception cref="T:System.Text.DecoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및<see cref="P:System.Text.Encoding.DecoderFallback" />이 <see cref="T:System.Text.DecoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetCharCount(System.Byte[],System.Int32,System.Int32)">
      <summary>파생 클래스에서 재정의되면 지정한 바이트 배열의 바이트 시퀀스를 디코딩하여 생성되는 문자 수를 계산합니다.</summary>
      <returns>지정한 바이트 시퀀스를 디코딩할 경우 생성되는 문자 수입니다.</returns>
      <param name="bytes">디코딩할 바이트 시퀀스를 포함하는 바이트 배열입니다. </param>
      <param name="index">디코딩할 첫 번째 바이트의 인덱스입니다. </param>
      <param name="count">디코딩할 바이트 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 또는 <paramref name="count" />가 0보다 작은 경우또는 <paramref name="index" /> 및 <paramref name="count" />가 <paramref name="bytes" />의 유효한 범위를 나타내지 않는 경우 </exception>
      <exception cref="T:System.Text.DecoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및<see cref="P:System.Text.Encoding.DecoderFallback" />이 <see cref="T:System.Text.DecoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetChars(System.Byte*,System.Int32,System.Char*,System.Int32)">
      <summary>파생 클래스에서 재정의되면 지정한 바이트 포인터에서 시작하는 바이트 시퀀스를 지정한 문자 포인터에서 시작하여 저장되는 문자 집합으로 디코딩합니다.</summary>
      <returns>
        <paramref name="chars" /> 매개 변수가 가리키는 위치에 써지는 실제 문자 수입니다.</returns>
      <param name="bytes">디코딩할 첫째 바이트를 가리키는 포인터입니다. </param>
      <param name="byteCount">디코딩할 바이트 수입니다. </param>
      <param name="chars">결과 문자 집합을 쓰기 시작할 위치를 가리키는 포인터입니다. </param>
      <param name="charCount">쓸 최대 문자 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" />가 null인 경우또는 <paramref name="chars" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteCount" /> 또는 <paramref name="charCount" />가 0보다 작은 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="charCount" />가 결과 문자 수보다 작은 경우 </exception>
      <exception cref="T:System.Text.DecoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및<see cref="P:System.Text.Encoding.DecoderFallback" />이 <see cref="T:System.Text.DecoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetChars(System.Byte[])">
      <summary>파생 클래스에서 재정의되면 지정한 바이트 배열의 모든 바이트를 문자 집합으로 디코딩합니다.</summary>
      <returns>지정한 바이트 시퀀스의 디코딩 결과가 포함된 문자 배열입니다.</returns>
      <param name="bytes">디코딩할 바이트 시퀀스를 포함하는 바이트 배열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" />가 null인 경우 </exception>
      <exception cref="T:System.Text.DecoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및<see cref="P:System.Text.Encoding.DecoderFallback" />이 <see cref="T:System.Text.DecoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetChars(System.Byte[],System.Int32,System.Int32)">
      <summary>파생 클래스에서 재정의되면 지정한 바이트 배열의 바이트 시퀀스를 문자 집합으로 디코딩합니다.</summary>
      <returns>지정한 바이트 시퀀스의 디코딩 결과가 포함된 문자 배열입니다.</returns>
      <param name="bytes">디코딩할 바이트 시퀀스를 포함하는 바이트 배열입니다. </param>
      <param name="index">디코딩할 첫 번째 바이트의 인덱스입니다. </param>
      <param name="count">디코딩할 바이트 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 또는 <paramref name="count" />가 0보다 작은 경우또는 <paramref name="index" /> 및 <paramref name="count" />가 <paramref name="bytes" />의 유효한 범위를 나타내지 않는 경우 </exception>
      <exception cref="T:System.Text.DecoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및<see cref="P:System.Text.Encoding.DecoderFallback" />이 <see cref="T:System.Text.DecoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetChars(System.Byte[],System.Int32,System.Int32,System.Char[],System.Int32)">
      <summary>파생 클래스에서 재정의되면 지정한 바이트 배열의 바이트 시퀀스를 지정한 문자 배열로 디코딩합니다.</summary>
      <returns>
        <paramref name="chars" />에 써지는 실제 문자 수입니다.</returns>
      <param name="bytes">디코딩할 바이트 시퀀스를 포함하는 바이트 배열입니다. </param>
      <param name="byteIndex">디코딩할 첫 번째 바이트의 인덱스입니다. </param>
      <param name="byteCount">디코딩할 바이트 수입니다. </param>
      <param name="chars">결과 문자 집합을 포함할 문자 배열입니다. </param>
      <param name="charIndex">결과 문자 집합을 쓰기 시작할 인덱스입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" />가 null인 경우또는 <paramref name="chars" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteIndex" />, <paramref name="byteCount" /> 또는 <paramref name="charIndex" />가 0보다 작은 경우또는 <paramref name="byteindex" /> 및 <paramref name="byteCount" />가 <paramref name="bytes" />의 유효한 범위를 나타내지 않는 경우또는 <paramref name="charIndex" />가 <paramref name="chars" />의 유효한 인덱스가 아닌 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="chars" />의 용량(<paramref name="charIndex" /> ~ 배열 끝)이 부족해서 결과 문자를 수용할 수 없는 경우 </exception>
      <exception cref="T:System.Text.DecoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및<see cref="P:System.Text.Encoding.DecoderFallback" />이 <see cref="T:System.Text.DecoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetDecoder">
      <summary>파생 클래스에서 재정의되면 인코딩된 바이트 시퀀스를 문자 시퀀스로 변환하는 디코더를 가져옵니다.</summary>
      <returns>인코딩된 바이트 시퀀스를 문자 시퀀스로 변환하는 <see cref="T:System.Text.Decoder" />입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetEncoder">
      <summary>파생 클래스에서 재정의되면 유니코드 문자 시퀀스를 인코딩된 바이트 시퀀스로 변환하는 인코더를 가져옵니다.</summary>
      <returns>유니코드 문자 시퀀스를 인코딩된 바이트 시퀀스로 변환하는 <see cref="T:System.Text.Encoder" />입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetEncoding(System.Int32)">
      <summary>지정한 코드 페이지 식별자와 관련된 인코딩을 반환합니다.</summary>
      <returns>지정한 코드 페이지와 관련된 인코딩입니다.</returns>
      <param name="codepage">기본 설정 인코딩의 코드 페이지 식별자입니다.가능한 값은 <see cref="T:System.Text.Encoding" /> 클래스 항목에 나타나는 테이블의 Code Page 열에 나열됩니다.또는 기본 인코딩을 사용하려면 0(제로)을 사용합니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="codepage" />가 0보다 작거나 65535보다 큰 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="codepage" />가 내부 플랫폼에서 지원되지 않는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="codepage" />가 내부 플랫폼에서 지원되지 않는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetEncoding(System.Int32,System.Text.EncoderFallback,System.Text.DecoderFallback)">
      <summary>지정한 코드 페이지 식별자와 관련된 인코딩을 반환합니다.매개 변수는 인코딩할 수 없는 문자 및 디코딩할 수 없는 바이트 시퀀스에 대한 오류 처리기를 지정합니다.</summary>
      <returns>지정한 코드 페이지와 관련된 인코딩입니다.</returns>
      <param name="codepage">기본 설정 인코딩의 코드 페이지 식별자입니다.가능한 값은 <see cref="T:System.Text.Encoding" /> 클래스 항목에 나타나는 테이블의 Code Page 열에 나열됩니다.또는 기본 인코딩을 사용하려면 0(제로)을 사용합니다. </param>
      <param name="encoderFallback">현재 인코딩으로 문자를 인코딩할 수 없는 경우 오류 처리 프로시저를 제공하는 개체입니다. </param>
      <param name="decoderFallback">현재 인코딩으로 바이트 시퀀스를 디코딩할 수 없는 경우 오류 처리 프로시저를 제공하는 개체입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="codepage" />가 0보다 작거나 65535보다 큰 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="codepage" />가 내부 플랫폼에서 지원되지 않는 경우 </exception>
      <exception cref="T:System.NotSupportedException">
        <paramref name="codepage" />가 내부 플랫폼에서 지원되지 않는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetEncoding(System.String)">
      <summary>지정한 코드 페이지 이름과 관련된 인코딩을 반환합니다.</summary>
      <returns>지정한 코드 페이지와 관련된 인코딩입니다.</returns>
      <param name="name">기본 설정 인코딩의 코드 페이지 이름입니다.<see cref="P:System.Text.Encoding.WebName" /> 속성에서 반환된 값이 유효합니다.가능한 값은 <see cref="T:System.Text.Encoding" /> 클래스 항목에 나타나는 테이블의 코드 Name 열에 나열됩니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" />이 올바른 코드 페이지 이름이 아닌 경우또는 <paramref name="name" />으로 표시된 코드 페이지가 내부 플랫폼에서 지원되지 않는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetEncoding(System.String,System.Text.EncoderFallback,System.Text.DecoderFallback)">
      <summary>지정한 코드 페이지 이름과 관련된 인코딩을 반환합니다.매개 변수는 인코딩할 수 없는 문자 및 디코딩할 수 없는 바이트 시퀀스에 대한 오류 처리기를 지정합니다.</summary>
      <returns>지정한 코드 페이지와 관련된 인코딩입니다.</returns>
      <param name="name">기본 설정 인코딩의 코드 페이지 이름입니다.<see cref="P:System.Text.Encoding.WebName" /> 속성에서 반환된 값이 유효합니다.가능한 값은 <see cref="T:System.Text.Encoding" /> 클래스 항목에 나타나는 테이블의 코드 Name 열에 나열됩니다.</param>
      <param name="encoderFallback">현재 인코딩으로 문자를 인코딩할 수 없는 경우 오류 처리 프로시저를 제공하는 개체입니다. </param>
      <param name="decoderFallback">현재 인코딩으로 바이트 시퀀스를 디코딩할 수 없는 경우 오류 처리 프로시저를 제공하는 개체입니다. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" />이 올바른 코드 페이지 이름이 아닌 경우또는 <paramref name="name" />으로 표시된 코드 페이지가 내부 플랫폼에서 지원되지 않는 경우 </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetHashCode">
      <summary>현재 인스턴스의 해시 코드를 반환합니다.</summary>
      <returns>현재 인스턴스에 대한 해시 코드입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetMaxByteCount(System.Int32)">
      <summary>파생 클래스에서 재정의되면 지정한 문자 수의 문자를 인코딩하여 만들 바이트 수를 계산합니다.</summary>
      <returns>지정한 수의 문자를 인코딩할 경우 생성되는 최대 바이트 수입니다.</returns>
      <param name="charCount">인코딩할 문자 수입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="charCount" />가 0보다 작은 경우 </exception>
      <exception cref="T:System.Text.EncoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및<see cref="P:System.Text.Encoding.EncoderFallback" />이 <see cref="T:System.Text.EncoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetMaxCharCount(System.Int32)">
      <summary>파생 클래스에서 재정의되면 지정한 수의 바이트를 디코딩하여 생성되는 최대 문자 수를 계산합니다.</summary>
      <returns>지정한 수의 바이트를 디코딩할 경우 생성되는 최대 문자 수입니다.</returns>
      <param name="byteCount">디코딩할 바이트 수입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteCount" />가 0보다 작은 경우 </exception>
      <exception cref="T:System.Text.DecoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및<see cref="P:System.Text.Encoding.DecoderFallback" />이 <see cref="T:System.Text.DecoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetPreamble">
      <summary>파생 클래스에서 재정의되면 사용되는 인코딩을 지정하는 바이트 시퀀스를 반환합니다.</summary>
      <returns>사용되는 인코딩을 지정하는 바이트 시퀀스가 포함된 바이트 배열입니다.또는 프리앰블이 필요하지 않으면 길이가 0인 바이트 배열입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetString(System.Byte*,System.Int32)">
      <summary>파생된 클래스에서 재정의 되 면 지정 된 문자열로 지정된 된 주소에서 시작 하는 바이트 수를 디코딩합니다. </summary>
      <returns>지정된 바이트 시퀀스에 대한 디코딩 결과가 포함된 문자열입니다. </returns>
      <param name="bytes">바이트 배열에 대 한 포인터입니다. </param>
      <param name="byteCount">디코딩할 바이트 수입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" />null 포인터가입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="byteCount" />가 0보다 작은 경우 </exception>
      <exception cref="T:System.Text.DecoderFallbackException">대체 (fallback) 발생 했습니다 (참조 .NET Framework의 문자 인코딩 에 대 한 전체 설명은)및<see cref="P:System.Text.Encoding.DecoderFallback" />이 <see cref="T:System.Text.DecoderExceptionFallback" />로 설정됩니다. </exception>
    </member>
    <member name="M:System.Text.Encoding.GetString(System.Byte[])">
      <summary>파생 클래스에서 재정의되면 지정한 바이트 배열의 모든 바이트를 문자열로 디코딩합니다.</summary>
      <returns>지정된 바이트 시퀀스에 대한 디코딩 결과가 포함된 문자열입니다.</returns>
      <param name="bytes">디코딩할 바이트 시퀀스를 포함하는 바이트 배열입니다. </param>
      <exception cref="T:System.ArgumentException">바이트 배열에 잘못된 유니코드 코드 포인트가 포함되어 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" />가 null인 경우 </exception>
      <exception cref="T:System.Text.DecoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및<see cref="P:System.Text.Encoding.DecoderFallback" />이 <see cref="T:System.Text.DecoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.GetString(System.Byte[],System.Int32,System.Int32)">
      <summary>파생 클래스에서 재정의되면 지정한 바이트 배열의 바이트 시퀀스를 문자열로 디코딩합니다.</summary>
      <returns>지정된 바이트 시퀀스에 대한 디코딩 결과가 포함된 문자열입니다.</returns>
      <param name="bytes">디코딩할 바이트 시퀀스를 포함하는 바이트 배열입니다. </param>
      <param name="index">디코딩할 첫 번째 바이트의 인덱스입니다. </param>
      <param name="count">디코딩할 바이트 수입니다. </param>
      <exception cref="T:System.ArgumentException">바이트 배열에 잘못된 유니코드 코드 포인트가 포함되어 있습니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 또는 <paramref name="count" />가 0보다 작은 경우또는 <paramref name="index" /> 및 <paramref name="count" />가 <paramref name="bytes" />의 유효한 범위를 나타내지 않는 경우 </exception>
      <exception cref="T:System.Text.DecoderFallbackException">대체(fallback)가 발생한 경우. 자세한 내용은 .NET Framework의 문자 인코딩를 참조하십시오.및<see cref="P:System.Text.Encoding.DecoderFallback" />이 <see cref="T:System.Text.DecoderExceptionFallback" />로 설정됩니다.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Text.Encoding.IsSingleByte">
      <summary>파생 클래스에서 재정의되면 현재 인코딩이 단일 바이트 코드 포인트를 사용하는지를 나타내는 값을 가져옵니다.</summary>
      <returns>현재 <see cref="T:System.Text.Encoding" />이 싱글바이트 코드 포인트를 사용하면 true이고, 그렇지 않으면 false입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Text.Encoding.RegisterProvider(System.Text.EncodingProvider)">
      <summary>인코딩 공급자를 등록합니다. </summary>
      <param name="provider">서브 클래스 <see cref="T:System.Text.EncodingProvider" /> 추가 문자 인코딩에 대 한 액세스를 제공 하는 합니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="provider" />가 null인 경우 </exception>
    </member>
    <member name="P:System.Text.Encoding.Unicode">
      <summary>little endian 바이트 순서를 사용하는 UTF-16 형식에 대한 인코딩을 가져옵니다.</summary>
      <returns>little-endian 바이트 순서를 사용하는 UTF-16 형식에 대한 인코딩입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Text.Encoding.UTF32">
      <summary>little endian 바이트 순서를 사용하는 UTF-32 형식에 대한 인코딩을 가져옵니다.</summary>
      <returns>little-endian 바이트 순서를 사용하는 UTF-32 형식에 대한 인코딩 개체입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Text.Encoding.UTF7">
      <summary>UTF-7 형식에 대한 인코딩을 가져옵니다.</summary>
      <returns>UTF-7 형식에 대한 인코딩입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Text.Encoding.UTF8">
      <summary>UTF-8 형식에 대한 인코딩을 가져옵니다.</summary>
      <returns>UTF-8 형식에 대한 인코딩입니다.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Text.Encoding.WebName">
      <summary>파생 클래스에서 재정의되면 현재 인코딩에 대해 IANA(Internet Assigned Numbers Authority)에 등록된 이름을 가져옵니다.</summary>
      <returns>현재 <see cref="T:System.Text.Encoding" />에 대한 IANA 이름입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Text.EncodingProvider">
      <summary>특정 플랫폼에서 사용할 수 없는 인코딩을 제공 하는 인코딩 공급자에 대 한 기본 클래스를 제공 합니다. </summary>
    </member>
    <member name="M:System.Text.EncodingProvider.#ctor">
      <summary>
        <see cref="T:System.Text.EncodingProvider" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="M:System.Text.EncodingProvider.GetEncoding(System.Int32)">
      <summary>지정한 코드 페이지 식별자와 관련된 인코딩을 반환합니다. </summary>
      <returns>지정한 코드 페이지와 관련 된 인코딩을 즉 또는 null 이 <see cref="T:System.Text.EncodingProvider" /> 에 해당 하는 유효한 인코딩이 반환할 수 없습니다 <paramref name="codepage" />. </returns>
      <param name="codepage">요청 된 인코딩을의 코드 페이지 식별자입니다. </param>
    </member>
    <member name="M:System.Text.EncodingProvider.GetEncoding(System.Int32,System.Text.EncoderFallback,System.Text.DecoderFallback)">
      <summary>지정한 코드 페이지 식별자와 관련된 인코딩을 반환합니다.매개 변수는 인코딩할 수 없는 문자 및 디코딩할 수 없는 바이트 시퀀스에 대한 오류 처리기를 지정합니다.</summary>
      <returns>지정한 코드 페이지와 관련 된 인코딩을 즉 또는 null 이 <see cref="T:System.Text.EncodingProvider" /> 에 해당 하는 유효한 인코딩이 반환할 수 없습니다 <paramref name="codepage" />. </returns>
      <param name="codepage">요청 된 인코딩을의 코드 페이지 식별자입니다. </param>
      <param name="encoderFallback">이 인코딩을 사용 하 여 문자를 인코딩할 수 없는 경우 오류 처리 프로시저를 제공 하는 개체입니다. </param>
      <param name="decoderFallback">바이트 시퀀스를 디코딩할 수 없는 경우이 인코딩을 사용 하 여 오류 처리 프로시저를 제공 하는 개체입니다. </param>
    </member>
    <member name="M:System.Text.EncodingProvider.GetEncoding(System.String)">
      <summary>지정한 이름을 가진 인코딩을 반환 합니다. </summary>
      <returns>지정된 된 이름과 연관 인코딩에 즉 또는 null 이 <see cref="T:System.Text.EncodingProvider" /> 에 해당 하는 유효한 인코딩이 반환할 수 없습니다 <paramref name="name" />.</returns>
      <param name="name">요청 된 인코딩 이름입니다. </param>
    </member>
    <member name="M:System.Text.EncodingProvider.GetEncoding(System.String,System.Text.EncoderFallback,System.Text.DecoderFallback)">
      <summary>지정한 이름과 연결된 인코딩을 반환합니다.매개 변수는 인코딩할 수 없는 문자 및 디코딩할 수 없는 바이트 시퀀스에 대한 오류 처리기를 지정합니다.</summary>
      <returns>지정된 된 이름과 연관 인코딩에 즉 또는 null 이 <see cref="T:System.Text.EncodingProvider" /> 에 해당 하는 유효한 인코딩이 반환할 수 없습니다 <paramref name="name" />. </returns>
      <param name="name">기본 설정 인코딩 이름입니다. </param>
      <param name="encoderFallback">이 인코딩을 사용 하 여 문자를 인코딩할 수 없는 경우 오류 처리 프로시저를 제공 하는 개체입니다. </param>
      <param name="decoderFallback">현재 인코딩으로 바이트 시퀀스를 디코딩할 수 없는 경우 오류 처리 프로시저를 제공하는 개체입니다. </param>
    </member>
  </members>
</doc>