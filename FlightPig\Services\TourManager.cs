using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using FlightPig.Models;

namespace FlightPig.Services
{
    /// <summary>
    /// Manages guided tours including loading, route optimization, and progression
    /// </summary>
    public class TourManager
    {
        private readonly List<Tour> _availableTours;
        private Tour _currentTour;
        private readonly string _toursDataPath;

        public Tour CurrentTour => _currentTour;
        public IReadOnlyList<Tour> AvailableTours => _availableTours.AsReadOnly();

        public event EventHandler<PoiReachedEventArgs> PoiReached;
        public event EventHandler<TourCompletedEventArgs> TourCompleted;

        public TourManager(string toursDataPath = "tours.json")
        {
            _toursDataPath = toursDataPath;
            _availableTours = new List<Tour>();
        }

        /// <summary>
        /// Load tours from JSON file
        /// </summary>
        public async Task LoadToursAsync()
        {
            try
            {
                if (File.Exists(_toursDataPath))
                {
                    var json = await File.ReadAllTextAsync(_toursDataPath);
                    var tours = JsonSerializer.Deserialize<List<Tour>>(json, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _availableTours.Clear();
                    if (tours != null)
                    {
                        _availableTours.AddRange(tours);
                        Console.WriteLine($"Loaded {tours.Count} tours from {_toursDataPath}");
                    }
                }
                else
                {
                    // Create default tours if file doesn't exist
                    await CreateDefaultToursAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading tours: {ex.Message}");
                await CreateDefaultToursAsync();
            }
        }

        /// <summary>
        /// Save tours to JSON file
        /// </summary>
        public async Task SaveToursAsync()
        {
            try
            {
                var json = JsonSerializer.Serialize(_availableTours, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
                await File.WriteAllTextAsync(_toursDataPath, json);
                Console.WriteLine($"Saved {_availableTours.Count} tours to {_toursDataPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving tours: {ex.Message}");
            }
        }

        /// <summary>
        /// Start a guided tour
        /// </summary>
        public Tour StartTour(string tourId)
        {
            var tour = _availableTours.FirstOrDefault(t => t.Id == tourId);
            if (tour == null)
            {
                throw new ArgumentException($"Tour with ID '{tourId}' not found");
            }

            _currentTour = tour;
            _currentTour.IsActive = true;
            _currentTour.StartTime = DateTime.Now;
            _currentTour.CurrentPoiIndex = 0;

            return _currentTour;
        }

        /// <summary>
        /// Stop the current tour
        /// </summary>
        public void StopTour()
        {
            if (_currentTour != null)
            {
                _currentTour.IsActive = false;
                _currentTour = null;
            }
        }

        /// <summary>
        /// Check if aircraft has reached the current POI
        /// </summary>
        public bool CheckPoiReached(AircraftInfo aircraftInfo)
        {
            if (_currentTour?.CurrentPoi == null) return false;

            var currentPoi = _currentTour.CurrentPoi;
            var distance = CalculateDistanceNm(
                aircraftInfo.Latitude, aircraftInfo.Longitude,
                currentPoi.Latitude, currentPoi.Longitude);

            bool reached = distance <= currentPoi.ProximityThresholdNm;

            // Check landing requirement if specified
            if (reached && currentPoi.IsLandingRequired)
            {
                reached = aircraftInfo.OnGround;
            }

            // Check altitude requirements if specified
            if (reached && currentPoi.MinimumAltitude.HasValue)
            {
                reached = aircraftInfo.Altitude >= currentPoi.MinimumAltitude.Value;
            }

            if (reached && currentPoi.MaximumAltitude.HasValue)
            {
                reached = aircraftInfo.Altitude <= currentPoi.MaximumAltitude.Value;
            }

            if (reached)
            {
                PoiReached?.Invoke(this, new PoiReachedEventArgs(currentPoi, _currentTour));
                
                // Advance to next POI
                if (!_currentTour.AdvanceToNextPoi())
                {
                    // Tour completed
                    TourCompleted?.Invoke(this, new TourCompletedEventArgs(_currentTour));
                    StopTour();
                }
            }

            return reached;
        }

        /// <summary>
        /// Get tours suitable for the current aircraft and location
        /// </summary>
        public List<Tour> GetRecommendedTours(AircraftInfo aircraftInfo, double maxDistanceNm = 50)
        {
            return _availableTours.Where(tour =>
            {
                // Check if aircraft is recommended for this tour
                if (tour.RecommendedAircraft.Count > 0 && 
                    !tour.RecommendedAircraft.Any(aircraft => 
                        aircraftInfo.Title.Contains(aircraft, StringComparison.OrdinalIgnoreCase)))
                {
                    return false;
                }

                // Check distance to starting location
                if (tour.StartingLocation != null)
                {
                    var distance = CalculateDistanceNm(
                        aircraftInfo.Latitude, aircraftInfo.Longitude,
                        tour.StartingLocation.Latitude, tour.StartingLocation.Longitude);
                    
                    return distance <= maxDistanceNm;
                }

                return true;
            }).OrderBy(tour =>
            {
                // Order by distance to starting location
                if (tour.StartingLocation != null)
                {
                    return CalculateDistanceNm(
                        aircraftInfo.Latitude, aircraftInfo.Longitude,
                        tour.StartingLocation.Latitude, tour.StartingLocation.Longitude);
                }
                return 0;
            }).ToList();
        }

        /// <summary>
        /// Optimize tour route to minimize zig-zagging
        /// </summary>
        public Tour OptimizeTourRoute(Tour tour)
        {
            if (tour.PointsOfInterest.Count <= 2) return tour;

            // Simple nearest neighbor optimization
            var optimizedPois = new List<PointOfInterest>();
            var remainingPois = new List<PointOfInterest>(tour.PointsOfInterest);

            // Start with the first POI
            var currentPoi = remainingPois.First();
            optimizedPois.Add(currentPoi);
            remainingPois.Remove(currentPoi);

            // Find nearest unvisited POI for each step
            while (remainingPois.Count > 0)
            {
                var nearestPoi = remainingPois.OrderBy(poi =>
                    CalculateDistanceNm(currentPoi.Latitude, currentPoi.Longitude,
                                      poi.Latitude, poi.Longitude)).First();

                optimizedPois.Add(nearestPoi);
                remainingPois.Remove(nearestPoi);
                currentPoi = nearestPoi;
            }

            tour.PointsOfInterest = optimizedPois;
            return tour;
        }

        /// <summary>
        /// Calculate distance between two points in nautical miles
        /// </summary>
        private double CalculateDistanceNm(double lat1, double lon1, double lat2, double lon2)
        {
            const double earthRadiusNm = 3440.065; // Earth radius in nautical miles
            
            var lat1Rad = lat1 * Math.PI / 180;
            var lat2Rad = lat2 * Math.PI / 180;
            var deltaLatRad = (lat2 - lat1) * Math.PI / 180;
            var deltaLonRad = (lon2 - lon1) * Math.PI / 180;

            var a = Math.Sin(deltaLatRad / 2) * Math.Sin(deltaLatRad / 2) +
                    Math.Cos(lat1Rad) * Math.Cos(lat2Rad) *
                    Math.Sin(deltaLonRad / 2) * Math.Sin(deltaLonRad / 2);
            
            var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
            
            return earthRadiusNm * c;
        }

        /// <summary>
        /// Create default tours for demonstration
        /// </summary>
        private async Task CreateDefaultToursAsync()
        {
            _availableTours.Clear();

            // San Francisco Bay Area Tour
            var sfBayTour = new Tour
            {
                Id = "sf-bay-tour",
                Title = "San Francisco Bay Area Scenic Tour",
                Description = "A breathtaking tour of the San Francisco Bay Area's most iconic landmarks",
                Region = "California",
                Country = "United States",
                Difficulty = "Easy",
                EstimatedDurationMinutes = 45,
                RecommendedAircraft = new List<string> { "Cessna", "Piper", "Icon A5" },
                Introduction = "Welcome to the San Francisco Bay Area scenic tour! Today we'll explore some of the most iconic landmarks in one of the world's most beautiful flying regions.",
                Conclusion = "Thank you for joining us on this scenic tour of the San Francisco Bay Area. We hope you enjoyed seeing these magnificent landmarks from the air!",
                Tags = new List<string> { "scenic", "landmarks", "coastal", "urban" },
                MinimumAltitude = 500,
                MaximumAltitude = 3000,
                StartingLocation = new PointOfInterest
                {
                    Id = "ksfo",
                    Name = "San Francisco International Airport",
                    Latitude = 37.6213,
                    Longitude = -122.3790,
                    Description = "Starting point for our Bay Area tour"
                },
                PointsOfInterest = new List<PointOfInterest>
                {
                    new PointOfInterest
                    {
                        Id = "golden-gate",
                        Name = "Golden Gate Bridge",
                        Description = "The iconic Golden Gate Bridge",
                        Latitude = 37.8199,
                        Longitude = -122.4783,
                        Altitude = 1500,
                        Category = "Landmark",
                        TourGuideText = "Welcome to the Golden Gate Bridge, one of the most recognizable structures in the world! Completed in 1937, this Art Deco masterpiece spans 1.7 miles across the Golden Gate strait. The bridge's International Orange color was chosen to enhance visibility in San Francisco's frequent fog and to complement the natural surroundings.",
                        NextPoiInstructions = "Turn southeast and head towards Alcatraz Island. Maintain altitude at 1500 feet and enjoy the view of San Francisco Bay.",
                        Tags = new List<string> { "bridge", "landmark", "iconic" },
                        MinimumAltitude = 1000,
                        MaximumAltitude = 2000,
                        ProximityThresholdNm = 0.3
                    },
                    new PointOfInterest
                    {
                        Id = "alcatraz",
                        Name = "Alcatraz Island",
                        Description = "The infamous former federal prison",
                        Latitude = 37.8267,
                        Longitude = -122.4230,
                        Altitude = 1200,
                        Category = "Historical",
                        TourGuideText = "Below us is Alcatraz Island, home to the infamous federal prison that operated from 1934 to 1963. Known as 'The Rock,' it housed some of America's most notorious criminals including Al Capone. The prison was considered escape-proof, though 36 men attempted to escape in 14 separate attempts. The island is now a popular tourist destination and part of the Golden Gate National Recreation Area.",
                        NextPoiInstructions = "Head east towards the Bay Bridge. You'll see the San Francisco skyline to your right as you approach the bridge.",
                        Tags = new List<string> { "prison", "historical", "island" },
                        MinimumAltitude = 800,
                        MaximumAltitude = 1500,
                        ProximityThresholdNm = 0.4
                    },
                    new PointOfInterest
                    {
                        Id = "bay-bridge",
                        Name = "San Francisco-Oakland Bay Bridge",
                        Description = "The Bay Bridge connecting San Francisco to Oakland",
                        Latitude = 37.7983,
                        Longitude = -122.3778,
                        Altitude = 1800,
                        Category = "Infrastructure",
                        TourGuideText = "This is the San Francisco-Oakland Bay Bridge, often overshadowed by its famous neighbor but equally impressive. Opened in 1936, just six months before the Golden Gate Bridge, it carries significantly more traffic. The eastern span was recently rebuilt and opened in 2013, featuring a single tower suspension design. The bridge is 4.5 miles long, making it one of the longest bridges in the United States.",
                        NextPoiInstructions = "Turn south and head towards the distinctive Transamerica Pyramid in downtown San Francisco.",
                        Tags = new List<string> { "bridge", "infrastructure", "transportation" },
                        MinimumAltitude = 1200,
                        MaximumAltitude = 2200,
                        ProximityThresholdNm = 0.5
                    },
                    new PointOfInterest
                    {
                        Id = "transamerica-pyramid",
                        Name = "Transamerica Pyramid",
                        Description = "San Francisco's distinctive pyramid-shaped skyscraper",
                        Latitude = 37.7952,
                        Longitude = -122.4028,
                        Altitude = 2000,
                        Category = "Architecture",
                        TourGuideText = "The Transamerica Pyramid is San Francisco's most distinctive skyscraper and was the tallest building in the city from 1972 to 2018. Standing 853 feet tall, its unique pyramid shape was designed to allow more light to reach the streets below. The building was controversial when first built but has since become an beloved icon of the San Francisco skyline.",
                        NextPoiInstructions = "Head southwest back towards San Francisco International Airport to complete our tour. Enjoy the panoramic views of the city and bay as we return.",
                        Tags = new List<string> { "skyscraper", "architecture", "downtown" },
                        MinimumAltitude = 1500,
                        MaximumAltitude = 2500,
                        ProximityThresholdNm = 0.3
                    }
                }
            };

            _availableTours.Add(sfBayTour);
            await SaveToursAsync();
        }
    }

    /// <summary>
    /// Event args for POI reached event
    /// </summary>
    public class PoiReachedEventArgs : EventArgs
    {
        public PointOfInterest Poi { get; }
        public Tour Tour { get; }

        public PoiReachedEventArgs(PointOfInterest poi, Tour tour)
        {
            Poi = poi;
            Tour = tour;
        }
    }

    /// <summary>
    /// Event args for tour completed event
    /// </summary>
    public class TourCompletedEventArgs : EventArgs
    {
        public Tour Tour { get; }

        public TourCompletedEventArgs(Tour tour)
        {
            Tour = tour;
        }
    }
}
