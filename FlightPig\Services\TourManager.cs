using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using FlightPig.Models;

namespace FlightPig.Services
{
    /// <summary>
    /// Manages guided tours including loading, route optimization, and progression
    /// </summary>
    public class TourManager
    {
        private readonly List<Tour> _availableTours;
        private Tour _currentTour;
        private readonly string _toursDataPath;

        public Tour CurrentTour => _currentTour;
        public IReadOnlyList<Tour> AvailableTours => _availableTours.AsReadOnly();

        public event EventHandler<PoiReachedEventArgs> PoiReached;
        public event EventHandler<TourCompletedEventArgs> TourCompleted;

        public TourManager(string toursDataPath = "tours.json")
        {
            _toursDataPath = toursDataPath;
            _availableTours = new List<Tour>();
        }

        /// <summary>
        /// Load tours from JSON file
        /// </summary>
        public async Task LoadToursAsync()
        {
            try
            {
                if (File.Exists(_toursDataPath))
                {
                    var json = await File.ReadAllTextAsync(_toursDataPath);
                    var tours = JsonSerializer.Deserialize<List<Tour>>(json, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _availableTours.Clear();
                    if (tours != null)
                    {
                        _availableTours.AddRange(tours);
                        Console.WriteLine($"Loaded {tours.Count} tours from {_toursDataPath}");
                    }
                }
                else
                {
                    // Create default tours if file doesn't exist
                    await CreateDefaultToursAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading tours: {ex.Message}");
                await CreateDefaultToursAsync();
            }
        }

        /// <summary>
        /// Save tours to JSON file
        /// </summary>
        public async Task SaveToursAsync()
        {
            try
            {
                var json = JsonSerializer.Serialize(_availableTours, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });
                await File.WriteAllTextAsync(_toursDataPath, json);
                Console.WriteLine($"Saved {_availableTours.Count} tours to {_toursDataPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving tours: {ex.Message}");
            }
        }

        /// <summary>
        /// Start a guided tour
        /// </summary>
        public Tour StartTour(string tourId)
        {
            var tour = _availableTours.FirstOrDefault(t => t.Id == tourId);
            if (tour == null)
            {
                throw new ArgumentException($"Tour with ID '{tourId}' not found");
            }

            _currentTour = tour;
            _currentTour.IsActive = true;
            _currentTour.StartTime = DateTime.Now;
            _currentTour.CurrentPoiIndex = 0;

            return _currentTour;
        }

        /// <summary>
        /// Stop the current tour
        /// </summary>
        public void StopTour()
        {
            if (_currentTour != null)
            {
                _currentTour.IsActive = false;
                _currentTour = null;
            }
        }

        /// <summary>
        /// Check if aircraft has reached the current POI
        /// </summary>
        public bool CheckPoiReached(AircraftInfo aircraftInfo)
        {
            if (_currentTour?.CurrentPoi == null) return false;

            var currentPoi = _currentTour.CurrentPoi;
            var distance = CalculateDistanceNm(
                aircraftInfo.Latitude, aircraftInfo.Longitude,
                currentPoi.Latitude, currentPoi.Longitude);

            bool reached = distance <= currentPoi.ProximityThresholdNm;

            // Check landing requirement if specified
            if (reached && currentPoi.IsLandingRequired)
            {
                reached = aircraftInfo.OnGround;
            }

            // Check altitude requirements if specified
            if (reached && currentPoi.MinimumAltitude.HasValue)
            {
                reached = aircraftInfo.Altitude >= currentPoi.MinimumAltitude.Value;
            }

            if (reached && currentPoi.MaximumAltitude.HasValue)
            {
                reached = aircraftInfo.Altitude <= currentPoi.MaximumAltitude.Value;
            }

            if (reached)
            {
                PoiReached?.Invoke(this, new PoiReachedEventArgs(currentPoi, _currentTour));
                
                // Advance to next POI
                if (!_currentTour.AdvanceToNextPoi())
                {
                    // Tour completed
                    TourCompleted?.Invoke(this, new TourCompletedEventArgs(_currentTour));
                    StopTour();
                }
            }

            return reached;
        }

        /// <summary>
        /// Get tours suitable for the current aircraft and location
        /// </summary>
        public List<Tour> GetRecommendedTours(AircraftInfo aircraftInfo, double maxDistanceNm = 50)
        {
            return _availableTours.Where(tour =>
            {
                // Check if aircraft is recommended for this tour
                if (tour.RecommendedAircraft.Count > 0 && 
                    !tour.RecommendedAircraft.Any(aircraft => 
                        aircraftInfo.Title.Contains(aircraft, StringComparison.OrdinalIgnoreCase)))
                {
                    return false;
                }

                // Check distance to starting location
                if (tour.StartingLocation != null)
                {
                    var distance = CalculateDistanceNm(
                        aircraftInfo.Latitude, aircraftInfo.Longitude,
                        tour.StartingLocation.Latitude, tour.StartingLocation.Longitude);
                    
                    return distance <= maxDistanceNm;
                }

                return true;
            }).OrderBy(tour =>
            {
                // Order by distance to starting location
                if (tour.StartingLocation != null)
                {
                    return CalculateDistanceNm(
                        aircraftInfo.Latitude, aircraftInfo.Longitude,
                        tour.StartingLocation.Latitude, tour.StartingLocation.Longitude);
                }
                return 0;
            }).ToList();
        }

        /// <summary>
        /// Optimize tour route to minimize zig-zagging
        /// </summary>
        public Tour OptimizeTourRoute(Tour tour)
        {
            if (tour.PointsOfInterest.Count <= 2) return tour;

            // Simple nearest neighbor optimization
            var optimizedPois = new List<PointOfInterest>();
            var remainingPois = new List<PointOfInterest>(tour.PointsOfInterest);

            // Start with the first POI
            var currentPoi = remainingPois.First();
            optimizedPois.Add(currentPoi);
            remainingPois.Remove(currentPoi);

            // Find nearest unvisited POI for each step
            while (remainingPois.Count > 0)
            {
                var nearestPoi = remainingPois.OrderBy(poi =>
                    CalculateDistanceNm(currentPoi.Latitude, currentPoi.Longitude,
                                      poi.Latitude, poi.Longitude)).First();

                optimizedPois.Add(nearestPoi);
                remainingPois.Remove(nearestPoi);
                currentPoi = nearestPoi;
            }

            tour.PointsOfInterest = optimizedPois;
            return tour;
        }

        /// <summary>
        /// Calculate distance between two points in nautical miles
        /// </summary>
        private double CalculateDistanceNm(double lat1, double lon1, double lat2, double lon2)
        {
            const double earthRadiusNm = 3440.065; // Earth radius in nautical miles
            
            var lat1Rad = lat1 * Math.PI / 180;
            var lat2Rad = lat2 * Math.PI / 180;
            var deltaLatRad = (lat2 - lat1) * Math.PI / 180;
            var deltaLonRad = (lon2 - lon1) * Math.PI / 180;

            var a = Math.Sin(deltaLatRad / 2) * Math.Sin(deltaLatRad / 2) +
                    Math.Cos(lat1Rad) * Math.Cos(lat2Rad) *
                    Math.Sin(deltaLonRad / 2) * Math.Sin(deltaLonRad / 2);
            
            var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
            
            return earthRadiusNm * c;
        }

        /// <summary>
        /// Add a generated tour to the available tours
        /// </summary>
        public void AddGeneratedTour(Tour tour)
        {
            if (tour == null) return;

            // Remove any existing tour with the same ID
            _availableTours.RemoveAll(t => t.Id == tour.Id);

            // Add the new tour
            _availableTours.Add(tour);
        }

        /// <summary>
        /// Clear all tours (used when generating new tours for a different location)
        /// </summary>
        public void ClearTours()
        {
            _availableTours.Clear();
        }

        /// <summary>
        /// Create default tours for demonstration (now empty - tours are LLM generated)
        /// </summary>
        private async Task CreateDefaultToursAsync()
        {
            _availableTours.Clear();
            // No default tours - all tours will be generated by LLM based on current position
            await SaveToursAsync();
        }
    }

    /// <summary>
    /// Event args for POI reached event
    /// </summary>
    public class PoiReachedEventArgs : EventArgs
    {
        public PointOfInterest Poi { get; }
        public Tour Tour { get; }

        public PoiReachedEventArgs(PointOfInterest poi, Tour tour)
        {
            Poi = poi;
            Tour = tour;
        }
    }

    /// <summary>
    /// Event args for tour completed event
    /// </summary>
    public class TourCompletedEventArgs : EventArgs
    {
        public Tour Tour { get; }

        public TourCompletedEventArgs(Tour tour)
        {
            Tour = tour;
        }
    }
}
