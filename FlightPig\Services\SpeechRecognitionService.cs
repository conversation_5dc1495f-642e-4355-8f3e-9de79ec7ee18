using System;
using System.IO;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using NAudio.Wave;
using Whisper.net;
using FlightPig.Models;

namespace FlightPig.Services
{
    /// <summary>
    /// Speech recognition service using Whisper
    /// </summary>
    public class SpeechRecognitionService : IDisposable
    {
        private readonly AppSettings _settings;
        private WhisperFactory _whisperFactory;
        private WhisperProcessor _whisperProcessor;
        private WaveInEvent _waveIn;
        private MemoryStream _audioStream;
        private bool _isRecording;
        private bool _isListening;
        private readonly object _lockObject = new object();

        public event EventHandler<string> SpeechRecognized;
        public event EventHandler RecordingStarted;
        public event EventHandler RecordingStopped;

        public SpeechRecognitionService(AppSettings settings)
        {
            _settings = settings;
            InitializeWhisper();
            InitializeAudio();
        }

        private void InitializeWhisper()
        {
            try
            {
                Console.WriteLine("Initializing Whisper speech recognition...");

                // Try different approaches to initialize Whisper
                if (TryInitializeWithBuiltInModel())
                {
                    Console.WriteLine("✓ Whisper initialized successfully with built-in model.");
                    return;
                }

                if (TryInitializeWithLocalModel())
                {
                    Console.WriteLine("✓ Whisper initialized successfully with local model.");
                    return;
                }

                if (TryDownloadAndInitialize())
                {
                    Console.WriteLine("✓ Whisper initialized successfully after download.");
                    return;
                }

                // If all methods fail, disable speech recognition
                Console.WriteLine("❌ Failed to initialize Whisper. Speech recognition will be disabled.");
                Console.WriteLine("To enable speech recognition:");
                Console.WriteLine("1. Download ggml-tiny.bin from https://huggingface.co/ggerganov/whisper.cpp/resolve/main/ggml-tiny.bin");
                Console.WriteLine("2. Place it in the application directory");
                Console.WriteLine("3. Restart FlightPig");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing Whisper: {ex.Message}");
                Console.WriteLine("Speech recognition will be disabled.");
            }
        }

        private bool TryInitializeWithBuiltInModel()
        {
            try
            {
                // Try to use the default model from Whisper.net.Runtime
                Console.WriteLine("Attempting to use built-in Whisper model...");
                _whisperFactory = WhisperFactory.FromPath("ggml-tiny.bin");
                _whisperProcessor = _whisperFactory.CreateBuilder()
                    .WithLanguage(_settings.Whisper.Language)
                    .Build();
                return true;
            }
            catch
            {
                return false;
            }
        }

        private bool TryInitializeWithLocalModel()
        {
            try
            {
                // Try to find model in various locations
                var possiblePaths = new[]
                {
                    "ggml-tiny.bin",
                    Path.Combine("models", "ggml-tiny.bin"),
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ggml-tiny.bin"),
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "models", "ggml-tiny.bin")
                };

                foreach (var path in possiblePaths)
                {
                    if (File.Exists(path))
                    {
                        Console.WriteLine($"Found Whisper model at: {path}");
                        _whisperFactory = WhisperFactory.FromPath(path);
                        _whisperProcessor = _whisperFactory.CreateBuilder()
                            .WithLanguage(_settings.Whisper.Language)
                            .Build();
                        return true;
                    }
                }

                Console.WriteLine("No local Whisper model found.");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to initialize with local model: {ex.Message}");
                return false;
            }
        }

        private bool TryDownloadAndInitialize()
        {
            try
            {
                Console.WriteLine("Attempting to download Whisper tiny model...");
                DownloadWhisperModel();
                _whisperFactory = WhisperFactory.FromPath("ggml-tiny.bin");
                _whisperProcessor = _whisperFactory.CreateBuilder()
                    .WithLanguage(_settings.Whisper.Language)
                    .Build();
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to download and initialize: {ex.Message}");
                return false;
            }
        }

        private void DownloadWhisperModel()
        {
            var modelPath = "ggml-tiny.bin";

            // Check if model already exists
            if (File.Exists(modelPath))
            {
                Console.WriteLine($"Whisper model already exists at {modelPath}");
                return;
            }

            Console.WriteLine("Downloading Whisper tiny model (39MB)...");
            Console.WriteLine("This is a one-time download and may take a few minutes.");

            var modelUrl = "https://huggingface.co/ggerganov/whisper.cpp/resolve/main/ggml-tiny.bin";

            try
            {
                using (var client = new System.Net.Http.HttpClient())
                {
                    client.Timeout = TimeSpan.FromMinutes(10); // 10 minute timeout for download

                    Console.WriteLine("Starting download...");
                    var response = client.GetAsync(modelUrl).Result;
                    response.EnsureSuccessStatusCode();

                    var totalBytes = response.Content.Headers.ContentLength ?? 0;
                    Console.WriteLine($"Downloading {totalBytes / (1024 * 1024):F1} MB...");

                    var modelData = response.Content.ReadAsByteArrayAsync().Result;
                    File.WriteAllBytes(modelPath, modelData);

                    Console.WriteLine($"✓ Whisper model downloaded successfully to {modelPath}");
                    Console.WriteLine($"Model size: {new FileInfo(modelPath).Length / (1024 * 1024):F1} MB");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to download model automatically: {ex.Message}");
                Console.WriteLine();
                Console.WriteLine("MANUAL DOWNLOAD INSTRUCTIONS:");
                Console.WriteLine($"1. Download the model from: {modelUrl}");
                Console.WriteLine($"2. Save it as: {Path.GetFullPath(modelPath)}");
                Console.WriteLine("3. Restart FlightPig");
                Console.WriteLine();
                throw;
            }
        }

        private void InitializeAudio()
        {
            try
            {
                _waveIn = new WaveInEvent
                {
                    WaveFormat = new WaveFormat(16000, 1), // 16kHz mono for Whisper
                    BufferMilliseconds = 100
                };
                _waveIn.DataAvailable += OnDataAvailable;
                _waveIn.RecordingStopped += OnRecordingStopped;

                Console.WriteLine("Audio input initialized successfully.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing audio: {ex.Message}");
            }
        }

        /// <summary>
        /// Start listening for voice commands
        /// </summary>
        public void StartListening()
        {
            if (!_settings.Whisper.Enabled || _whisperProcessor == null)
            {
                Console.WriteLine("Speech recognition is disabled or not available.");
                return;
            }

            lock (_lockObject)
            {
                if (_isListening) return;
                _isListening = true;
            }

            Console.WriteLine("Voice recognition started. Say a command...");
            StartRecording();
        }

        /// <summary>
        /// Stop listening for voice commands
        /// </summary>
        public void StopListening()
        {
            lock (_lockObject)
            {
                if (!_isListening) return;
                _isListening = false;
            }

            StopRecording();
            Console.WriteLine("Voice recognition stopped.");
        }

        /// <summary>
        /// Record a single voice command
        /// </summary>
        public async Task<string> RecordSingleCommandAsync()
        {
            if (!_settings.Whisper.Enabled || _whisperProcessor == null)
            {
                return null;
            }

            var tcs = new TaskCompletionSource<string>();
            
            EventHandler<string> handler = (sender, text) =>
            {
                tcs.TrySetResult(text);
            };

            try
            {
                SpeechRecognized += handler;
                StartRecording();

                // Wait for speech or timeout
                var timeoutTask = Task.Delay(_settings.Whisper.RecordingTimeoutSeconds * 1000);
                var completedTask = await Task.WhenAny(tcs.Task, timeoutTask);

                if (completedTask == timeoutTask)
                {
                    Console.WriteLine("Voice recording timed out.");
                    return null;
                }

                return await tcs.Task;
            }
            finally
            {
                SpeechRecognized -= handler;
                StopRecording();
            }
        }

        private void StartRecording()
        {
            if (_isRecording || _waveIn == null) return;

            try
            {
                _audioStream = new MemoryStream();
                _isRecording = true;
                _waveIn.StartRecording();
                RecordingStarted?.Invoke(this, EventArgs.Empty);
                Console.WriteLine("🎤 Recording started...");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error starting recording: {ex.Message}");
            }
        }

        private void StopRecording()
        {
            if (!_isRecording || _waveIn == null) return;

            try
            {
                _waveIn.StopRecording();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error stopping recording: {ex.Message}");
            }
        }

        private void OnDataAvailable(object sender, WaveInEventArgs e)
        {
            if (_audioStream != null && _isRecording)
            {
                _audioStream.Write(e.Buffer, 0, e.BytesRecorded);
            }
        }

        private async void OnRecordingStopped(object sender, StoppedEventArgs e)
        {
            _isRecording = false;
            RecordingStopped?.Invoke(this, EventArgs.Empty);
            Console.WriteLine("🎤 Recording stopped.");

            if (_audioStream != null && _audioStream.Length > 0)
            {
                await ProcessAudioAsync();
            }
        }

        private async Task ProcessAudioAsync()
        {
            if (_whisperProcessor == null || _audioStream == null) return;

            try
            {
                Console.WriteLine("Processing speech...");
                
                _audioStream.Position = 0;
                var audioData = _audioStream.ToArray();

                // Convert audio data to float array for Whisper
                var floatData = ConvertBytesToFloat(audioData);

                await foreach (var result in _whisperProcessor.ProcessAsync(floatData))
                {
                    if (!string.IsNullOrWhiteSpace(result.Text))
                    {
                        Console.WriteLine($"Recognized: \"{result.Text}\"");
                        SpeechRecognized?.Invoke(this, result.Text.Trim());
                        break; // Take the first result
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing audio: {ex.Message}");
            }
            finally
            {
                _audioStream?.Dispose();
                _audioStream = null;
            }
        }

        private float[] ConvertBytesToFloat(byte[] audioData)
        {
            var floatData = new float[audioData.Length / 2];
            for (int i = 0; i < floatData.Length; i++)
            {
                var sample = BitConverter.ToInt16(audioData, i * 2);
                floatData[i] = sample / 32768f; // Convert to float range [-1, 1]
            }
            return floatData;
        }

        public void Dispose()
        {
            StopListening();
            _waveIn?.Dispose();
            _audioStream?.Dispose();
            _whisperProcessor?.Dispose();
            _whisperFactory?.Dispose();
        }
    }
}
