﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>Microsoft.CodeCoverage</id>
    <version>16.9.1</version>
    <title>Microsoft.CodeCoverage</title>
    <authors>Microsoft</authors>
    <owners>Microsoft</owners>
    <requireLicenseAcceptance>true</requireLicenseAcceptance>
    <license type="file">LICENSE_NET.txt</license>
    <licenseUrl>https://aka.ms/deprecateLicenseUrl</licenseUrl>
    <icon>Icon.png</icon>
    <projectUrl>https://github.com/microsoft/vstest/</projectUrl>
    <iconUrl>http://go.microsoft.com/fwlink/?LinkID=288859</iconUrl>
    <description>Microsoft.CodeCoverage package brings infra for collecting code coverage from vstest.console.exe and "dotnet test".</description>
    <copyright>© Microsoft Corporation. All rights reserved.</copyright>
    <tags>vstest visual-studio unittest testplatform mstest microsoft test testing codecoverage code-coverage</tags>
    <repository type="git" url="https://github.com/microsoft/vstest" branch="refs/heads/rel/16.9" commit="6a858a323caa439115e9e3cf1c958b04f4ff9993" />
    <dependencies>
      <group targetFramework=".NETFramework4.5" />
      <group targetFramework=".NETCoreApp1.0" />
    </dependencies>
  </metadata>
</package>