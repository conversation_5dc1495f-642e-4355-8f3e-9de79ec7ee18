using System;
using System.Linq;
using FlightPig.Models;

namespace FlightPig.Services
{
    /// <summary>
    /// Monitors mission progress based on aircraft data
    /// </summary>
    public class MissionProgressMonitor
    {
        private Mission _currentMission;
        private readonly double _proximityThresholdNm = 0.5; // Nautical miles
        private readonly double _altitudeThresholdFt = 500; // Feet

        public event EventHandler<TourPoiReachedEventArgs> TourPoiReached;
        public event EventHandler<LandingChallengeCompletedEventArgs> LandingChallengeCompleted;

        public Mission CurrentMission
        {
            get => _currentMission;
            set => _currentMission = value;
        }

        /// <summary>
        /// Update mission progress based on current aircraft information
        /// </summary>
        public void UpdateProgress(AircraftInfo aircraftInfo)
        {
            if (_currentMission == null || _currentMission.Objectives.Count == 0)
                return;

            foreach (var objective in _currentMission.Objectives.Where(o => !o.Completed))
            {
                UpdateObjectiveProgress(objective, aircraftInfo);
            }

            DisplayProgress();
        }

        private void UpdateObjectiveProgress(Objective objective, AircraftInfo aircraftInfo)
        {
            switch (objective.Type)
            {
                case ObjectiveType.FlyOver:
                    UpdateFlyOverProgress(objective, aircraftInfo);
                    break;
                case ObjectiveType.LandAt:
                    UpdateLandAtProgress(objective, aircraftInfo);
                    break;
                case ObjectiveType.LandAtAndWait:
                    UpdateLandAtAndWaitProgress(objective, aircraftInfo);
                    break;
                case ObjectiveType.NavigateTo:
                    UpdateNavigateToProgress(objective, aircraftInfo);
                    break;
                case ObjectiveType.MaintainAltitude:
                    UpdateMaintainAltitudeProgress(objective, aircraftInfo);
                    break;
                case ObjectiveType.MaintainSpeed:
                    UpdateMaintainSpeedProgress(objective, aircraftInfo);
                    break;
                case ObjectiveType.TourPointOfInterest:
                    UpdateTourPoiProgress(objective, aircraftInfo);
                    break;
                case ObjectiveType.LandingChallenge:
                    UpdateLandingChallengeProgress(objective, aircraftInfo);
                    break;
                default:
                    // For other objective types, implement as needed
                    break;
            }
        }

        private void UpdateFlyOverProgress(Objective objective, AircraftInfo aircraftInfo)
        {
            if (objective.Latitude.HasValue && objective.Longitude.HasValue)
            {
                var distance = CalculateDistanceNm(
                    aircraftInfo.Latitude, aircraftInfo.Longitude,
                    objective.Latitude.Value, objective.Longitude.Value);

                if (distance <= _proximityThresholdNm)
                {
                    objective.Progress = 1.0;
                    objective.Completed = true;
                    Console.WriteLine($"✓ Objective completed: {objective.Title}");
                }
                else
                {
                    // Progress based on proximity (closer = higher progress)
                    var maxDistance = 10.0; // Max distance for progress calculation
                    objective.Progress = Math.Max(0, 1.0 - (distance / maxDistance));
                }
            }
        }

        private void UpdateLandAtProgress(Objective objective, AircraftInfo aircraftInfo)
        {
            if (objective.Latitude.HasValue && objective.Longitude.HasValue)
            {
                var distance = CalculateDistanceNm(
                    aircraftInfo.Latitude, aircraftInfo.Longitude,
                    objective.Latitude.Value, objective.Longitude.Value);

                if (distance <= _proximityThresholdNm && aircraftInfo.OnGround)
                {
                    objective.Progress = 1.0;
                    objective.Completed = true;
                    Console.WriteLine($"✓ Objective completed: {objective.Title}");
                }
                else if (distance <= _proximityThresholdNm)
                {
                    objective.Progress = 0.8; // Close but not landed
                }
                else
                {
                    var maxDistance = 10.0;
                    objective.Progress = Math.Max(0, 0.7 * (1.0 - (distance / maxDistance)));
                }
            }
        }

        private void UpdateLandAtAndWaitProgress(Objective objective, AircraftInfo aircraftInfo)
        {
            // Similar to LandAt but requires staying on ground for a period
            UpdateLandAtProgress(objective, aircraftInfo);
            
            if (objective.Completed && aircraftInfo.OnGround)
            {
                // Could add timer logic here for waiting period
            }
        }

        private void UpdateNavigateToProgress(Objective objective, AircraftInfo aircraftInfo)
        {
            // Similar to FlyOver
            UpdateFlyOverProgress(objective, aircraftInfo);
        }

        private void UpdateMaintainAltitudeProgress(Objective objective, AircraftInfo aircraftInfo)
        {
            if (objective.Altitude.HasValue)
            {
                var altitudeDifference = Math.Abs(aircraftInfo.Altitude - objective.Altitude.Value);
                
                if (altitudeDifference <= _altitudeThresholdFt)
                {
                    objective.Progress = Math.Min(1.0, objective.Progress + 0.1);
                    
                    if (objective.Progress >= 1.0)
                    {
                        objective.Completed = true;
                        Console.WriteLine($"✓ Objective completed: {objective.Title}");
                    }
                }
                else
                {
                    objective.Progress = Math.Max(0, objective.Progress - 0.05);
                }
            }
        }

        private void UpdateMaintainSpeedProgress(Objective objective, AircraftInfo aircraftInfo)
        {
            // Extract target speed from parameters
            if (objective.Parameters != null && objective.Parameters.TryGetValue("targetSpeed", out var speedObj))
            {
                if (double.TryParse(speedObj.ToString(), out var targetSpeed))
                {
                    var speedDifference = Math.Abs(aircraftInfo.AirspeedKnots - targetSpeed);
                    
                    if (speedDifference <= 10) // Within 10 knots
                    {
                        objective.Progress = Math.Min(1.0, objective.Progress + 0.1);
                        
                        if (objective.Progress >= 1.0)
                        {
                            objective.Completed = true;
                            Console.WriteLine($"✓ Objective completed: {objective.Title}");
                        }
                    }
                    else
                    {
                        objective.Progress = Math.Max(0, objective.Progress - 0.05);
                    }
                }
            }
        }

        private void DisplayProgress()
        {
            if (_currentMission == null) return;

            Console.Clear();
            Console.WriteLine("=== FLIGHT PIG - MISSION PROGRESS ===");
            Console.WriteLine($"Mission: {_currentMission.Title}");
            Console.WriteLine($"Overall Progress: {_currentMission.OverallProgress:P1}");
            Console.WriteLine();

            for (int i = 0; i < _currentMission.Objectives.Count; i++)
            {
                var obj = _currentMission.Objectives[i];
                var status = obj.Completed ? "✓ COMPLETE" : $"{obj.Progress:P1}";
                var progressBar = CreateProgressBar(obj.Progress);
                
                Console.WriteLine($"{i + 1}. {obj.Title}");
                Console.WriteLine($"   {obj.Description}");
                Console.WriteLine($"   Status: {status} {progressBar}");
                Console.WriteLine();
            }

            Console.WriteLine("Press ESC to return to menu, or any other key to continue monitoring...");
        }

        private string CreateProgressBar(double progress, int width = 20)
        {
            var filled = (int)(progress * width);
            var bar = new string('█', filled) + new string('░', width - filled);
            return $"[{bar}]";
        }

        /// <summary>
        /// Calculate distance between two points in nautical miles
        /// </summary>
        private double CalculateDistanceNm(double lat1, double lon1, double lat2, double lon2)
        {
            const double R = 3440.065; // Earth's radius in nautical miles
            
            var dLat = ToRadians(lat2 - lat1);
            var dLon = ToRadians(lon2 - lon1);
            
            var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                    Math.Cos(ToRadians(lat1)) * Math.Cos(ToRadians(lat2)) *
                    Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
            
            var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));
            
            return R * c;
        }

        private double ToRadians(double degrees)
        {
            return degrees * Math.PI / 180;
        }

        private void UpdateTourPoiProgress(Objective objective, AircraftInfo aircraftInfo)
        {
            if (objective.Latitude.HasValue && objective.Longitude.HasValue)
            {
                var distance = CalculateDistanceNm(
                    aircraftInfo.Latitude, aircraftInfo.Longitude,
                    objective.Latitude.Value, objective.Longitude.Value);

                // Get proximity threshold from parameters or use default
                var proximityThreshold = _proximityThresholdNm;
                if (objective.Parameters?.TryGetValue("proximityThresholdNm", out var thresholdObj) == true)
                {
                    if (double.TryParse(thresholdObj.ToString(), out var threshold))
                    {
                        proximityThreshold = threshold;
                    }
                }

                // Check if landing is required
                var isLandingRequired = false;
                if (objective.Parameters?.TryGetValue("isLandingRequired", out var landingObj) == true)
                {
                    bool.TryParse(landingObj.ToString(), out isLandingRequired);
                }

                bool reached = distance <= proximityThreshold;

                // Check landing requirement if specified
                if (reached && isLandingRequired)
                {
                    reached = aircraftInfo.OnGround;
                }

                if (reached)
                {
                    objective.Progress = 1.0;
                    objective.Completed = true;
                    Console.WriteLine($"✓ POI reached: {objective.Title}");

                    // Fire tour POI reached event
                    var tourGuideText = objective.Parameters?.TryGetValue("tourGuideText", out var textObj) == true
                        ? textObj.ToString()
                        : "";
                    var nextPoiInstructions = objective.Parameters?.TryGetValue("nextPoiInstructions", out var instructionsObj) == true
                        ? instructionsObj.ToString()
                        : "";

                    TourPoiReached?.Invoke(this, new TourPoiReachedEventArgs(objective, tourGuideText, nextPoiInstructions));
                }
                else
                {
                    // Progress based on proximity (closer = higher progress)
                    var maxDistance = 10.0; // Max distance for progress calculation
                    objective.Progress = Math.Max(0, 1.0 - (distance / maxDistance));
                }
            }
        }

        private void UpdateLandingChallengeProgress(Objective objective, AircraftInfo aircraftInfo)
        {
            if (objective.Latitude.HasValue && objective.Longitude.HasValue)
            {
                var distance = CalculateDistanceNm(
                    aircraftInfo.Latitude, aircraftInfo.Longitude,
                    objective.Latitude.Value, objective.Longitude.Value);

                // Get proximity threshold from parameters or use default
                var proximityThreshold = 0.1; // Very precise for landing challenges
                if (objective.Parameters?.TryGetValue("proximityThresholdNm", out var thresholdObj) == true)
                {
                    if (double.TryParse(thresholdObj.ToString(), out var threshold))
                    {
                        proximityThreshold = threshold;
                    }
                }

                bool nearRunway = distance <= proximityThreshold;
                bool landed = aircraftInfo.OnGround && nearRunway;

                if (landed && !objective.Completed)
                {
                    objective.Progress = 1.0;
                    objective.Completed = true;
                    Console.WriteLine($"✓ Landing challenge completed: {objective.Title}");

                    // Fire landing challenge completed event
                    LandingChallengeCompleted?.Invoke(this, new LandingChallengeCompletedEventArgs(objective, aircraftInfo));
                }
                else if (nearRunway)
                {
                    // Close to runway but not landed yet
                    objective.Progress = 0.8;
                }
                else
                {
                    // Progress based on proximity to runway
                    var maxDistance = 5.0; // Max distance for progress calculation
                    objective.Progress = Math.Max(0, 1.0 - (distance / maxDistance));
                }
            }
        }
    }

    /// <summary>
    /// Event args for tour POI reached event
    /// </summary>
    public class TourPoiReachedEventArgs : EventArgs
    {
        public Objective Objective { get; }
        public string TourGuideText { get; }
        public string NextPoiInstructions { get; }

        public TourPoiReachedEventArgs(Objective objective, string tourGuideText, string nextPoiInstructions)
        {
            Objective = objective;
            TourGuideText = tourGuideText;
            NextPoiInstructions = nextPoiInstructions;
        }
    }

    /// <summary>
    /// Event args for landing challenge completed event
    /// </summary>
    public class LandingChallengeCompletedEventArgs : EventArgs
    {
        public Objective Objective { get; }
        public AircraftInfo AircraftInfo { get; }

        public LandingChallengeCompletedEventArgs(Objective objective, AircraftInfo aircraftInfo)
        {
            Objective = objective;
            AircraftInfo = aircraftInfo;
        }
    }
}
