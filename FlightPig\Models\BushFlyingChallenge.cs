using System;
using System.Collections.Generic;

namespace FlightPig.Models
{
    /// <summary>
    /// Represents a multi-leg bush flying adventure with remote airstrips and survival scenarios
    /// </summary>
    public class BushFlyingChallenge
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Title { get; set; }
        public string Description { get; set; }
        public string Scenario { get; set; }
        public BushFlyingDifficulty Difficulty { get; set; }
        public string Region { get; set; }
        public string StartingAirport { get; set; }
        public List<BushFlyingWaypoint> Waypoints { get; set; } = new List<BushFlyingWaypoint>();
        public List<string> RequiredSkills { get; set; } = new List<string>();
        public List<string> Challenges { get; set; } = new List<string>();
        public List<string> SurvivalElements { get; set; } = new List<string>();
        public double EstimatedDurationMinutes { get; set; }
        public double TotalDistanceNM { get; set; }
        public string WeatherConditions { get; set; }
        public string TerrainType { get; set; }
        public List<string> SuitableAircraft { get; set; } = new List<string>();
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public BushFlyingStatus Status { get; set; } = BushFlyingStatus.NotStarted;
        public int CurrentWaypointIndex { get; set; } = 0;
        public List<BushFlyingLegResult> CompletedLegs { get; set; } = new List<BushFlyingLegResult>();
        public string BackgroundStory { get; set; }
        public List<string> SafetyConsiderations { get; set; } = new List<string>();
    }

    /// <summary>
    /// Represents a waypoint in a bush flying route
    /// </summary>
    public class BushFlyingWaypoint
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public int SequenceNumber { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public double ElevationFeet { get; set; }
        public BushFlyingWaypointType Type { get; set; }
        public Airport Airport { get; set; }
        public string Purpose { get; set; }
        public List<string> LocalChallenges { get; set; } = new List<string>();
        public List<string> NavigationNotes { get; set; } = new List<string>();
        public string TerrainDescription { get; set; }
        public string ApproachNotes { get; set; }
        public string LandingNotes { get; set; }
        public bool IsOptional { get; set; } = false;
        public double DistanceFromPreviousNM { get; set; }
        public double HeadingFromPrevious { get; set; }
        public string FuelConsiderations { get; set; }
        public List<string> LocalHazards { get; set; } = new List<string>();
        public string WeatherConsiderations { get; set; }
        public bool RequiresSpecialTechnique { get; set; } = false;
        public string SpecialTechniqueDescription { get; set; }
    }

    /// <summary>
    /// Results from completing a bush flying leg
    /// </summary>
    public class BushFlyingLegResult
    {
        public string WaypointId { get; set; }
        public DateTime CompletedAt { get; set; }
        public double FlightTimeMinutes { get; set; }
        public double FuelUsedGallons { get; set; }
        public BushFlyingLegScore Score { get; set; }
        public List<string> ChallengesEncountered { get; set; } = new List<string>();
        public List<string> SkillsDemonstrated { get; set; } = new List<string>();
        public string Notes { get; set; }
        public bool LandingCompleted { get; set; }
        public LandingScore LandingScore { get; set; }
    }

    /// <summary>
    /// Scoring for bush flying leg performance
    /// </summary>
    public class BushFlyingLegScore
    {
        public double NavigationAccuracy { get; set; } // 0-100
        public double TerrainAwareness { get; set; } // 0-100
        public double FuelManagement { get; set; } // 0-100
        public double WeatherAdaptation { get; set; } // 0-100
        public double LandingTechnique { get; set; } // 0-100
        public double SafetyConsciousness { get; set; } // 0-100
        public double OverallScore { get; set; } // 0-100
        public string Grade { get; set; } // A+, A, B+, B, C+, C, D, F
        public List<string> Strengths { get; set; } = new List<string>();
        public List<string> AreasForImprovement { get; set; } = new List<string>();
    }

    /// <summary>
    /// Bush flying challenge difficulty levels
    /// </summary>
    public enum BushFlyingDifficulty
    {
        Beginner,      // Easy terrain, good weather, well-maintained strips
        Intermediate,  // Moderate terrain, variable weather, some challenging strips
        Advanced,      // Difficult terrain, challenging weather, short/rough strips
        Expert,        // Extreme terrain, harsh conditions, survival scenarios
        Legendary      // The most challenging bush flying scenarios possible
    }

    /// <summary>
    /// Types of waypoints in bush flying routes
    /// </summary>
    public enum BushFlyingWaypointType
    {
        StartingPoint,     // Where the adventure begins
        FuelStop,          // Refueling location
        SupplyDrop,        // Delivering supplies to remote location
        Rescue,            // Emergency rescue scenario
        Exploration,       // Exploring new territory
        Checkpoint,        // Navigation checkpoint
        EmergencyLanding,  // Forced landing scenario
        Destination,       // Final destination
        Scenic,            // Scenic flyover point
        WeatherCheck,      // Weather observation point
        SurvivalCache,     // Emergency supply cache
        WildlifeObservation // Wildlife viewing area
    }

    /// <summary>
    /// Status of bush flying challenge
    /// </summary>
    public enum BushFlyingStatus
    {
        NotStarted,
        InProgress,
        Paused,
        Completed,
        Failed,
        Abandoned
    }

    /// <summary>
    /// Bush flying mission types for variety
    /// </summary>
    public enum BushFlyingMissionType
    {
        SupplyRun,         // Delivering supplies to remote communities
        MedicalEvacuation, // Emergency medical transport
        WildlifeResearch,  // Supporting wildlife research
        SearchAndRescue,   // Finding lost persons/aircraft
        FireSpotting,      // Forest fire detection and reporting
        Exploration,       // Mapping new territory
        Hunting,           // Supporting hunting expeditions
        Fishing,           // Access to remote fishing locations
        Mining,            // Supporting remote mining operations
        Tourism,           // Scenic bush flying tours
        Training,          // Bush flying skill development
        Survival,          // Survival scenario training
        Photography,       // Aerial photography missions
        Conservation,      // Wildlife conservation support
        Emergency          // General emergency response
    }

    /// <summary>
    /// Terrain types for bush flying challenges
    /// </summary>
    public enum BushFlyingTerrain
    {
        Mountains,         // High altitude, challenging approaches
        Forest,            // Dense forest with small clearings
        Tundra,            // Arctic/subarctic conditions
        Desert,            // Hot, dry conditions with limited landmarks
        Swamp,             // Wetlands with challenging conditions
        Coastal,           // Coastal areas with water crossings
        Prairie,           // Open grasslands
        Canyon,            // Deep canyons and mesas
        Glacier,           // Glacial terrain and ice fields
        Jungle,            // Dense tropical vegetation
        Badlands,          // Rough, eroded terrain
        Island,            // Island hopping scenarios
        River,             // Following river systems
        Lake,              // Lake country with seaplane operations
        Volcanic           // Volcanic terrain with unique challenges
    }

    /// <summary>
    /// Weather conditions for bush flying
    /// </summary>
    public enum BushFlyingWeather
    {
        Clear,             // Perfect flying conditions
        PartlyCloudy,      // Some clouds, good visibility
        Overcast,          // Solid cloud layer
        LightRain,         // Light precipitation
        HeavyRain,         // Challenging precipitation
        Fog,               // Low visibility conditions
        Snow,              // Winter conditions
        Thunderstorms,     // Severe weather
        HighWinds,         // Strong wind conditions
        Turbulence,        // Rough air conditions
        Icing,             // Icing conditions
        Sandstorm,         // Desert dust conditions
        Blizzard,          // Severe winter weather
        Hail,              // Hail conditions
        Variable           // Changing conditions
    }
}
