﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;

namespace AirportDataGenerator
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== FlightPig Airport Data Generator ===");
            Console.WriteLine("Downloading OpenFlights airport database...");
            Console.WriteLine();

            try
            {
                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromMinutes(5);
                httpClient.DefaultRequestHeaders.Add("User-Agent",
                    "FlightPig/1.0 (https://github.com/MariusMyburg/FlightPig)");

                var airports = await FetchOpenFlightsDataAsync(httpClient);

                Console.WriteLine($"Successfully processed {airports.Count} airports");

                var jsonOptions = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };

                var json = JsonSerializer.Serialize(airports, jsonOptions);
                var outputPath = "../FlightPig/airports.json";

                await File.WriteAllTextAsync(outputPath, json);

                Console.WriteLine($"✅ Generated {outputPath}");
                Console.WriteLine($"📁 File size: {new FileInfo(outputPath).Length / 1024:N0} KB");

                DisplayStatistics(airports);

                Console.WriteLine("🎉 Airport data generation completed!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
                Environment.Exit(1);
            }

            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        static async Task<List<Airport>> FetchOpenFlightsDataAsync(HttpClient httpClient)
        {
            const string OPENFLIGHTS_URL = "https://raw.githubusercontent.com/jpatokal/openflights/master/data/airports.dat";

            var data = await httpClient.GetStringAsync(OPENFLIGHTS_URL);
            var airports = new List<Airport>();
            var lines = data.Split('\n', StringSplitOptions.RemoveEmptyEntries);

            foreach (var line in lines)
            {
                try
                {
                    var fields = ParseCsvLine(line);
                    if (fields.Length < 14) continue;

                    if (fields[12] != "airport") continue; // Only airports

                    var airport = new Airport
                    {
                        IcaoCode = CleanField(fields[5]),
                        IataCode = CleanField(fields[4]),
                        Name = CleanField(fields[1]),
                        Latitude = ParseDouble(fields[6]),
                        Longitude = ParseDouble(fields[7]),
                        ElevationFeet = ParseDouble(fields[8]),
                        Country = CleanField(fields[3]),
                        City = CleanField(fields[2]),
                        Type = DetermineAirportType(fields[1]),
                        IsControlled = DetermineIfControlled(fields[1]),
                        Difficulty = DetermineDifficulty(fields[1]),
                        SuitableAircraft = DetermineSuitableAircraft(fields[1]),
                        Description = $"{CleanField(fields[1])} in {CleanField(fields[2])}, {CleanField(fields[3])}"
                    };

                    // Only include airports with valid ICAO codes and coordinates
                    if (!string.IsNullOrEmpty(airport.IcaoCode) &&
                        airport.IcaoCode != "\\N" &&
                        Math.Abs(airport.Latitude) > 0.001 &&
                        Math.Abs(airport.Longitude) > 0.001)
                    {
                        airport.Runways.Add(CreateDefaultRunway(airport));
                        airports.Add(airport);
                    }
                }
                catch
                {
                    // Skip malformed lines
                }
            }

            return airports.OrderBy(a => a.Country).ThenBy(a => a.Name).ToList();
        }

        static string[] ParseCsvLine(string line)
        {
            var fields = new List<string>();
            var current = "";
            var inQuotes = false;

            for (int i = 0; i < line.Length; i++)
            {
                var c = line[i];
                if (c == '"') inQuotes = !inQuotes;
                else if (c == ',' && !inQuotes)
                {
                    fields.Add(current);
                    current = "";
                }
                else current += c;
            }
            fields.Add(current);
            return fields.ToArray();
        }

        static string CleanField(string field)
        {
            if (string.IsNullOrEmpty(field) || field == "\\N") return "";
            return field.Trim('"').Trim();
        }

        static double ParseDouble(string value)
        {
            if (string.IsNullOrEmpty(value) || value == "\\N") return 0;
            return double.TryParse(value, NumberStyles.Float, CultureInfo.InvariantCulture, out double result) ? result : 0;
        }

        static AirportType DetermineAirportType(string name)
        {
            var nameLower = name.ToLower();
            if (nameLower.Contains("international")) return AirportType.International;
            if (nameLower.Contains("regional")) return AirportType.Regional;
            if (nameLower.Contains("municipal")) return AirportType.Municipal;
            if (nameLower.Contains("private")) return AirportType.Private;
            if (nameLower.Contains("military")) return AirportType.Military;
            if (nameLower.Contains("heliport")) return AirportType.Heliport;
            if (nameLower.Contains("seaplane")) return AirportType.Seaplane;
            return AirportType.Regional;
        }

        static bool DetermineIfControlled(string name)
        {
            var nameLower = name.ToLower();
            return nameLower.Contains("international") || nameLower.Contains("regional") || nameLower.Contains("municipal");
        }

        static LandingDifficulty DetermineDifficulty(string name)
        {
            var nameLower = name.ToLower();
            if (nameLower.Contains("mountain") || nameLower.Contains("short")) return LandingDifficulty.Expert;
            if (nameLower.Contains("international")) return LandingDifficulty.Medium;
            return LandingDifficulty.Easy;
        }

        static List<string> DetermineSuitableAircraft(string name)
        {
            var nameLower = name.ToLower();
            if (nameLower.Contains("heliport")) return new List<string> { "Helicopter" };
            if (nameLower.Contains("seaplane")) return new List<string> { "Seaplane" };
            if (nameLower.Contains("international")) return new List<string> { "Jet", "Turboprop", "General Aviation" };
            return new List<string> { "General Aviation", "Helicopter" };
        }

        static Runway CreateDefaultRunway(Airport airport)
        {
            return new Runway
            {
                Designation = "01",
                Heading = 10,
                LengthFeet = airport.Type == AirportType.International ? 10000 : 5000,
                WidthFeet = airport.Type == AirportType.International ? 150 : 100,
                Surface = "Asphalt",
                HasLights = airport.IsControlled,
                HasILS = airport.Type == AirportType.International
            };
        }

        static void DisplayStatistics(List<Airport> airports)
        {
            Console.WriteLine();
            Console.WriteLine("=== STATISTICS ===");

            var typeStats = airports.GroupBy(a => a.Type).ToDictionary(g => g.Key.ToString(), g => g.Count());
            var countryStats = airports.GroupBy(a => a.Country).ToDictionary(g => g.Key, g => g.Count());

            Console.WriteLine("By Type:");
            foreach (var kvp in typeStats.OrderByDescending(x => x.Value))
                Console.WriteLine($"  {kvp.Key}: {kvp.Value:N0}");

            Console.WriteLine();
            Console.WriteLine("Top 10 Countries:");
            foreach (var kvp in countryStats.OrderByDescending(x => x.Value).Take(10))
                Console.WriteLine($"  {kvp.Key}: {kvp.Value:N0}");

            Console.WriteLine();
            Console.WriteLine("Sample Airports:");
            foreach (var airport in airports.Where(a => !string.IsNullOrEmpty(a.IataCode)).Take(10))
                Console.WriteLine($"  {airport.IcaoCode} ({airport.IataCode}) - {airport.Name}, {airport.Country}");

            Console.WriteLine();
        }
    }
}
