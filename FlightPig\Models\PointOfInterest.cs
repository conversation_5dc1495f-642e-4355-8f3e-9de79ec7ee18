using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace FlightPig.Models
{
    /// <summary>
    /// Represents a point of interest for guided tours
    /// </summary>
    public class PointOfInterest
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; }

        [JsonPropertyName("latitude")]
        public double Latitude { get; set; }

        [JsonPropertyName("longitude")]
        public double Longitude { get; set; }

        [JsonPropertyName("altitude")]
        public double? Altitude { get; set; }

        [JsonPropertyName("category")]
        public string Category { get; set; }

        [JsonPropertyName("tourGuideText")]
        public string TourGuideText { get; set; }

        [JsonPropertyName("historicalInfo")]
        public string HistoricalInfo { get; set; }

        [JsonPropertyName("flyoverInstructions")]
        public string FlyoverInstructions { get; set; }

        [JsonPropertyName("nextPoiInstructions")]
        public string NextPoiInstructions { get; set; }

        [JsonPropertyName("tags")]
        public List<string> Tags { get; set; } = new List<string>();

        [JsonPropertyName("estimatedVisitDurationMinutes")]
        public int EstimatedVisitDurationMinutes { get; set; } = 2;

        [JsonPropertyName("minimumAltitude")]
        public double? MinimumAltitude { get; set; }

        [JsonPropertyName("maximumAltitude")]
        public double? MaximumAltitude { get; set; }

        [JsonPropertyName("proximityThresholdNm")]
        public double ProximityThresholdNm { get; set; } = 0.5;

        [JsonPropertyName("isLandingRequired")]
        public bool IsLandingRequired { get; set; } = false;

        [JsonPropertyName("airportCode")]
        public string AirportCode { get; set; }

        [JsonPropertyName("runwayInfo")]
        public string RunwayInfo { get; set; }
    }
}
