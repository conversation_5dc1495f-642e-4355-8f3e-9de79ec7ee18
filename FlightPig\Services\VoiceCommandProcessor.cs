using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using FlightPig.Models;

namespace FlightPig.Services
{
    /// <summary>
    /// Voice command recognition and processing
    /// </summary>
    public class VoiceCommandProcessor
    {
        private readonly Dictionary<string, VoiceCommand> _commands;
        private readonly TextToSpeechService _ttsService;

        public event EventHandler<VoiceCommandEventArgs> CommandRecognized;

        public VoiceCommandProcessor(TextToSpeechService ttsService)
        {
            _ttsService = ttsService;
            _commands = InitializeCommands();
        }

        private Dictionary<string, VoiceCommand> InitializeCommands()
        {
            return new Dictionary<string, VoiceCommand>
            {
                // Main menu commands
                ["start_mission"] = new VoiceCommand
                {
                    Id = "start_mission",
                    Patterns = new[] { "start mission", "new mission", "start story", "begin mission", "create mission" },
                    Action = VoiceCommandAction.StartMission,
                    Description = "Start a new story mission"
                },
                ["start_tour"] = new VoiceCommand
                {
                    Id = "start_tour",
                    Patterns = new[] { "start tour", "guided tour", "begin tour", "new tour", "tour guide" },
                    Action = VoiceCommandAction.StartTour,
                    Description = "Start a new guided tour"
                },
                ["start_landing_challenge"] = new VoiceCommand
                {
                    Id = "start_landing_challenge",
                    Patterns = new[] { "landing challenge", "start landing", "practice landing", "landing practice", "challenge landing" },
                    Action = VoiceCommandAction.StartLandingChallenge,
                    Description = "Start a landing challenge"
                },
                ["start_bush_flying"] = new VoiceCommand
                {
                    Id = "start_bush_flying",
                    Patterns = new[] { "bush flying", "start bush flying", "bush challenge", "bush adventure", "wilderness flying", "backcountry flying" },
                    Action = VoiceCommandAction.StartBushFlying,
                    Description = "Start a bush flying challenge"
                },
                ["show_status"] = new VoiceCommand
                {
                    Id = "show_status",
                    Patterns = new[] { "show status", "aircraft status", "current status", "flight status", "aircraft info" },
                    Action = VoiceCommandAction.ShowStatus,
                    Description = "Show current aircraft information"
                },
                ["monitor_progress"] = new VoiceCommand
                {
                    Id = "monitor_progress",
                    Patterns = new[] { "monitor progress", "mission progress", "show progress", "track mission" },
                    Action = VoiceCommandAction.MonitorProgress,
                    Description = "Monitor mission progress"
                },
                ["exit"] = new VoiceCommand
                {
                    Id = "exit",
                    Patterns = new[] { "exit", "quit", "close", "stop", "end program" },
                    Action = VoiceCommandAction.Exit,
                    Description = "Exit the application"
                },
                
                // Navigation commands
                ["main_menu"] = new VoiceCommand
                {
                    Id = "main_menu",
                    Patterns = new[] { "main menu", "go back", "return", "back to menu", "home" },
                    Action = VoiceCommandAction.MainMenu,
                    Description = "Return to main menu"
                },
                
                // Mission control commands
                ["accept_mission"] = new VoiceCommand
                {
                    Id = "accept_mission",
                    Patterns = new[] { "accept", "start this mission", "yes", "confirm", "begin" },
                    Action = VoiceCommandAction.AcceptMission,
                    Description = "Accept and start the current mission"
                },
                ["decline_mission"] = new VoiceCommand
                {
                    Id = "decline_mission",
                    Patterns = new[] { "decline", "no", "cancel", "reject", "skip" },
                    Action = VoiceCommandAction.DeclineMission,
                    Description = "Decline the current mission"
                },

                // Tour control commands
                ["accept_tour"] = new VoiceCommand
                {
                    Id = "accept_tour",
                    Patterns = new[] { "accept tour", "start this tour", "yes tour", "confirm tour", "begin tour" },
                    Action = VoiceCommandAction.AcceptTour,
                    Description = "Accept and start the current tour"
                },
                ["decline_tour"] = new VoiceCommand
                {
                    Id = "decline_tour",
                    Patterns = new[] { "decline tour", "no tour", "cancel tour", "reject tour", "skip tour" },
                    Action = VoiceCommandAction.DeclineTour,
                    Description = "Decline the current tour"
                },
                ["repeat_information"] = new VoiceCommand
                {
                    Id = "repeat_information",
                    Patterns = new[] { "repeat", "say again", "repeat information", "what did you say", "repeat that" },
                    Action = VoiceCommandAction.RepeatInformation,
                    Description = "Repeat the last tour information"
                },
                ["next_poi"] = new VoiceCommand
                {
                    Id = "next_poi",
                    Patterns = new[] { "next point", "next poi", "next destination", "skip to next", "continue tour" },
                    Action = VoiceCommandAction.NextPoi,
                    Description = "Skip to the next point of interest"
                },

                // Landing challenge control commands
                ["accept_landing_challenge"] = new VoiceCommand
                {
                    Id = "accept_landing_challenge",
                    Patterns = new[] { "accept landing", "start landing challenge", "yes landing", "confirm landing", "begin landing challenge" },
                    Action = VoiceCommandAction.AcceptLandingChallenge,
                    Description = "Accept and start the current landing challenge"
                },
                ["decline_landing_challenge"] = new VoiceCommand
                {
                    Id = "decline_landing_challenge",
                    Patterns = new[] { "decline landing", "no landing", "cancel landing", "reject landing", "skip landing challenge" },
                    Action = VoiceCommandAction.DeclineLandingChallenge,
                    Description = "Decline the current landing challenge"
                },
                
                // System commands
                ["help"] = new VoiceCommand
                {
                    Id = "help",
                    Patterns = new[] { "help", "commands", "what can I say", "voice commands" },
                    Action = VoiceCommandAction.Help,
                    Description = "Show available voice commands"
                },
                ["settings"] = new VoiceCommand
                {
                    Id = "settings",
                    Patterns = new[] { "settings", "configuration", "options", "preferences" },
                    Action = VoiceCommandAction.Settings,
                    Description = "Show current settings"
                },
                ["test_voice"] = new VoiceCommand
                {
                    Id = "test_voice",
                    Patterns = new[] { "test voice", "test speech", "test audio", "voice test" },
                    Action = VoiceCommandAction.TestVoice,
                    Description = "Test text-to-speech functionality"
                }
            };
        }

        /// <summary>
        /// Process recognized speech text and identify commands
        /// </summary>
        public async Task<VoiceCommand> ProcessSpeechAsync(string speechText)
        {
            if (string.IsNullOrWhiteSpace(speechText))
                return null;

            var normalizedText = NormalizeText(speechText);
            Console.WriteLine($"Processing speech: \"{normalizedText}\"");

            // Find matching command
            var matchedCommand = FindBestMatch(normalizedText);
            
            if (matchedCommand != null)
            {
                Console.WriteLine($"Command recognized: {matchedCommand.Description}");
                
                // Provide audio feedback
                if (_ttsService != null)
                {
                    await _ttsService.SpeakConfirmationAsync($"Command recognized: {matchedCommand.Description}");
                }

                // Raise event
                CommandRecognized?.Invoke(this, new VoiceCommandEventArgs(matchedCommand, speechText));
                
                return matchedCommand;
            }
            else
            {
                Console.WriteLine("No matching command found.");
                
                if (_ttsService != null)
                {
                    await _ttsService.SpeakErrorAsync("Command not recognized. Say 'help' for available commands.");
                }
                
                return null;
            }
        }

        private string NormalizeText(string text)
        {
            // Convert to lowercase and remove extra whitespace
            text = text.ToLowerInvariant().Trim();
            
            // Remove common filler words
            var fillerWords = new[] { "please", "can you", "could you", "would you", "um", "uh", "the" };
            foreach (var filler in fillerWords)
            {
                text = Regex.Replace(text, $@"\b{filler}\b", "", RegexOptions.IgnoreCase);
            }
            
            // Normalize whitespace
            text = Regex.Replace(text, @"\s+", " ").Trim();
            
            return text;
        }

        private VoiceCommand FindBestMatch(string normalizedText)
        {
            var bestMatch = (Command: (VoiceCommand)null, Score: 0.0);

            foreach (var command in _commands.Values)
            {
                foreach (var pattern in command.Patterns)
                {
                    var score = CalculateMatchScore(normalizedText, pattern);
                    if (score > bestMatch.Score && score > 0.6) // Minimum confidence threshold
                    {
                        bestMatch = (command, score);
                    }
                }
            }

            return bestMatch.Command;
        }

        private double CalculateMatchScore(string text, string pattern)
        {
            // Exact match
            if (text == pattern)
                return 1.0;

            // Contains pattern
            if (text.Contains(pattern))
                return 0.9;

            // Word-based matching
            var textWords = text.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            var patternWords = pattern.Split(' ', StringSplitOptions.RemoveEmptyEntries);

            if (patternWords.Length == 0)
                return 0.0;

            var matchedWords = patternWords.Count(pw => textWords.Any(tw => tw.Contains(pw) || pw.Contains(tw)));
            var score = (double)matchedWords / patternWords.Length;

            // Bonus for word order preservation
            if (score > 0.5 && IsWordOrderPreserved(textWords, patternWords))
            {
                score += 0.1;
            }

            return Math.Min(score, 1.0);
        }

        private bool IsWordOrderPreserved(string[] textWords, string[] patternWords)
        {
            var lastIndex = -1;
            foreach (var patternWord in patternWords)
            {
                var index = Array.FindIndex(textWords, lastIndex + 1, tw => tw.Contains(patternWord) || patternWord.Contains(tw));
                if (index <= lastIndex)
                    return false;
                lastIndex = index;
            }
            return true;
        }

        /// <summary>
        /// Get all available commands for help display
        /// </summary>
        public List<VoiceCommand> GetAllCommands()
        {
            return _commands.Values.ToList();
        }

        /// <summary>
        /// Speak all available commands
        /// </summary>
        public async Task SpeakHelpAsync()
        {
            var helpText = "Available voice commands: ";
            var commands = GetAllCommands().Take(5); // Limit to avoid too long speech
            
            foreach (var command in commands)
            {
                helpText += $"{command.Patterns[0]}, ";
            }
            
            helpText += "and more. Check the console for a complete list.";
            
            if (_ttsService != null)
            {
                await _ttsService.SpeakAsync(helpText);
            }
        }
    }

    /// <summary>
    /// Voice command definition
    /// </summary>
    public class VoiceCommand
    {
        public string Id { get; set; }
        public string[] Patterns { get; set; }
        public VoiceCommandAction Action { get; set; }
        public string Description { get; set; }
    }

    /// <summary>
    /// Voice command actions
    /// </summary>
    public enum VoiceCommandAction
    {
        StartMission,
        StartTour,
        StartLandingChallenge,
        StartBushFlying,
        ShowStatus,
        MonitorProgress,
        Exit,
        MainMenu,
        AcceptMission,
        DeclineMission,
        AcceptTour,
        DeclineTour,
        AcceptLandingChallenge,
        DeclineLandingChallenge,
        RepeatInformation,
        NextPoi,
        Help,
        Settings,
        TestVoice
    }

    /// <summary>
    /// Voice command event arguments
    /// </summary>
    public class VoiceCommandEventArgs : EventArgs
    {
        public VoiceCommand Command { get; }
        public string OriginalText { get; }

        public VoiceCommandEventArgs(VoiceCommand command, string originalText)
        {
            Command = command;
            OriginalText = originalText;
        }
    }
}
