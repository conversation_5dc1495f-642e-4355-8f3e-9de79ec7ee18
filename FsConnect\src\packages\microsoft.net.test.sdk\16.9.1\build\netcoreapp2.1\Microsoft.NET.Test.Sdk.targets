<!--
 ***********************************************************************************************
 Microsoft.NET.Test.Sdk.targets

 WARNING:  DO NOT MODIFY this file unless you are knowledgeable about MSBuild and have
           created a backup copy.  Incorrect changes to this file will make it
           impossible to load or build your projects from the command-line or the IDE.

 Copyright (c) .NET Foundation. All rights reserved. 
 ***********************************************************************************************
-->

<Project InitialTargets="GenerateProgramFile" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">

  <!--
    Output type for .NET Core test projects should be exe.
    https://devdiv.visualstudio.com/DevDiv/_workitems?id=375688&_a=edit
  -->
  <PropertyGroup>
    <OutputType>Exe</OutputType>
  </PropertyGroup>

  <PropertyGroup>
    <GeneratedProgramFile Condition="'$(GeneratedProgramFile)' == ''">$(MSBuildThisFileDirectory)Microsoft.NET.Test.Sdk.Program$(DefaultLanguageSourceExtension)</GeneratedProgramFile>
    <GenerateProgramFile Condition="'$(GenerateProgramFile)' == ''">true</GenerateProgramFile>
  </PropertyGroup>

  <!--
    ============================================================
    GenerateProgramFile
    Generates Program file which contains the Main entry point
    ============================================================
  -->
  <Target Name="GenerateProgramFile"
          Condition="'$(GenerateProgramFile)' == 'true'">

    <ItemGroup Condition="'$(Language)' == 'VB' or '$(Language)' == 'C#'">
      <Compile Include="$(GeneratedProgramFile)"/>
    </ItemGroup>

    <ItemGroup Condition="'$(Language)' == 'F#'">
      <ProgramCompiles Include="@(Compile)" Condition="'%(Identity)' == 'Program.fs'" />
      <CompileAfter Include="$(GeneratedProgramFile)" Condition="@(ProgramCompiles-&gt;Count()) == 0" />
    </ItemGroup>

    <Warning Condition="@(ProgramCompiles-&gt;Count()) != 0" Text="A 'Program.fs' file can be automatically generated for F# .NET Core test projects. To fix this warning, either delete the file from the project, or set the &lt;GenerateProgramFile&gt; property to 'false'." />

  </Target>

</Project>
