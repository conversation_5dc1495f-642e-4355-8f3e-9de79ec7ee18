﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.Cryptography.X509Certificates</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeX509ChainHandle">
      <summary>Provides a wrapper class that represents the handle of an X.509 chain object. For more information, see <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" />.</summary>
    </member>
    <member name="M:Microsoft.Win32.SafeHandles.SafeX509ChainHandle.#ctor">
      <summary>Creates a <see cref="T:Microsoft.Win32.SafeHandles.SafeX509ChainHandle" />.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.CertificateRequest">
      <summary>Represents an abstraction over the PKCS#10 CertificationRequestInfo and the X.509 TbsCertificate.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.CertificateRequest.#ctor(System.Security.Cryptography.X509Certificates.X500DistinguishedName,System.Security.Cryptography.ECDsa,System.Security.Cryptography.HashAlgorithmName)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.CertificateRequest" /> class using the specified subject name, ECDSA key, and hash algorithm.</summary>
      <param name="subjectName">The parsed representation of the subject name for the certificate or certificate request.</param>
      <param name="key">An ECDSA key whose public key material will be included in the certificate or certificate request. If the <see cref="M:System.Security.Cryptography.X509Certificates.CertificateRequest.CreateSelfSigned(System.DateTimeOffset,System.DateTimeOffset)" /> method is called, this key is used as a private key.</param>
      <param name="hashAlgorithm">The hash algorithm to use when signing the certificate or certificate request.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subjectName" /> is <see langword="null" />.

 -or-  

<paramref name="key" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.CertificateRequest.#ctor(System.Security.Cryptography.X509Certificates.X500DistinguishedName,System.Security.Cryptography.RSA,System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.RSASignaturePadding)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.CertificateRequest" /> class using the specified subject name, RSA key, and hash algorithm.</summary>
      <param name="subjectName">The parsed representation of the subject name for the certificate or certificate request.</param>
      <param name="key">A RSA key whose public key material will be included in the certificate or certificate request. If the <see cref="M:System.Security.Cryptography.X509Certificates.CertificateRequest.CreateSelfSigned(System.DateTimeOffset,System.DateTimeOffset)" /> method is called, this key is used as a private key.</param>
      <param name="hashAlgorithm">The hash algorithm to use when signing the certificate or certificate request.</param>
      <param name="padding">The RSA signature padding to apply if self-signing or being signed with an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subjectName" /> is <see langword="null" />.

 -or-  

<paramref name="key" /> is <see langword="null" />.

 -or-  

<paramref name="padding" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.CertificateRequest.#ctor(System.Security.Cryptography.X509Certificates.X500DistinguishedName,System.Security.Cryptography.X509Certificates.PublicKey,System.Security.Cryptography.HashAlgorithmName)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.CertificateRequest" /> class using the specified subject name, encoded public key, and hash algorithm.</summary>
      <param name="subjectName">The parsed representation of the subject name for the certificate or certificate request.</param>
      <param name="publicKey">The encoded representation of the public key to include in the certificate or certificate request.</param>
      <param name="hashAlgorithm">The hash algorithm to use when signing the certificate or certificate request.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subjectName" /> is <see langword="null" />.

 -or-  

<paramref name="publicKey" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.CertificateRequest.#ctor(System.String,System.Security.Cryptography.ECDsa,System.Security.Cryptography.HashAlgorithmName)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.CertificateRequest" /> class using the specified subject name, ECDSA key, and hash algorithm.</summary>
      <param name="subjectName">The string representation of the subject name for the certificate or certificate request.</param>
      <param name="key">An ECDSA key whose public key material will be included in the certificate or certificate request. If the <see cref="M:System.Security.Cryptography.X509Certificates.CertificateRequest.CreateSelfSigned(System.DateTimeOffset,System.DateTimeOffset)" /> method is called, this key is used as a private key.</param>
      <param name="hashAlgorithm">The hash algorithm to use when signing the certificate or certificate request.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subjectName" /> is <see langword="null" />.

 -or-  

<paramref name="key" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.CertificateRequest.#ctor(System.String,System.Security.Cryptography.RSA,System.Security.Cryptography.HashAlgorithmName,System.Security.Cryptography.RSASignaturePadding)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.CertificateRequest" /> class using the specified subject name, RSA key, and hash algorithm.</summary>
      <param name="subjectName">The string representation of the subject name for the certificate or certificate request.</param>
      <param name="key">A RSA key whose public key material will be included in the certificate or certificate request. If the <see cref="M:System.Security.Cryptography.X509Certificates.CertificateRequest.CreateSelfSigned(System.DateTimeOffset,System.DateTimeOffset)" /> method is called, this key is used as a private key.</param>
      <param name="hashAlgorithm">The hash algorithm to use when signing the certificate or certificate request.</param>
      <param name="padding">The RSA signature padding to apply if self-signing or being signed with an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="subjectName" /> is <see langword="null" />.

 -or-  

<paramref name="key" /> is <see langword="null" />.

 -or-  

<paramref name="padding" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.CertificateRequest.Create(System.Security.Cryptography.X509Certificates.X500DistinguishedName,System.Security.Cryptography.X509Certificates.X509SignatureGenerator,System.DateTimeOffset,System.DateTimeOffset,System.Byte[])">
      <summary>Signs the current certificate request to create a chain-signed or self-signed certificate.</summary>
      <param name="issuerName">The <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> for the issuer.</param>
      <param name="generator">An <see cref="T:System.Security.Cryptography.X509Certificates.X509SignatureGenerator" /> object representing the issuing certificate authority.</param>
      <param name="notBefore">The oldest date and time when this certificate is considered valid. Typically <see cref="P:System.DateTimeOffset.UtcNow" />, plus or minus a few seconds.</param>
      <param name="notAfter">The date and time when this certificate is no longer considered valid.</param>
      <param name="serialNumber">The serial number to use for the new certificate. This value should be unique per issuer. The value is interpreted as an unsigned integer of arbitrary size in big-endian byte ordering. RFC 3280 recommends confining it to 20 bytes or less.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="issuerCertificate" /> is <see langword="null" />.

 -or-

<paramref name="generator" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="notAfter" /> represents a date and time that happens earlier than <paramref name="notBefore" />.

 -or-

<paramref name="serialNumber" /> is zero length or <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurs during the signing operation.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object with the specified values. The returned object won't assert <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.HasPrivateKey" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.CertificateRequest.Create(System.Security.Cryptography.X509Certificates.X500DistinguishedName,System.Security.Cryptography.X509Certificates.X509SignatureGenerator,System.DateTimeOffset,System.DateTimeOffset,System.ReadOnlySpan{System.Byte})">
      <summary>Sign the current certificate request to create a chain-signed or self-signed certificate.</summary>
      <param name="issuerName">The distinguished name of the issuer.</param>
      <param name="generator">The issuing certificate authority.</param>
      <param name="notBefore">The oldest date and time where this certificate is considered valid.
              Typically <see cref="P:System.DateTimeOffset.UtcNow" />, plus or minus a few seconds.</param>
      <param name="notAfter">The date and time where this certificate is no longer considered valid.</param>
      <param name="serialNumber">The serial number to use for the new certificate. This value should be unique per issuer.
              The value is interpreted as an unsigned (big) integer in big endian byte ordering.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="issuerName" /> or <paramref name="generator" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="notAfter" /> represents a date and time before <paramref name="notBefore" />.

-or

<paramref name="serialNumber" /> has length 0.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurred during the signing operation.</exception>
      <returns>A certificate with the specified values. The returned object will not assert <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.HasPrivateKey" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.CertificateRequest.Create(System.Security.Cryptography.X509Certificates.X509Certificate2,System.DateTimeOffset,System.DateTimeOffset,System.Byte[])">
      <summary>Creates a certificate using the established subject, key, and optional extensions using the specified certificate as the issuer.</summary>
      <param name="issuerCertificate">An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> instance representing the issuing Certificate Authority (CA).</param>
      <param name="notBefore">The oldest date and time when this certificate is considered valid. Typically <see cref="P:System.DateTimeOffset.UtcNow" />, plus or minus a few seconds.</param>
      <param name="notAfter">The date and time when this certificate is no longer considered valid.</param>
      <param name="serialNumber">The serial number to use for the new certificate. This value should be unique per issuer. The value is interpreted as an unsigned integer of arbitrary size in big-endian byte ordering. RFC 3280 recommends confining it to 20 bytes or less.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="issuerCertificate" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="issuerCertificate" /> doesn't contain a private key.

 -or-

The type of signing key represented by <paramref name="issuerCertificate" /> couldn't be determined.

 -or-

<paramref name="notAfter" /> represents a date and time that happens earlier than <paramref name="notBefore" />.

 -or-

<paramref name="serialNumber" /> is zero length or <see langword="null" />.

 -or-

<paramref name="issuerCertificate" /> has a different key algorithm than the requested certificate.

-or-

The <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.HasPrivateKey" /> value for <paramref name="issuerCertificate" /> is <see langword="false" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="issuerCertificate" /> is an RSA certificate and the current object was created using a constructor that doesn't accept a <paramref name="padding" /> parameter.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <see cref="P:System.Security.Cryptography.X509Certificates.CertificateRequest.HashAlgorithm" /> property value is not supported.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object with the specified values. The returned object won't assert <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.HasPrivateKey" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.CertificateRequest.Create(System.Security.Cryptography.X509Certificates.X509Certificate2,System.DateTimeOffset,System.DateTimeOffset,System.ReadOnlySpan{System.Byte})">
      <summary>Create a certificate using the established subject, key, and optional extensions using the provided certificate as the issuer.</summary>
      <param name="issuerCertificate">An X509Certificate2 instance representing the issuing Certificate Authority (CA).</param>
      <param name="notBefore">The oldest date and time where this certificate is considered valid.
              Typically <see cref="P:System.DateTimeOffset.UtcNow" />, plus or minus a few seconds.</param>
      <param name="notAfter">The date and time where this certificate is no longer considered valid.</param>
      <param name="serialNumber">The serial number to use for the new certificate. This value should be unique per issuer.
              The value is interpreted as an unsigned (big) integer in big endian byte ordering.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="issuerCertificate" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.HasPrivateKey" /> value for <paramref name="issuerCertificate" /> is <see langword="false" />.</exception>
      <exception cref="T:System.ArgumentException">The type of signing key represented by <paramref name="issuerCertificate" /> could not be determined.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="notAfter" /> represents a date and time before <paramref name="notBefore" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="serialNumber" /> has length 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="issuerCertificate" /> has a different key algorithm than the requested certificate.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="issuerCertificate" /> is an RSA certificate and this object was created via a constructor that doesn't accept an <see cref="T:System.Security.Cryptography.RSASignaturePadding" /> value.</exception>
      <returns>A certificate with the specified values. The returned object will not assert <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.HasPrivateKey" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.CertificateRequest.CreateSelfSigned(System.DateTimeOffset,System.DateTimeOffset)">
      <summary>Creates a self-signed certificate using the established subject, key, and optional extensions.</summary>
      <param name="notBefore">The oldest date and time when this certificate is considered valid. Typically <see cref="P:System.DateTimeOffset.UtcNow" />, plus or minus a few seconds.</param>
      <param name="notAfter">The date and time when this certificate is no longer considered valid.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="notAfter" /> represents a date and time that happens earlier than <paramref name="notBefore" />.</exception>
      <exception cref="T:System.InvalidOperationException">The current object was created using a constructor that doesn't accept a signing key.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error occurs during the certificate creation process.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <see cref="P:System.Security.Cryptography.X509Certificates.CertificateRequest.HashAlgorithm" /> property value is not supported.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object with the specified values. The returned object will assert <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.HasPrivateKey" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.CertificateRequest.CreateSigningRequest">
      <summary>Creates an ASN.1 DER-encoded PKCS#10 CertificationRequest value representing the state of the current object.</summary>
      <exception cref="T:System.InvalidOperationException">The current object was created using a constructor that doesn't accept a signing key.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <see cref="P:System.Security.Cryptography.X509Certificates.CertificateRequest.HashAlgorithm" /> property value is not supported.</exception>
      <returns>A DER-encoded certificate signing request.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.CertificateRequest.CreateSigningRequest(System.Security.Cryptography.X509Certificates.X509SignatureGenerator)">
      <summary>Creates an ASN.1 DER-encoded PKCS#10 CertificationRequest representing the current state of the current object using the provided signature generator.</summary>
      <param name="signatureGenerator">The signature generator with which to sign the request.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="signatureGenerator" /> is <see langword="null" />.</exception>
      <returns>A DER-encoded certificate signing request.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.CertificateRequest.CertificateExtensions">
      <summary>Gets the X.509 Certificate Extensions collection, which is a mutable collection, to include in the certificate or certificate request.</summary>
      <returns>The X.509 Certificate Extensions collection to include in the certificate or certificate request.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.CertificateRequest.HashAlgorithm">
      <summary>Gets the hash algorithm to use when signing the certificate or certificate request.</summary>
      <returns>The hash algorithm to use when signing the certificate or certificate request.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.CertificateRequest.PublicKey">
      <summary>Gets a representation of the public key for the certificate or certificate request.</summary>
      <returns>The representation of the public key for the certificate or certificate request.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.CertificateRequest.SubjectName">
      <summary>Gets the X.500 Distinguished Name to use as the Subject in a created certificate or certificate request.</summary>
      <returns>The X.500 Distinguished Name to use as the Subject in a created certificate or certificate request.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.DSACertificateExtensions">
      <summary>Provides extension methods for retrieving <see cref="T:System.Security.Cryptography.DSA" /> implementations for the public and private keys of an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.DSACertificateExtensions.CopyWithPrivateKey(System.Security.Cryptography.X509Certificates.X509Certificate2,System.Security.Cryptography.DSA)">
      <summary>Combines a private key with the public key of a <see cref="T:System.Security.Cryptography.DSA" /> certificate to generate a new DSA certificate.</summary>
      <param name="certificate">The DSA certificate.</param>
      <param name="privateKey">The private DSA key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> or <paramref name="privateKey" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The certificate already has an associated private key.</exception>
      <exception cref="T:System.ArgumentException">The certificate doesn't have a public key.  
  
 -or-  
  
 The specified private key doesn't match the public key for the specified certificate.</exception>
      <returns>A new DSA certificate with the <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.HasPrivateKey" /> property set to <see langword="true" />. The input DSA certificate object isn't modified.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.DSACertificateExtensions.GetDSAPrivateKey(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Gets the <see cref="T:System.Security.Cryptography.DSA" /> private key from the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />.</summary>
      <param name="certificate">The certificate.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> is <see langword="null" />.</exception>
      <returns>The private key, or <see langword="null" /> if the certificate does not have a DSA private key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.DSACertificateExtensions.GetDSAPublicKey(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Gets the <see cref="T:System.Security.Cryptography.DSA" /> public key from the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />.</summary>
      <param name="certificate">The certificate.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Windows reports an error. See the <see cref="P:System.Exception.Message" /> property for more information.</exception>
      <returns>The public key, or <see langword="null" /> if the certificate does not have a DSA public key.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.ECDsaCertificateExtensions">
      <summary>Provides extension methods for retrieving <see cref="T:System.Security.Cryptography.ECDsa" /> implementations for the     public and private keys of a <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> certificate.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.ECDsaCertificateExtensions.CopyWithPrivateKey(System.Security.Cryptography.X509Certificates.X509Certificate2,System.Security.Cryptography.ECDsa)">
      <summary>Combines a private key with the public key of an <see cref="T:System.Security.Cryptography.ECDsa" /> certificate to generate a new ECDSA certificate.</summary>
      <param name="certificate">The ECDSA certificate.</param>
      <param name="privateKey">The private ECDSA key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> or <paramref name="privateKey" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The certificate already has an associated private key.</exception>
      <exception cref="T:System.ArgumentException">The certificate doesn't have a public key.  
  
 -or-  
  
 The specified private key doesn't match the public key for the specified certificate.</exception>
      <returns>A new ECDSA certificate with the <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.HasPrivateKey" /> property set to <see langword="true" />. The input ECDSA certificate object isn't modified.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.ECDsaCertificateExtensions.GetECDsaPrivateKey(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Gets the <see cref="T:System.Security.Cryptography.ECDsa" /> private key from the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> certificate.</summary>
      <param name="certificate">The certificate.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> is <see langword="null" />.</exception>
      <returns>The private key, or <see langword="null" /> if the certificate does not have an ECDsa private key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.ECDsaCertificateExtensions.GetECDsaPublicKey(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Gets the <see cref="T:System.Security.Cryptography.ECDsa" /> public key from the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> certificate.</summary>
      <param name="certificate">The certificate.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The handle is invalid.</exception>
      <returns>The public key, or <see langword="null" /> if the certificate does not have an ECDsa public key.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.OpenFlags">
      <summary>Specifies the way to open the X.509 certificate store.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.IncludeArchived">
      <summary>Open the X.509 certificate store and include archived certificates.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.MaxAllowed">
      <summary>Open the X.509 certificate store for the highest access allowed.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.OpenExistingOnly">
      <summary>Opens only existing stores; if no store exists, the <see cref="M:System.Security.Cryptography.X509Certificates.X509Store.Open(System.Security.Cryptography.X509Certificates.OpenFlags)" /> method will not create a new store.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.ReadOnly">
      <summary>Open the X.509 certificate store for reading only.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.OpenFlags.ReadWrite">
      <summary>Open the X.509 certificate store for both reading and writing.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.PublicKey">
      <summary>Represents a certificate's public key information. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.PublicKey.#ctor(System.Security.Cryptography.AsymmetricAlgorithm)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" /> class using SubjectPublicKeyInfo from an <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" />.</summary>
      <param name="key">An asymmetric algorithm to obtain the SubjectPublicKeyInfo from.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The SubjectPublicKeyInfo could not be decoded. The <see cref="M:System.Security.Cryptography.AsymmetricAlgorithm.ExportSubjectPublicKeyInfo" /> must return a valid ASN.1-DER encoded X.509 SubjectPublicKeyInfo.</exception>
      <exception cref="T:System.NotImplementedException">
        <see cref="M:System.Security.Cryptography.AsymmetricAlgorithm.ExportSubjectPublicKeyInfo" /> has not been overridden in a derived class.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.PublicKey.#ctor(System.Security.Cryptography.Oid,System.Security.Cryptography.AsnEncodedData,System.Security.Cryptography.AsnEncodedData)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" /> class using an object identifier (OID) object of the public key, an ASN.1-encoded representation of the public key parameters, and an ASN.1-encoded representation of the public key value.</summary>
      <param name="oid">An object identifier (OID) object that represents the public key.</param>
      <param name="parameters">An ASN.1-encoded representation of the public key parameters.</param>
      <param name="keyValue">An ASN.1-encoded representation of the public key value.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.PublicKey.CreateFromSubjectPublicKeyInfo(System.ReadOnlySpan{System.Byte},System.Int32@)">
      <summary>Creates a new instance of <see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" /> from a X.509 SubjectPublicKeyInfo.</summary>
      <param name="source">The bytes of an X.509 SubjectPublicKeyInfo structure in the ASN.1-DER encoding.</param>
      <param name="bytesRead">When this method returns, contains a value that indicates the number of bytes read from <paramref name="source" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The SubjectPublicKeyInfo could not be decoded.</exception>
      <returns>A public key representing the SubjectPublicKeyInfo.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.PublicKey.ExportSubjectPublicKeyInfo">
      <summary>Exports the current key in the X.509 SubjectPublicKeyInfo format.</summary>
      <returns>A byte array containing the X.509 SubjectPublicKeyInfo representation of this key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.PublicKey.GetDSAPublicKey">
      <summary>Gets the <see cref="T:System.Security.Cryptography.DSA" /> public key, or <see langword="null" /> if the key is not an DSA key.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key contents are corrupt or could not be read successfully.</exception>
      <returns>The public key, or <see langword="null" /> if the key is not an DSA key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.PublicKey.GetECDiffieHellmanPublicKey">
      <summary>Gets the <see cref="T:System.Security.Cryptography.ECDiffieHellman" /> public key, or <see langword="null" /> if the key is not an ECDiffieHellman key.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key contents are corrupt or could not be read successfully.</exception>
      <returns>The public key, or <see langword="null" /> if the key is not an ECDiffieHellman key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.PublicKey.GetECDsaPublicKey">
      <summary>Gets the <see cref="T:System.Security.Cryptography.ECDsa" /> public key, or <see langword="null" /> if the key is not an ECDsa key.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key contents are corrupt or could not be read successfully.</exception>
      <returns>The public key, or <see langword="null" /> if the key is not an ECDsa key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.PublicKey.GetRSAPublicKey">
      <summary>Gets the <see cref="T:System.Security.Cryptography.RSA" /> public key, or <see langword="null" /> if the key is not an RSA key.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key contents are corrupt or could not be read successfully.</exception>
      <returns>The public key, or <see langword="null" /> if the key is not an RSA key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.PublicKey.TryExportSubjectPublicKeyInfo(System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to export the current key in the X.509 SubjectPublicKeyInfo format into a provided buffer.</summary>
      <param name="destination">The byte span to receive the X.509 SubjectPublicKeyInfo data.</param>
      <param name="bytesWritten">When this method returns, contains a value that indicates the number of bytes written to <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is big enough to receive the output; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.EncodedKeyValue">
      <summary>Gets the ASN.1-encoded representation of the public key value.</summary>
      <returns>The ASN.1-encoded representation of the public key value.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.EncodedParameters">
      <summary>Gets the ASN.1-encoded representation of the public key parameters.</summary>
      <returns>The ASN.1-encoded representation of the public key parameters.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.Key">
      <summary>Gets an <see cref="T:System.Security.Cryptography.RSA" /> derived object or a <see cref="T:System.Security.Cryptography.DSA" /> derived object representing the public key.</summary>
      <exception cref="T:System.NotSupportedException">The key algorithm is not supported.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> object representing the public key.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.PublicKey.Oid">
      <summary>Gets an object identifier (OID) object of the public key.</summary>
      <returns>An object identifier (OID) object of the public key.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.RSACertificateExtensions">
      <summary>Provides extension methods for retrieving <see cref="T:System.Security.Cryptography.RSA" /> implementations for the public and private keys of an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.RSACertificateExtensions.CopyWithPrivateKey(System.Security.Cryptography.X509Certificates.X509Certificate2,System.Security.Cryptography.RSA)">
      <summary>Combines a private key with the public key of an <see cref="T:System.Security.Cryptography.RSA" /> certificate to generate a new RSA certificate.</summary>
      <param name="certificate">The RSA certificate.</param>
      <param name="privateKey">The private RSA key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> or <paramref name="privateKey" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The certificate already has an associated private key.</exception>
      <exception cref="T:System.ArgumentException">The certificate doesn't have a public key.  
  
 -or-  
  
 The specified private key doesn't match the public key for the specified certificate.</exception>
      <returns>A new RSA certificate with the <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.HasPrivateKey" /> property set to <see langword="true" />. The input RSA certificate object isn't modified.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.RSACertificateExtensions.GetRSAPrivateKey(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Gets the <see cref="T:System.Security.Cryptography.RSA" /> private key from the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />.</summary>
      <param name="certificate">The certificate.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> is <see langword="null" />.</exception>
      <returns>The private key, or <see langword="null" /> if the certificate does not have an RSA private key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.RSACertificateExtensions.GetRSAPublicKey(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Gets the <see cref="T:System.Security.Cryptography.RSA" /> public key from the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />.</summary>
      <param name="certificate">The certificate.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">Windows reports an error. See the <see cref="P:System.Exception.Message" /> property for more information.</exception>
      <returns>The public key, or <see langword="null" /> if the certificate does not have an RSA public key.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.StoreLocation">
      <summary>Specifies the location of the X.509 certificate store.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreLocation.CurrentUser">
      <summary>The X.509 certificate store used by the current user.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreLocation.LocalMachine">
      <summary>The X.509 certificate store assigned to the local machine.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.StoreName">
      <summary>Specifies the name of the X.509 certificate store to open.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.AddressBook">
      <summary>The X.509 certificate store for other users.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.AuthRoot">
      <summary>The X.509 certificate store for third-party certificate authorities (CAs).</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.CertificateAuthority">
      <summary>The X.509 certificate store for intermediate certificate authorities (CAs).</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.Disallowed">
      <summary>The X.509 certificate store for revoked certificates.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.My">
      <summary>The X.509 certificate store for personal certificates.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.Root">
      <summary>The X.509 certificate store for trusted root certificate authorities (CAs).</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.TrustedPeople">
      <summary>The X.509 certificate store for directly trusted people and resources.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.StoreName.TrustedPublisher">
      <summary>The X.509 certificate store for directly trusted publishers.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.SubjectAlternativeNameBuilder">
      <summary>This class facilitates building a subject alternative name extension for an X.509 certificate.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.SubjectAlternativeNameBuilder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.SubjectAlternativeNameBuilder" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.SubjectAlternativeNameBuilder.AddDnsName(System.String)">
      <summary>Adds a DNS Name to the subject alternative name extension.</summary>
      <param name="dnsName">The DNS name to be added.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="dnsName" /> is either <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.SubjectAlternativeNameBuilder.AddEmailAddress(System.String)">
      <summary>Adds an email address to the subject alternative name extension.</summary>
      <param name="emailAddress">The email address to be added.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="emailAddress" /> is either <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.SubjectAlternativeNameBuilder.AddIpAddress(System.Net.IPAddress)">
      <summary>Adds an IP address to the subject alternative name extension.</summary>
      <param name="ipAddress">The IP address to be added.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ipAddress" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.SubjectAlternativeNameBuilder.AddUri(System.Uri)">
      <summary>Adds a Uniform Resource Identifier (URI) to the subject alternative name extension.</summary>
      <param name="uri">The URI to be added.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.SubjectAlternativeNameBuilder.AddUserPrincipalName(System.String)">
      <summary>Adds a User Principal Name (UPN) to the subject alternative name extension.</summary>
      <param name="upn">The UPN to be added.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="upn" /> is either <see langword="null" /> or <see cref="F:System.String.Empty" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.SubjectAlternativeNameBuilder.Build(System.Boolean)">
      <summary>Returns an <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> object that represents the encoded subject alternative name extension.</summary>
      <param name="critical">
        <see langword="true" /> to mark the extension as <see cref="P:System.Security.Cryptography.X509Certificates.X509Extension.Critical" />; otherwise, <see langword="false" />. The default value is <see langword="false" />.</param>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> object that represents the encoded subject alternative name extension.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName">
      <summary>Represents the distinguished name of an X509 certificate. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> class using information from the specified byte array.</summary>
      <param name="encodedDistinguishedName">A byte array that contains distinguished name information.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.ReadOnlySpan{System.Byte})">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> class using information from the provided data.</summary>
      <param name="encodedDistinguishedName">The encoded distinguished name.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.Security.Cryptography.AsnEncodedData)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> class using the specified <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object.</summary>
      <param name="encodedDistinguishedName">An <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object that represents the distinguished name.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.Security.Cryptography.X509Certificates.X500DistinguishedName)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> class using the specified <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> object.</summary>
      <param name="distinguishedName">An <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> object.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> class using information from the specified string.</summary>
      <param name="distinguishedName">A string that represents the distinguished name.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.#ctor(System.String,System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> class using the specified string and <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags" /> flag.</summary>
      <param name="distinguishedName">A string that represents the distinguished name.</param>
      <param name="flag">A bitwise combination of the enumeration values that specify the characteristics of the distinguished name.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.Decode(System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags)">
      <summary>Decodes a distinguished name using the characteristics specified by the <paramref name="flag" /> parameter.</summary>
      <param name="flag">A bitwise combination of the enumeration values that specify the characteristics of the distinguished name.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate has an invalid name.</exception>
      <returns>The decoded distinguished name.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X500DistinguishedName.Format(System.Boolean)">
      <summary>Returns a formatted version of an X500 distinguished name for printing or for output to a text window or to a console.</summary>
      <param name="multiLine">
        <see langword="true" /> if the return string should contain carriage returns; otherwise, <see langword="false" />.</param>
      <returns>A formatted string that represents the X500 distinguished name.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X500DistinguishedName.Name">
      <summary>Gets the comma-delimited distinguished name from an X500 certificate.</summary>
      <returns>The comma-delimited distinguished name of the X509 certificate.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags">
      <summary>Specifies characteristics of the X.500 distinguished name.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.DoNotUsePlusSign">
      <summary>The distinguished name does not use the plus sign.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.DoNotUseQuotes">
      <summary>The distinguished name does not use quotation marks.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.ForceUTF8Encoding">
      <summary>Forces the distinguished name to encode specific X.500 keys as UTF-8 strings rather than printable Unicode strings. For more information and the list of X.500 keys affected, see the X500NameFlags enumeration.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.None">
      <summary>The distinguished name has no special characteristics.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.Reversed">
      <summary>The distinguished name is reversed.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseCommas">
      <summary>The distinguished name uses commas.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseNewLines">
      <summary>The distinguished name uses the new line character.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseSemicolons">
      <summary>The distinguished name uses semicolons.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseT61Encoding">
      <summary>The distinguished name uses T61 encoding.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X500DistinguishedNameFlags.UseUTF8Encoding">
      <summary>The distinguished name uses UTF8 encoding instead of Unicode character encoding.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension">
      <summary>Defines the constraints set on a certificate. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.#ctor(System.Boolean,System.Boolean,System.Int32,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" /> class. Parameters specify a value that indicates whether a certificate is a certificate authority (CA) certificate, a value that indicates whether the certificate has a restriction on the number of path levels it allows, the number of levels allowed in a certificate's path, and a value that indicates whether the extension is critical.</summary>
      <param name="certificateAuthority">
        <see langword="true" /> if the certificate is a certificate authority (CA) certificate; otherwise, <see langword="false" />.</param>
      <param name="hasPathLengthConstraint">
        <see langword="true" /> if the certificate has a restriction on the number of path levels it allows; otherwise, <see langword="false" />.</param>
      <param name="pathLengthConstraint">The number of levels allowed in a certificate's path.</param>
      <param name="critical">
        <see langword="true" /> if the extension is critical; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" /> class using an <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object and a value that identifies whether the extension is critical.</summary>
      <param name="encodedBasicConstraints">The encoded data to use to create the extension.</param>
      <param name="critical">
        <see langword="true" /> if the extension is critical; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension" /> class using an <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object.</summary>
      <param name="asnEncodedData">The encoded data to use to create the extension.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.CertificateAuthority">
      <summary>Gets a value indicating whether a certificate is a certificate authority (CA) certificate.</summary>
      <returns>
        <see langword="true" /> if the certificate is a certificate authority (CA) certificate, otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.HasPathLengthConstraint">
      <summary>Gets a value indicating whether a certificate has a restriction on the number of path levels it allows.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The extension cannot be decoded.</exception>
      <returns>
        <see langword="true" /> if the certificate has a restriction on the number of path levels it allows, otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509BasicConstraintsExtension.PathLengthConstraint">
      <summary>Gets the number of levels allowed in a certificate's path.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The extension cannot be decoded.</exception>
      <returns>An integer indicating the number of levels allowed in a certificate's path.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate">
      <summary>Provides methods that help you use X.509 v.3 certificates.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> class defined from a sequence of bytes representing an X.509v3 certificate.</summary>
      <param name="data">A byte array containing data from an X.509 certificate.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="rawData" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The length of the <paramref name="rawData" /> parameter is 0.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[],System.Security.SecureString)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> class using a byte array and a password.</summary>
      <param name="rawData">A byte array that contains data from an X.509 certificate.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="rawData" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The length of the <paramref name="rawData" /> parameter is 0.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[],System.Security.SecureString,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> class using a byte array, a password, and a key storage flag.</summary>
      <param name="rawData">A byte array that contains data from an X.509 certificate.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control where and how to import the certificate.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="rawData" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The length of the <paramref name="rawData" /> parameter is 0.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[],System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> class using a byte array and a password.</summary>
      <param name="rawData">A byte array containing data from an X.509 certificate.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="rawData" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The length of the <paramref name="rawData" /> parameter is 0.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> class using a byte array, a password, and a key storage flag.</summary>
      <param name="rawData">A byte array containing data from an X.509 certificate.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control where and how to import the certificate.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="rawData" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The length of the <paramref name="rawData" /> parameter is 0.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.IntPtr)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> class using a handle to an unmanaged <see langword="PCCERT_CONTEXT" /> structure.</summary>
      <param name="handle">A handle to an unmanaged <see langword="PCCERT_CONTEXT" /> structure.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
      <exception cref="T:System.ArgumentException">The handle parameter does not represent a valid <see langword="PCCERT_CONTEXT" /> structure.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> class using a <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object and a <see cref="T:System.Runtime.Serialization.StreamingContext" /> structure.</summary>
      <param name="info">A <see cref="T:System.Runtime.Serialization.SerializationInfo" /> object that describes serialization information.</param>
      <param name="context">A <see cref="T:System.Runtime.Serialization.StreamingContext" /> structure that describes how serialization should be performed.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> class using another <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> class.</summary>
      <param name="cert">A <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> class from which to initialize this class.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="cert" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> class using the name of a PKCS7 signed file.</summary>
      <param name="fileName">The name of a PKCS7 signed file.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="fileName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String,System.Security.SecureString)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> class using a certificate file name and a password.</summary>
      <param name="fileName">The name of a certificate file.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="fileName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String,System.Security.SecureString,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> class using a certificate file name, a password, and a key storage flag.</summary>
      <param name="fileName">The name of a certificate file.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control where and how to import the certificate.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="fileName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> class using the name of a PKCS7 signed file and a password to access the certificate.</summary>
      <param name="fileName">The name of a PKCS7 signed file.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="fileName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.#ctor(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> class using the name of a PKCS7 signed file, a password to access the certificate, and a key storage flag.</summary>
      <param name="fileName">The name of a PKCS7 signed file.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control where and how to import the certificate.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
      <exception cref="T:System.ArgumentException">The <paramref name="fileName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.CreateFromCertFile(System.String)">
      <summary>Creates an X.509v3 certificate from the specified PKCS7 signed file.</summary>
      <param name="filename">The path of the PKCS7 signed file from which to create the X.509 certificate.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="filename" /> parameter is <see langword="null" />.</exception>
      <returns>The newly created X.509 certificate.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.CreateFromSignedFile(System.String)">
      <summary>Creates an X.509v3 certificate from the specified signed file.</summary>
      <param name="filename">The path of the signed file from which to create the X.509 certificate.</param>
      <returns>The newly created X.509 certificate.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Dispose">
      <summary>Releases all resources used by the current <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Dispose(System.Boolean)">
      <summary>Releases all of the unmanaged resources used by this <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Equals(System.Object)">
      <summary>Compares two <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> objects for equality.</summary>
      <param name="obj">An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object to compare to the current object.</param>
      <returns>
        <see langword="true" /> if the current <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object is equal to the object specified by the <paramref name="other" /> parameter; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Equals(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Compares two <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> objects for equality.</summary>
      <param name="other">An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object to compare to the current object.</param>
      <returns>
        <see langword="true" /> if the current <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object is equal to the object specified by the <paramref name="other" /> parameter; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Export(System.Security.Cryptography.X509Certificates.X509ContentType)">
      <summary>Exports the current <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object to a byte array in a format described by one of the <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> values.</summary>
      <param name="contentType">One of the <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> values that describes how to format the output data.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A value other than <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Cert" />, <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedCert" />, or <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs12" /> was passed to the <paramref name="contentType" /> parameter.  
  
 -or-  
  
 The certificate could not be exported.</exception>
      <returns>An array of bytes that represents the current <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Export(System.Security.Cryptography.X509Certificates.X509ContentType,System.Security.SecureString)">
      <summary>Exports the current <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object to a byte array using the specified format and a password.</summary>
      <param name="contentType">One of the <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> values that describes how to format the output data.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A value other than <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Cert" />, <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedCert" />, or <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs12" /> was passed to the <paramref name="contentType" /> parameter.  
  
 -or-  
  
 The certificate could not be exported.</exception>
      <returns>A byte array that represents the current <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Export(System.Security.Cryptography.X509Certificates.X509ContentType,System.String)">
      <summary>Exports the current <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object to a byte array in a format described by one of the <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> values, and using the specified password.</summary>
      <param name="contentType">One of the <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> values that describes how to format the output data.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">A value other than <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Cert" />, <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedCert" />, or <see cref="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs12" /> was passed to the <paramref name="contentType" /> parameter.  
  
 -or-  
  
 The certificate could not be exported.</exception>
      <returns>An array of bytes that represents the current <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.FormatDate(System.DateTime)">
      <summary>Converts the specified date and time to a string.</summary>
      <param name="date">The date and time to convert.</param>
      <returns>A string representation of the value of the <see cref="T:System.DateTime" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetCertHash">
      <summary>Returns the hash value for the X.509v3 certificate as an array of bytes.</summary>
      <returns>The hash value for the X.509 certificate.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetCertHash(System.Security.Cryptography.HashAlgorithmName)">
      <summary>Returns the hash value for the X.509v3 certificate that is computed by using the specified cryptographic hash algorithm.</summary>
      <param name="hashAlgorithm">The name of the cryptographic hash algorithm to use.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or an empty string.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="hashAlgorithm" /> is not a known hash algorithm.</exception>
      <returns>A byte array that contains the hash value for the X.509 certificate.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetCertHashString">
      <summary>Returns the SHA1 hash value for the X.509v3 certificate as a hexadecimal string.</summary>
      <returns>The hexadecimal string representation of the X.509 certificate hash value.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetCertHashString(System.Security.Cryptography.HashAlgorithmName)">
      <summary>Returns a hexadecimal string containing the hash value for the X.509v3 certificate computed using the specified cryptographic hash algorithm.</summary>
      <param name="hashAlgorithm">The name of the cryptographic hash algorithm to use.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or an empty string.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="hashAlgorithm" /> is not a known hash algorithm.</exception>
      <returns>The hexadecimal string representation of the X.509 certificate hash value.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetEffectiveDateString">
      <summary>Returns the effective date of this X.509v3 certificate.</summary>
      <returns>The effective date for this X.509 certificate.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetExpirationDateString">
      <summary>Returns the expiration date of this X.509v3 certificate.</summary>
      <returns>The expiration date for this X.509 certificate.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetFormat">
      <summary>Returns the name of the format of this X.509v3 certificate.</summary>
      <returns>The format of this X.509 certificate.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetHashCode">
      <summary>Returns the hash code for the X.509v3 certificate as an integer.</summary>
      <returns>The hash code for the X.509 certificate as an integer.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetIssuerName">
      <summary>Returns the name of the certification authority that issued the X.509v3 certificate.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
      <returns>The name of the certification authority that issued the X.509 certificate.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetKeyAlgorithm">
      <summary>Returns the key algorithm information for this X.509v3 certificate as a string.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate context is invalid.</exception>
      <returns>The key algorithm information for this X.509 certificate as a string.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetKeyAlgorithmParameters">
      <summary>Returns the key algorithm parameters for the X.509v3 certificate as an array of bytes.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate context is invalid.</exception>
      <returns>The key algorithm parameters for the X.509 certificate as an array of bytes.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetKeyAlgorithmParametersString">
      <summary>Returns the key algorithm parameters for the X.509v3 certificate as a hexadecimal string.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate context is invalid.</exception>
      <returns>The key algorithm parameters for the X.509 certificate as a hexadecimal string.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetName">
      <summary>Returns the name of the principal to which the certificate was issued.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate context is invalid.</exception>
      <returns>The name of the principal to which the certificate was issued.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetPublicKey">
      <summary>Returns the public key for the X.509v3 certificate as an array of bytes.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate context is invalid.</exception>
      <returns>The public key for the X.509 certificate as an array of bytes.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetPublicKeyString">
      <summary>Returns the public key for the X.509v3 certificate as a hexadecimal string.</summary>
      <returns>The public key for the X.509 certificate as a hexadecimal string.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetRawCertData">
      <summary>Returns the raw data for the entire X.509v3 certificate as an array of bytes.</summary>
      <returns>A byte array containing the X.509 certificate data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetRawCertDataString">
      <summary>Returns the raw data for the entire X.509v3 certificate as a hexadecimal string.</summary>
      <returns>The X.509 certificate data as a hexadecimal string.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetSerialNumber">
      <summary>Returns the serial number of the X.509v3 certificate as an array of bytes in little-endian order.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate context is invalid.</exception>
      <returns>The serial number of the X.509 certificate as an array of bytes in little-endian order.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetSerialNumberString">
      <summary>Returns the serial number of the X.509v3 certificate as a little-endian hexadecimal string .</summary>
      <returns>The serial number of the X.509 certificate as a little-endian hexadecimal string.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Import(System.Byte[])">
      <summary>Populates the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object with data from a byte array.</summary>
      <param name="rawData">A byte array containing data from an X.509 certificate.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="rawData" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The length of the <paramref name="rawData" /> parameter is 0.</exception>
      <exception cref="T:System.PlatformNotSupportedException">.NET Core and .NET 5+ only: In all cases.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Import(System.Byte[],System.Security.SecureString,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Populates an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object using data from a byte array, a password, and a key storage flag.</summary>
      <param name="rawData">A byte array that contains data from an X.509 certificate.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control where and how to import the certificate.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="rawData" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The length of the <paramref name="rawData" /> parameter is 0.</exception>
      <exception cref="T:System.PlatformNotSupportedException">.NET Core and .NET 5+ only: In all cases.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Import(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Populates the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object using data from a byte array, a password, and flags for determining how the private key is imported.</summary>
      <param name="rawData">A byte array containing data from an X.509 certificate.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control where and how to import the certificate.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="rawData" /> parameter is <see langword="null" />.  
  
 -or-  
  
 The length of the <paramref name="rawData" /> parameter is 0.</exception>
      <exception cref="T:System.PlatformNotSupportedException">.NET Core and .NET 5+ only: In all cases.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Import(System.String)">
      <summary>Populates the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object with information from a certificate file.</summary>
      <param name="fileName">The name of a certificate file represented as a string.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="fileName" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.PlatformNotSupportedException">.NET Core and .NET 5+ only: In all cases.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Import(System.String,System.Security.SecureString,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Populates an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object with information from a certificate file, a password, and a key storage flag.</summary>
      <param name="fileName">The name of a certificate file.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control where and how to import the certificate.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="fileName" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.PlatformNotSupportedException">.NET Core and .NET 5+ only: In all cases.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Import(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Populates the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object with information from a certificate file, a password, and a <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags" /> value.</summary>
      <param name="fileName">The name of a certificate file represented as a string.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control where and how to import the certificate.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="fileName" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.PlatformNotSupportedException">.NET Core and .NET 5+ only: In all cases.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.Reset">
      <summary>Resets the state of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.System#Runtime#Serialization#IDeserializationCallback#OnDeserialization(System.Object)">
      <summary>Implements the <see cref="T:System.Runtime.Serialization.ISerializable" /> interface and is called back by the deserialization event when deserialization is complete.</summary>
      <param name="sender">The source of the deserialization event.</param>
      <exception cref="T:System.PlatformNotSupportedException">.NET Core and .NET 5+ only: In all cases.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.System#Runtime#Serialization#ISerializable#GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Gets serialization information with all the data needed to recreate an instance of the current <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object.</summary>
      <param name="info">The object to populate with serialization information.</param>
      <param name="context">The destination context of the serialization.</param>
      <exception cref="T:System.PlatformNotSupportedException">.NET Core and .NET 5+ only: In all cases.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.ToString">
      <summary>Returns a string representation of the current <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object.</summary>
      <returns>A string representation of the current <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.ToString(System.Boolean)">
      <summary>Returns a string representation of the current <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object, with extra information, if specified.</summary>
      <param name="fVerbose">
        <see langword="true" /> to produce the verbose form of the string representation; otherwise, <see langword="false" />.</param>
      <returns>A string representation of the current <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate.TryGetCertHash(System.Security.Cryptography.HashAlgorithmName,System.Span{System.Byte},System.Int32@)">
      <summary>Attempts to produce a "thumbprint" for the certificate by hashing the encoded representation of the certificate with the specified hash algorithm.</summary>
      <param name="hashAlgorithm">The algorithm to use for the thumbprint.</param>
      <param name="destination">The buffer to receive the certificate thumbprint.</param>
      <param name="bytesWritten">When this method returns, the total number of bytes written into <paramref name="destination" />. This parameter is treated as uninitialized.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="hashAlgorithm" />.<see cref="P:System.Security.Cryptography.HashAlgorithmName.Name" /> is <see langword="null" /> or an empty string.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="hashAlgorithm" /> is not a known hash algorithm.</exception>
      <returns>
        <see langword="true" /> if <paramref name="destination" /> is long enough to receive the hash value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate.Handle">
      <summary>Gets a handle to a Microsoft Cryptographic API certificate context described by an unmanaged <see langword="PCCERT_CONTEXT" /> structure.</summary>
      <returns>An <see cref="T:System.IntPtr" /> structure that represents an unmanaged <see langword="PCCERT_CONTEXT" /> structure.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate.Issuer">
      <summary>Gets the name of the certificate authority that issued the X.509v3 certificate.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate handle is invalid.</exception>
      <returns>The name of the certificate authority that issued the X.509v3 certificate.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate.Subject">
      <summary>Gets the subject distinguished name from the certificate.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate handle is invalid.</exception>
      <returns>The subject distinguished name from the certificate.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate2">
      <summary>Represents an X.509 certificate.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> class using information from a byte array.</summary>
      <param name="rawData">A byte array containing data from an X.509 certificate.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[],System.Security.SecureString)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> class using a byte array and a password.</summary>
      <param name="rawData">A byte array that contains data from an X.509 certificate.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[],System.Security.SecureString,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> class using a byte array, a password, and a key storage flag.</summary>
      <param name="rawData">A byte array that contains data from an X.509 certificate.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control where and how to import the certificate.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[],System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> class using a byte array and a password.</summary>
      <param name="rawData">A byte array containing data from an X.509 certificate.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> class using a byte array, a password, and a key storage flag.</summary>
      <param name="rawData">A byte array containing data from an X.509 certificate.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control where and how to import the certificate.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.IntPtr)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> class using an unmanaged handle.</summary>
      <param name="handle">A pointer to a certificate context in unmanaged code. The C structure is called <see langword="PCCERT_CONTEXT" />.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.ReadOnlySpan{System.Byte})">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> class from certificate data.</summary>
      <param name="rawData">The certificate data to process.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Char},System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> class from certificate data, a password, and key storage flags.</summary>
      <param name="rawData">The certificate data to process.</param>
      <param name="password">The password required to access the certificate data.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control where and how to import the certificate.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> class using the specified serialization and stream context information.</summary>
      <param name="info">The serialization information required to deserialize the new <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" />.</param>
      <param name="context">Contextual information about the source of the stream to be deserialized.</param>
      <exception cref="T:System.PlatformNotSupportedException">.NET Core and .NET 5+ only: In all cases.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> class using an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object.</summary>
      <param name="certificate">An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> object.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> class using a certificate file name.</summary>
      <param name="fileName">The name of a certificate file.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String,System.ReadOnlySpan{System.Char},System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> class using a certificate file name, a password, and a key storage flag.</summary>
      <param name="fileName">The name of a certificate file.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control where and how to import the certificate.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String,System.Security.SecureString)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> class using a certificate file name and a password.</summary>
      <param name="fileName">The name of a certificate file.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String,System.Security.SecureString,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> class using a certificate file name, a password, and a key storage flag.</summary>
      <param name="fileName">The name of a certificate file.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control where and how to import the certificate.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> class using a certificate file name and a password used to access the certificate.</summary>
      <param name="fileName">The name of a certificate file.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.#ctor(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> class using a certificate file name, a password used to access the certificate, and a key storage flag.</summary>
      <param name="fileName">The name of a certificate file.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control where and how to import the certificate.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">An error with the certificate occurs. For example:  
  
-   The certificate file does not exist.  
  
-   The certificate is invalid.  
  
-   The certificate's password is incorrect.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.CopyWithPrivateKey(System.Security.Cryptography.ECDiffieHellman)">
      <summary>Combines a private key with the public key of an <see cref="T:System.Security.Cryptography.ECDiffieHellman" /> certificate to generate a new ECDiffieHellman certificate.</summary>
      <param name="privateKey">The private ECDiffieHellman key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="privateKey" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">The certificate already has an associated private key.</exception>
      <exception cref="T:System.ArgumentException">The certificate doesn't have a public key.

-or-

The specified private key doesn't match the public key for this certificate.</exception>
      <returns>A new ECDiffieHellman certificate with the <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.HasPrivateKey" /> property set to <see langword="true" />.
            The current certificate isn't modified.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.CreateFromEncryptedPem(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char})">
      <summary>Creates a new X509 certificate from the contents of an RFC 7468 PEM-encoded certificate and password protected private key.</summary>
      <param name="certPem">The text of the PEM-encoded X509 certificate.</param>
      <param name="keyPem">The text of the password protected PEM-encoded private key.</param>
      <param name="password">The password for the encrypted PEM.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
The contents of <paramref name="certPem" /> do not contain a PEM-encoded certificate, or it is malformed.

-or-

The contents of <paramref name="keyPem" /> do not contain a password protected PEM-encoded private key, or it is malformed.

-or-

The contents of <paramref name="keyPem" /> contains a key that does not match the public key in the certificate.

-or-

The certificate uses an unknown public key algorithm.

-or-

The password specified for the private key is incorrect.</exception>
      <returns>A new certificate with the private key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.CreateFromEncryptedPemFile(System.String,System.ReadOnlySpan{System.Char},System.String)">
      <summary>Creates a new X509 certificate from the file contents of an RFC 7468 PEM-encoded certificate and password protected private key.</summary>
      <param name="certPemFilePath">The path for the PEM-encoded X509 certificate.</param>
      <param name="password">The password for the encrypted PEM.</param>
      <param name="keyPemFilePath">If specified, the path for the password protected PEM-encoded private key.
            If unspecified, the <paramref name="certPemFilePath" /> file will be used to load the private key.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
The contents of the file path in <paramref name="certPemFilePath" /> do not contain a PEM-encoded certificate, or it is malformed.

-or-

The contents of the file path in <paramref name="keyPemFilePath" /> do not contain a password protected PEM-encoded private key, or it is malformed.

-or-

The contents of the file path in <paramref name="keyPemFilePath" /> contains a key that does not match the public key in the certificate.

-or-

The certificate uses an unknown public key algorithm.

-or-

The password specified for the private key is incorrect.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certPemFilePath" /> is <see langword="null" />.</exception>
      <returns>A new certificate with the private key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.CreateFromPem(System.ReadOnlySpan{System.Char})">
      <summary>Creates a new X509 certificate from the contents of an RFC 7468 PEM-encoded certificate.</summary>
      <param name="certPem">The text of the PEM-encoded X509 certificate.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The contents of <paramref name="certPem" /> do not contain a PEM-encoded certificate, or it is malformed.</exception>
      <returns>A new X509 certificate.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.CreateFromPem(System.ReadOnlySpan{System.Char},System.ReadOnlySpan{System.Char})">
      <summary>Creates a new X509 certificate from the contents of an RFC 7468 PEM-encoded certificate and private key.</summary>
      <param name="certPem">The text of the PEM-encoded X509 certificate.</param>
      <param name="keyPem">The text of the PEM-encoded private key.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
The contents of <paramref name="certPem" /> do not contain a PEM-encoded certificate, or it is malformed.

-or-

The contents of <paramref name="keyPem" /> do not contain a PEM-encoded private key, or it is malformed.

-or-

The contents of <paramref name="keyPem" /> contains a key that does not match the public key in the certificate.

-or-

The certificate uses an unknown public key algorithm.</exception>
      <returns>A new certificate with the private key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.CreateFromPemFile(System.String,System.String)">
      <summary>Creates a new X509 certificate from the file contents of an RFC 7468 PEM-encoded certificate and private key.</summary>
      <param name="certPemFilePath">The path for the PEM-encoded X509 certificate.</param>
      <param name="keyPemFilePath">If specified, the path for the PEM-encoded private key.
            If unspecified, the <paramref name="certPemFilePath" /> file will be used to load the private key.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
The contents of the file path in <paramref name="certPemFilePath" /> do not contain a PEM-encoded certificate, or it is malformed.

-or-

The contents of the file path in <paramref name="keyPemFilePath" /> do not contain a PEM-encoded private key, or it is malformed.

-or-

The contents of the file path in <paramref name="keyPemFilePath" /> contains a key that does not match the public key in the certificate.

-or-

The certificate uses an unknown public key algorithm.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certPemFilePath" /> is <see langword="null" />.</exception>
      <returns>A new certificate with the private key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetCertContentType(System.Byte[])">
      <summary>Indicates the type of certificate contained in a byte array.</summary>
      <param name="rawData">A byte array containing data from an X.509 certificate.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="rawData" /> has a zero length or is <see langword="null" />.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetCertContentType(System.ReadOnlySpan{System.Byte})">
      <summary>Indicates the type of certificate contained in the provided data.</summary>
      <param name="rawData">The data to identify.</param>
      <returns>One of the enumeration values that indicate the content type of the provided data.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetCertContentType(System.String)">
      <summary>Indicates the type of certificate contained in a file.</summary>
      <param name="fileName">The name of a certificate file.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="fileName" /> is <see langword="null" />.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetECDiffieHellmanPrivateKey">
      <summary>Gets the <see cref="T:System.Security.Cryptography.ECDiffieHellman" /> private key from this certificate.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The handle is invalid.</exception>
      <returns>The private key, or <see langword="null" /> if this certificate does not have an ECDiffieHellman private key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetECDiffieHellmanPublicKey">
      <summary>Gets the <see cref="T:System.Security.Cryptography.ECDiffieHellman" /> public key from this certificate.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The handle is invalid.</exception>
      <returns>The public key, or <see langword="null" /> if this certificate does not have an ECDiffieHellman public key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.GetNameInfo(System.Security.Cryptography.X509Certificates.X509NameType,System.Boolean)">
      <summary>Gets the subject and issuer names from a certificate.</summary>
      <param name="nameType">The <see cref="T:System.Security.Cryptography.X509Certificates.X509NameType" /> value for the subject.</param>
      <param name="forIssuer">
        <see langword="true" /> to include the issuer name; otherwise, <see langword="false" />.</param>
      <returns>The name of the certificate.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.Import(System.Byte[])">
      <summary>Populates an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object with data from a byte array.</summary>
      <param name="rawData">A byte array containing data from an X.509 certificate.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.Import(System.Byte[],System.Security.SecureString,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Populates an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object using data from a byte array, a password, and a key storage flag.</summary>
      <param name="rawData">A byte array that contains data from an X.509 certificate.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control where and how to import the certificate.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.Import(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Populates an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object using data from a byte array, a password, and flags for determining how to import the private key.</summary>
      <param name="rawData">A byte array containing data from an X.509 certificate.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control where and how to import the certificate.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.Import(System.String)">
      <summary>Populates an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object with information from a certificate file.</summary>
      <param name="fileName">The name of a certificate.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.Import(System.String,System.Security.SecureString,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Populates an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object with information from a certificate file, a password, and a key storage flag.</summary>
      <param name="fileName">The name of a certificate file.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control where and how to import the certificate.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.Import(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Populates an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object with information from a certificate file, a password, and a <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags" /> value.</summary>
      <param name="fileName">The name of a certificate file.</param>
      <param name="password">The password required to access the X.509 certificate data.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control where and how to import the certificate.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.Reset">
      <summary>Resets the state of an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.ToString">
      <summary>Displays an X.509 certificate in text format.</summary>
      <returns>The certificate information.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.ToString(System.Boolean)">
      <summary>Displays an X.509 certificate in text format.</summary>
      <param name="verbose">
        <see langword="true" /> to display the public key, private key, extensions, and so forth; <see langword="false" /> to display information that is similar to the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> class, including thumbprint, serial number, subject and issuer names, and so on.</param>
      <returns>The certificate information.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2.Verify">
      <summary>Performs a X.509 chain validation using basic validation policy.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate is unreadable.</exception>
      <returns>
        <see langword="true" /> if the validation succeeds; <see langword="false" /> if the validation fails.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Archived">
      <summary>Gets or sets a value indicating that an X.509 certificate is archived.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate is unreadable.</exception>
      <returns>
        <see langword="true" /> if the certificate is archived, <see langword="false" /> if the certificate is not archived.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Extensions">
      <summary>Gets a collection of <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> objects.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate is unreadable.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.FriendlyName">
      <summary>Gets or sets the associated alias for a certificate.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate is unreadable.</exception>
      <returns>The certificate's friendly name.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.HasPrivateKey">
      <summary>Gets a value that indicates whether an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object contains a private key.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate context is invalid.</exception>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object contains a private key; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.IssuerName">
      <summary>Gets the distinguished name of the certificate issuer.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate context is invalid.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> object that contains the name of the certificate issuer.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.NotAfter">
      <summary>Gets the date in local time after which a certificate is no longer valid.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate is unreadable.</exception>
      <returns>A <see cref="T:System.DateTime" /> object that represents the expiration date for the certificate.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.NotBefore">
      <summary>Gets the date in local time on which a certificate becomes valid.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate is unreadable.</exception>
      <returns>A <see cref="T:System.DateTime" /> object that represents the effective date of the certificate.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PrivateKey">
      <summary>Gets or sets the <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> object that represents the private key associated with a certificate.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key value is not an RSA or DSA key, or the key is unreadable.</exception>
      <exception cref="T:System.ArgumentNullException">The value being set for this property is <see langword="null" />.</exception>
      <exception cref="T:System.NotSupportedException">The key algorithm for this private key is not supported.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicUnexpectedOperationException">The X.509 keys do not match.</exception>
      <exception cref="T:System.ArgumentException">The cryptographic service provider key is <see langword="null" />.</exception>
      <exception cref="T:System.PlatformNotSupportedException">.NET Core and .NET 5+ only: An attempt was made to set this property.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.AsymmetricAlgorithm" /> object, which is either an RSA or DSA cryptographic service provider.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PublicKey">
      <summary>Gets a <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PublicKey" /> object associated with a certificate.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The key value is not an RSA or DSA key, or the key is unreadable.</exception>
      <returns>A <see cref="P:System.Security.Cryptography.X509Certificates.X509Certificate2.PublicKey" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.RawData">
      <summary>Gets the raw data of a certificate.</summary>
      <returns>The raw data of the certificate as a byte array.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.SerialNumber">
      <summary>Gets the serial number of a certificate as a big-endian hexadecimal string.</summary>
      <returns>The serial number of the certificate as a big-endian hexadecimal string.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.SignatureAlgorithm">
      <summary>Gets the algorithm used to create the signature of a certificate.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate is unreadable.</exception>
      <returns>The object identifier of the signature algorithm.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.SubjectName">
      <summary>Gets the subject distinguished name from a certificate.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate context is invalid.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X500DistinguishedName" /> object that represents the name of the certificate subject.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Thumbprint">
      <summary>Gets the thumbprint of a certificate.</summary>
      <returns>The thumbprint of the certificate.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2.Version">
      <summary>Gets the X.509 format version of a certificate.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate is unreadable.</exception>
      <returns>The certificate format.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection">
      <summary>Represents a collection of <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> objects. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> class without any <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> information.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> class using an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object.</summary>
      <param name="certificate">An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object to start the collection from.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> class using an array of <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> objects.</summary>
      <param name="certificates">An array of <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> objects.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> class using the specified certificate collection.</summary>
      <param name="certificates">An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Add(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Adds an object to the end of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" />.</summary>
      <param name="certificate">An X.509 certificate represented as an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> is <see langword="null" />.</exception>
      <returns>The <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> index at which the <paramref name="certificate" /> has been added.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.AddRange(System.Security.Cryptography.X509Certificates.X509Certificate2[])">
      <summary>Adds multiple <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> objects in an array to the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object.</summary>
      <param name="certificates">An array of <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> objects.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.AddRange(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>Adds multiple <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> objects in an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object to another <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object.</summary>
      <param name="certificates">An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Contains(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Determines whether the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object contains a specific certificate.</summary>
      <param name="certificate">The <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object to locate in the collection.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> is <see langword="null" />.</exception>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> contains the specified <paramref name="certificate" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Export(System.Security.Cryptography.X509Certificates.X509ContentType)">
      <summary>Exports X.509 certificate information into a byte array.</summary>
      <param name="contentType">A supported <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> object.</param>
      <returns>X.509 certificate information in a byte array.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Export(System.Security.Cryptography.X509Certificates.X509ContentType,System.String)">
      <summary>Exports X.509 certificate information into a byte array using a password.</summary>
      <param name="contentType">A supported <see cref="T:System.Security.Cryptography.X509Certificates.X509ContentType" /> object.</param>
      <param name="password">A string used to protect the byte array.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate is unreadable, the content is invalid or, in the case of a certificate requiring a password, the private key could not be exported because the password provided was incorrect.</exception>
      <returns>X.509 certificate information in a byte array.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)">
      <summary>Searches an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object using the search criteria specified by the <see cref="T:System.Security.Cryptography.X509Certificates.X509FindType" /> enumeration and the <paramref name="findValue" /> object.</summary>
      <param name="findType">One of the <see cref="T:System.Security.Cryptography.X509Certificates.X509FindType" /> values.</param>
      <param name="findValue">The search criteria as an object.</param>
      <param name="validOnly">
        <see langword="true" /> to allow only valid certificates to be returned from the search; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">
        <paramref name="findType" /> is invalid.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.GetEnumerator">
      <summary>Returns an enumerator that can iterate through a <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator" /> object that can iterate through the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.Byte[])">
      <summary>Imports a certificate in the form of a byte array into a <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object.</summary>
      <param name="rawData">A byte array containing data from an X.509 certificate.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.Byte[],System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Imports a certificate, in the form of a byte array that requires a password to access the certificate, into a <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object.</summary>
      <param name="rawData">A byte array containing data from an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object.</param>
      <param name="password">The password required to access the certificate information.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control how and where the certificate is imported.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.ReadOnlySpan{System.Byte})">
      <summary>Imports the certificates from the provided data into this collection.</summary>
      <param name="rawData">The certificate data to read.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.ReadOnlySpan{System.Byte},System.ReadOnlySpan{System.Char},System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Imports the certificates from the provided data into this collection.</summary>
      <param name="rawData">The certificate data to read.</param>
      <param name="password">The password required to access the certificate data.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control where and how to import the certificate.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.ReadOnlySpan{System.Byte},System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Imports the certificates from the provided data into this collection.</summary>
      <param name="rawData">The certificate data to read.</param>
      <param name="password">The password required to access the certificate data.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control where and how to import the certificate.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.String)">
      <summary>Imports a certificate file into a <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object.</summary>
      <param name="fileName">The name of the file containing the certificate information.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.String,System.ReadOnlySpan{System.Char},System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Imports the certificates from the specified file a into this collection.</summary>
      <param name="fileName">The name of the file containing the certificate information.</param>
      <param name="password">The password required to access the certificate data.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control where and how to import the certificate.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Import(System.String,System.String,System.Security.Cryptography.X509Certificates.X509KeyStorageFlags)">
      <summary>Imports a certificate file that requires a password into a <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object.</summary>
      <param name="fileName">The name of the file containing the certificate information.</param>
      <param name="password">The password required to access the certificate information.</param>
      <param name="keyStorageFlags">A bitwise combination of the enumeration values that control how and where the certificate is imported.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.ImportFromPem(System.ReadOnlySpan{System.Char})">
      <summary>Imports a collection of RFC 7468 PEM-encoded certificates.</summary>
      <param name="certPem">The text of the PEM-encoded X509 certificate collection.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The decoded contents of a PEM are invalid or corrupt and could not be imported.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.ImportFromPemFile(System.String)">
      <summary>Imports a collection of RFC 7468 PEM-encoded certificates.</summary>
      <param name="certPemFilePath">The path for the PEM-encoded X509 certificate collection.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The decoded contents of a PEM are invalid or corrupt and could not be imported.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certPemFilePath" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Insert(System.Int32,System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Inserts an object into the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object at the specified index.</summary>
      <param name="index">The zero-based index at which to insert <paramref name="certificate" />.</param>
      <param name="certificate">The <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object to insert.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.  
  
 -or-  
  
 <paramref name="index" /> is greater than the <see cref="P:System.Collections.CollectionBase.Count" /> property.</exception>
      <exception cref="T:System.NotSupportedException">The collection is read-only.  
  
 -or-  
  
 The collection has a fixed size.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Remove(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Removes the first occurrence of a certificate from the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object.</summary>
      <param name="certificate">The <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object to be removed from the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.RemoveRange(System.Security.Cryptography.X509Certificates.X509Certificate2[])">
      <summary>Removes multiple <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> objects in an array from an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object.</summary>
      <param name="certificates">An array of <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> objects.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.RemoveRange(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>Removes multiple <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> objects in an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object from another <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object.</summary>
      <param name="certificates">An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.System#Collections#Generic#IEnumerable{System#Security#Cryptography#X509Certificates#X509Certificate2}#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Item(System.Int32)">
      <summary>Gets or sets the element at the specified index.</summary>
      <param name="index">The zero-based index of the element to get or set.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.  
  
 -or-  
  
 <paramref name="index" /> is equal to or greater than the <see cref="P:System.Collections.CollectionBase.Count" /> property.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="index" /> is <see langword="null" />.</exception>
      <returns>The element at the specified index.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator">
      <summary>Supports a simple iteration over a <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.MoveNext">
      <summary>Advances the enumerator to the next element in the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created.</exception>
      <returns>
        <see langword="true" /> if the enumerator was successfully advanced to the next element; <see langword="false" /> if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.System#Collections#IEnumerator#MoveNext">
      <summary>For a description of this member, see <see cref="M:System.Collections.IEnumerator.MoveNext" />.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created.</exception>
      <returns>
        <see langword="true" /> if the enumerator was successfully advanced to the next element; <see langword="false" /> if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.System#Collections#IEnumerator#Reset">
      <summary>For a description of this member, see <see cref="M:System.Collections.IEnumerator.Reset" />.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.System#IDisposable#Dispose">
      <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.Current">
      <summary>Gets the current element in the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object.</summary>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element.</exception>
      <returns>The current element in the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Certificate2Enumerator.System#Collections#IEnumerator#Current">
      <summary>For a description of this member, see <see cref="P:System.Collections.IEnumerator.Current" />.</summary>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element.</exception>
      <returns>The current element in the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection">
      <summary>Defines a collection that stores <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> objects.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.#ctor(System.Security.Cryptography.X509Certificates.X509Certificate[])">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> class from an array of <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> objects.</summary>
      <param name="value">The array of <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> objects with which to initialize the new object.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.#ctor(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> class from another <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <param name="value">The <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> with which to initialize the new object.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Add(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Adds an <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> with the specified value to the current <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <param name="value">The <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> to add to the current <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</param>
      <returns>The index into the current <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> at which the new <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> was inserted.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.AddRange(System.Security.Cryptography.X509Certificates.X509Certificate[])">
      <summary>Copies the elements of an array of type <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> to the end of the current <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <param name="value">The array of type <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> containing the objects to add to the current <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.AddRange(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
      <summary>Copies the elements of the specified <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> to the end of the current <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <param name="value">The <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> containing the objects to add to the collection.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="value" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Contains(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Gets a value indicating whether the current <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> contains the specified <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" />.</summary>
      <param name="value">The <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> to locate.</param>
      <returns>
        <see langword="true" /> if the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> is contained in this collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.CopyTo(System.Security.Cryptography.X509Certificates.X509Certificate[],System.Int32)">
      <summary>Copies the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> values in the current <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> to a one-dimensional <see cref="T:System.Array" /> instance at the specified index.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the values copied from <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</param>
      <param name="index">The index into <paramref name="array" /> to begin copying.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="array" /> parameter is multidimensional.  
  
 -or-  
  
 The number of elements in the <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> is greater than the available space between <paramref name="arrayIndex" /> and the end of <paramref name="array" />.</exception>
      <exception cref="T:System.ArgumentNullException">The <paramref name="array" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="arrayIndex" /> parameter is less than the <paramref name="array" /> parameter's lower bound.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.GetEnumerator">
      <summary>Returns an enumerator that can iterate through the <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <returns>An enumerator of the subelements of <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> you can use to iterate through the collection.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.GetHashCode">
      <summary>Builds a hash value based on all values contained in the current <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <returns>A hash value based on all values contained in the current <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.IndexOf(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Returns the index of the specified <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> in the current <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <param name="value">The <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> to locate.</param>
      <returns>The index of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> specified by the <paramref name="value" /> parameter in the <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />, if found; otherwise, -1.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Insert(System.Int32,System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Inserts a <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> into the current <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> at the specified index.</summary>
      <param name="index">The zero-based index where <paramref name="value" /> should be inserted.</param>
      <param name="value">The <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> to insert.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.OnValidate(System.Object)">
      <summary>Performs additional custom processes when validating a value.</summary>
      <param name="value">The object to validate.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Remove(System.Security.Cryptography.X509Certificates.X509Certificate)">
      <summary>Removes a specific <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> from the current <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <param name="value">The <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> to remove from the current <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</param>
      <exception cref="T:System.ArgumentException">The <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> specified by the <paramref name="value" /> parameter is not found in the current <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.Item(System.Int32)">
      <summary>Gets or sets the entry at the specified index of the current <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <param name="index">The zero-based index of the entry to locate in the current <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The <paramref name="index" /> parameter is outside the valid range of indexes for the collection.</exception>
      <returns>The <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> at the specified index of the current <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator">
      <summary>Enumerates the <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> objects in an <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.#ctor(System.Security.Cryptography.X509Certificates.X509CertificateCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator" /> class for the specified <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <param name="mappings">The <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> to enumerate.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.MoveNext">
      <summary>Advances the enumerator to the next element of the collection.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was instantiated.</exception>
      <returns>
        <see langword="true" /> if the enumerator was successfully advanced to the next element; <see langword="false" /> if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="T:System.InvalidOperationException">The collection is modified after the enumerator is instantiated.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.System#Collections#IEnumerator#MoveNext">
      <summary>For a description of this member, see <see cref="M:System.Collections.IEnumerator.MoveNext" />.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was instantiated.</exception>
      <returns>
        <see langword="true" /> if the enumerator was successfully advanced to the next element; <see langword="false" /> if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.System#Collections#IEnumerator#Reset">
      <summary>For a description of this member, see <see cref="M:System.Collections.IEnumerator.Reset" />.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was instantiated.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.Current">
      <summary>Gets the current <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> in the <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</summary>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element.</exception>
      <returns>The current <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate" /> in the <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509CertificateCollection.X509CertificateEnumerator.System#Collections#IEnumerator#Current">
      <summary>For a description of this member, see <see cref="P:System.Collections.IEnumerator.Current" />.</summary>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element.</exception>
      <returns>The current X.509 certificate object in the <see cref="T:System.Security.Cryptography.X509Certificates.X509CertificateCollection" /> object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Chain">
      <summary>Represents a chain-building engine for <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> certificates.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> class specifying a value that indicates whether the machine context should be used.</summary>
      <param name="useMachineContext">
        <see langword="true" /> to use the machine context; <see langword="false" /> to use the current user context.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.#ctor(System.IntPtr)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> class using an <see cref="T:System.IntPtr" /> handle to an X.509 chain.</summary>
      <param name="chainContext">An <see cref="T:System.IntPtr" /> handle to an X.509 chain.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="chainContext" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="chainContext" /> parameter points to an invalid context.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Build(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Builds an X.509 chain using the policy specified in <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" />.</summary>
      <param name="certificate">An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="certificate" /> is not a valid certificate or is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="certificate" /> is unreadable.</exception>
      <returns>
        <see langword="true" /> if the X.509 certificate is valid; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Create">
      <summary>Creates an <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> object after querying for the mapping defined in the CryptoConfig file, and maps the chain to that mapping.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Dispose">
      <summary>Releases all of the resources used by this <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by this <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" />, and optionally releases the managed resources.</summary>
      <param name="disposing">
        <see langword="true" /> to release both managed and unmanaged resources; <see langword="false" /> to release only unmanaged resources.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Chain.Reset">
      <summary>Clears the current <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> object.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainContext">
      <summary>Gets a handle to an X.509 chain.</summary>
      <returns>An <see cref="T:System.IntPtr" /> handle to an X.509 chain.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainElements">
      <summary>Gets a collection of <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> objects.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainPolicy">
      <summary>Gets or sets the <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" /> to use when building an X.509 certificate chain.</summary>
      <exception cref="T:System.ArgumentNullException">The value being set for this property is <see langword="null" />.</exception>
      <returns>The <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" /> object associated with this X.509 chain.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainStatus">
      <summary>Gets the status of each element in an <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> object.</summary>
      <returns>An array of <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainStatus" /> objects.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Chain.SafeHandle">
      <summary>Gets a safe handle for this <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> instance.</summary>
      <returns>The safe handle for this <see cref="T:System.Security.Cryptography.X509Certificates.X509Chain" /> instance.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainElement">
      <summary>Represents an element of an X.509 chain.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElement.Certificate">
      <summary>Gets the X.509 certificate at a particular chain element.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElement.ChainElementStatus">
      <summary>Gets the error status of the current X.509 certificate in a chain.</summary>
      <returns>An array of <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainStatus" /> objects.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElement.Information">
      <summary>Gets additional error information from an unmanaged certificate chain structure.</summary>
      <returns>A string representing the <see langword="pwszExtendedErrorInfo" /> member of the unmanaged <see langword="CERT_CHAIN_ELEMENT" /> structure in the Crypto API.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection">
      <summary>Represents a collection of <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> objects. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.CopyTo(System.Security.Cryptography.X509Certificates.X509ChainElement[],System.Int32)">
      <summary>Copies an <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> object into an array, starting at the specified index.</summary>
      <param name="array">An array of <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> objects.</param>
      <param name="index">An integer representing the index value.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The specified <paramref name="index" /> is less than zero, or greater than or equal to the length of the array.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> plus the current count is greater than the length of the array.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.GetEnumerator">
      <summary>Gets an <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator" /> object that can be used to navigate through a collection of chain elements.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.System#Collections#Generic#IEnumerable{System#Security#Cryptography#X509Certificates#X509ChainElement}#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies an <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> object into an array, starting at the specified index.</summary>
      <param name="array">An array to copy the <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> object to.</param>
      <param name="index">The index of <paramref name="array" /> at which to start copying.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The specified <paramref name="index" /> is less than zero, or greater than or equal to the length of the array.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> plus the current count is greater than the length of the array.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gets an <see cref="T:System.Collections.IEnumerator" /> object that can be used to navigate a collection of chain elements.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.Count">
      <summary>Gets the number of elements in the collection.</summary>
      <returns>An integer representing the number of elements in the collection.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.IsSynchronized">
      <summary>Gets a value indicating whether the collection of chain elements is synchronized.</summary>
      <returns>Always returns <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.Item(System.Int32)">
      <summary>Gets the <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> object at the specified index.</summary>
      <param name="index">An integer value.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is greater than or equal to the length of the collection.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElement" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementCollection.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to an <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" /> object.</summary>
      <returns>A pointer reference to the current object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator">
      <summary>Supports a simple iteration over an <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.MoveNext">
      <summary>Advances the enumerator to the next element in the <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created.</exception>
      <returns>
        <see langword="true" /> if the enumerator was successfully advanced to the next element; <see langword="false" /> if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.System#IDisposable#Dispose">
      <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.Current">
      <summary>Gets the current element in the <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />.</summary>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element.</exception>
      <returns>The current element in the <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainElementEnumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the current element in the <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />.</summary>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element.</exception>
      <returns>The current element in the <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainElementCollection" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy">
      <summary>Represents the chain policy to be applied when building an X509 certificate chain. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainPolicy.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ChainPolicy.Reset">
      <summary>Resets the <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainPolicy" /> members to their default values.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.ApplicationPolicy">
      <summary>Gets a collection of object identifiers (OIDs) specifying which application policies or enhanced key usages (EKUs) the certificate must support.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.OidCollection" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.CertificatePolicy">
      <summary>Gets a collection of object identifiers (OIDs) specifying which certificate policies the certificate must support.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.OidCollection" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.CustomTrustStore">
      <summary>Represents a collection of certificates replacing the default certificate trust.</summary>
      <returns>A collection of certificates replacing the default certificate trust.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.DisableCertificateDownloads">
      <summary>Gets or sets a value that indicates whether the chain engine can use the Authority Information Access (AIA) extension to locate unknown issuer certificates.</summary>
      <returns>
        <see langword="true" /> if using the AIA extension is disabled; otherwise, <see langword="false" />.
 The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.ExtraStore">
      <summary>Gets an object that represents an additional collection of certificates that can be searched by the chaining engine when validating a certificate chain.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509Certificate2Collection" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.RevocationFlag">
      <summary>Gets or sets values for X509 revocation flags.</summary>
      <exception cref="T:System.ArgumentException">The <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationFlag" /> value supplied is not a valid flag.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationFlag" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.RevocationMode">
      <summary>Gets or sets values for X509 certificate revocation mode.</summary>
      <exception cref="T:System.ArgumentException">The <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationMode" /> value supplied is not a valid flag.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509RevocationMode" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.TrustMode">
      <summary>The mode determining the root trust for building the certificate chain.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainTrustMode" /> value determining the root trust.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.UrlRetrievalTimeout">
      <summary>Gets or sets the maximum amount of time to be spent during online revocation verification or downloading the certificate revocation list (CRL). A value of <see cref="F:System.TimeSpan.Zero" /> means there are no limits.</summary>
      <returns>A <see cref="T:System.TimeSpan" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.VerificationFlags">
      <summary>Gets verification flags for the certificate.</summary>
      <exception cref="T:System.ArgumentException">The <see cref="T:System.Security.Cryptography.X509Certificates.X509VerificationFlags" /> value supplied is not a valid flag. <see cref="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.NoFlag" /> is the default value.</exception>
      <returns>A value from the <see cref="T:System.Security.Cryptography.X509Certificates.X509VerificationFlags" /> enumeration.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.VerificationTime">
      <summary>Gets or sets the time for which the chain is to be validated.</summary>
      <returns>A <see cref="T:System.DateTime" /> object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainStatus">
      <summary>Provides a simple structure for storing X509 chain status and error information.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainStatus.Status">
      <summary>Specifies the status of the X509 chain.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags" /> value.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ChainStatus.StatusInformation">
      <summary>Specifies a description of the <see cref="P:System.Security.Cryptography.X509Certificates.X509Chain.ChainStatus" /> value.</summary>
      <returns>A localizable string.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags">
      <summary>Defines the status of an X509 chain.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.CtlNotSignatureValid">
      <summary>Specifies that the certificate trust list (CTL) contains an invalid signature.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.CtlNotTimeValid">
      <summary>Specifies that the certificate trust list (CTL) is not valid because of an invalid time value, such as one that indicates that the CTL has expired.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.CtlNotValidForUsage">
      <summary>Specifies that the certificate trust list (CTL) is not valid for this use.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.Cyclic">
      <summary>Specifies that the X509 chain could not be built.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.ExplicitDistrust">
      <summary>Specifies that the certificate is explicitly distrusted.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasExcludedNameConstraint">
      <summary>Specifies that the X509 chain is invalid because a certificate has excluded a name constraint.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasNotDefinedNameConstraint">
      <summary>Specifies that the certificate has an undefined name constraint.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasNotPermittedNameConstraint">
      <summary>Specifies that the certificate has an impermissible name constraint.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasNotSupportedCriticalExtension">
      <summary>Specifies that the certificate does not support a critical extension.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasNotSupportedNameConstraint">
      <summary>Specifies that the certificate does not have a supported name constraint or has a name constraint that is unsupported.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.HasWeakSignature">
      <summary>Specifies that the certificate has not been strong signed. Typically, this indicates that the MD2 or MD5 hashing algorithms were used to create a hash of the certificate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidBasicConstraints">
      <summary>Specifies that the X509 chain is invalid due to invalid basic constraints.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidExtension">
      <summary>Specifies that the X509 chain is invalid due to an invalid extension.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidNameConstraints">
      <summary>Specifies that the X509 chain is invalid due to invalid name constraints.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.InvalidPolicyConstraints">
      <summary>Specifies that the X509 chain is invalid due to invalid policy constraints.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NoError">
      <summary>Specifies that the X509 chain has no errors.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NoIssuanceChainPolicy">
      <summary>Specifies that there is no certificate policy extension in the certificate. This error would occur if a group policy has specified that all certificates must have a certificate policy.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotSignatureValid">
      <summary>Specifies that the X509 chain is invalid due to an invalid certificate signature.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotTimeNested">
      <summary>Deprecated. Specifies that the CA (certificate authority) certificate and the issued certificate have validity periods that are not nested. For example, the CA cert can be valid from January 1 to December 1 and the issued certificate from January 2 to December 2, which would mean the validity periods are not nested.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotTimeValid">
      <summary>Specifies that the X509 chain is not valid due to an invalid time value, such as a value that indicates an expired certificate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.NotValidForUsage">
      <summary>Specifies that the key usage is not valid.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.OfflineRevocation">
      <summary>Specifies that the online certificate revocation list (CRL) the X509 chain relies on is currently offline.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.PartialChain">
      <summary>Specifies that the X509 chain could not be built up to the root certificate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.RevocationStatusUnknown">
      <summary>Specifies that it is not possible to determine whether the certificate has been revoked. This can be due to the certificate revocation list (CRL) being offline or unavailable.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.Revoked">
      <summary>Specifies that the X509 chain is invalid due to a revoked certificate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainStatusFlags.UntrustedRoot">
      <summary>Specifies that the X509 chain is invalid due to an untrusted root certificate.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ChainTrustMode">
      <summary>The mode determining the root trust for building the certificate chain.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainTrustMode.CustomRootTrust">
      <summary>When this value is used, the <see cref="P:System.Security.Cryptography.X509Certificates.X509ChainPolicy.CustomTrustStore" /> will be used instead of the default root trust.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ChainTrustMode.System">
      <summary>Use the default (system) root trust.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ContentType">
      <summary>Specifies the format of an X.509 certificate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Authenticode">
      <summary>An Authenticode X.509 certificate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Cert">
      <summary>A single X.509 certificate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pfx">
      <summary>A PFX-formatted certificate. The <see langword="Pfx" /> value is identical to the <see langword="Pkcs12" /> value.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs12">
      <summary>A PKCS #12-formatted certificate. The <see langword="Pkcs12" /> value is identical to the <see langword="Pfx" /> value.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Pkcs7">
      <summary>A PKCS #7-formatted certificate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedCert">
      <summary>A single serialized X.509 certificate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.SerializedStore">
      <summary>A serialized store.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509ContentType.Unknown">
      <summary>An unknown X.509 certificate.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension">
      <summary>Defines the collection of object identifiers (OIDs) that indicates the applications that use the key. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> class using an <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object and a value that identifies whether the extension is critical.</summary>
      <param name="encodedEnhancedKeyUsages">The encoded data to use to create the extension.</param>
      <param name="critical">
        <see langword="true" /> if the extension is critical; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.#ctor(System.Security.Cryptography.OidCollection,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> class using an <see cref="T:System.Security.Cryptography.OidCollection" /> and a value that identifies whether the extension is critical.</summary>
      <param name="enhancedKeyUsages">An <see cref="T:System.Security.Cryptography.OidCollection" /> collection.</param>
      <param name="critical">
        <see langword="true" /> if the extension is critical; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The specified <see cref="T:System.Security.Cryptography.OidCollection" /> contains one or more corrupt values.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension" /> class using an <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object.</summary>
      <param name="asnEncodedData">The encoded data to use to create the extension.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509EnhancedKeyUsageExtension.EnhancedKeyUsages">
      <summary>Gets the collection of object identifiers (OIDs) that indicate the applications that use the key.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.OidCollection" /> object indicating the applications that use the key.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Extension">
      <summary>Represents an X509 extension.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> class.</summary>
      <param name="encodedExtension">The encoded data to be used to create the extension.</param>
      <param name="critical">
        <see langword="true" /> if the extension is critical; otherwise <see langword="false" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.Security.Cryptography.Oid,System.Byte[],System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> class.</summary>
      <param name="oid">The object identifier used to identify the extension.</param>
      <param name="rawData">The encoded data used to create the extension.</param>
      <param name="critical">
        <see langword="true" /> if the extension is critical; otherwise <see langword="false" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="oid" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="oid" /> is an empty string ("").</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.Security.Cryptography.Oid,System.ReadOnlySpan{System.Byte},System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> class.</summary>
      <param name="oid">The object identifier used to identify the extension.</param>
      <param name="rawData">The encoded data used to create the extension.</param>
      <param name="critical">
        <see langword="true" /> if the extension is critical; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.String,System.Byte[],System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> class.</summary>
      <param name="oid">A string representing the object identifier.</param>
      <param name="rawData">The encoded data used to create the extension.</param>
      <param name="critical">
        <see langword="true" /> if the extension is critical; otherwise <see langword="false" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.#ctor(System.String,System.ReadOnlySpan{System.Byte},System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> class.</summary>
      <param name="oid">The object identifier used to identify the extension.</param>
      <param name="rawData">The encoded data used to create the extension.</param>
      <param name="critical">
        <see langword="true" /> if the extension is critical; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Extension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Copies the extension properties of the specified <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object.</summary>
      <param name="asnEncodedData">The <see cref="T:System.Security.Cryptography.AsnEncodedData" /> to be copied.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asnEncodedData" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asnEncodedData" /> does not have a valid X.509 extension.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Extension.Critical">
      <summary>Gets a Boolean value indicating whether the extension is critical.</summary>
      <returns>
        <see langword="true" /> if the extension is critical; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection">
      <summary>Represents a collection of <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> objects. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Add(System.Security.Cryptography.X509Certificates.X509Extension)">
      <summary>Adds an <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> object to an <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> object.</summary>
      <param name="extension">An <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> object to add to the <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> object.</param>
      <exception cref="T:System.ArgumentNullException">The value of the <paramref name="extension" /> parameter is <see langword="null" />.</exception>
      <returns>The index at which the <paramref name="extension" /> parameter was added.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.CopyTo(System.Security.Cryptography.X509Certificates.X509Extension[],System.Int32)">
      <summary>Copies a collection into an array starting at the specified index.</summary>
      <param name="array">An array of <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> objects.</param>
      <param name="index">The location in the array at which copying starts.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> is a zero-length string or contains an invalid value.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="index" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> specifies a value that is not in the range of the array.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.GetEnumerator">
      <summary>Returns an enumerator that can iterate through an <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> object.</summary>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator" /> object to use to iterate through the <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> object.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.System#Collections#Generic#IEnumerable{System#Security#Cryptography#X509Certificates#X509Extension}#GetEnumerator">
      <summary>Returns an enumerator that iterates through the collection.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the collection into an array starting at the specified index.</summary>
      <param name="array">An array of <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> objects.</param>
      <param name="index">The location in the array at which copying starts.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> is a zero-length string or contains an invalid value.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="index" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> specifies a value that is not in the range of the array.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that can iterate through an <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> object.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object to use to iterate through the <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Count">
      <summary>Gets the number of <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> objects in a <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> object.</summary>
      <returns>An integer representing the number of <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> objects in the <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.IsSynchronized">
      <summary>Gets a value indicating whether the collection is guaranteed to be thread safe.</summary>
      <returns>
        <see langword="true" /> if the collection is thread safe; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Item(System.Int32)">
      <summary>Gets the <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> object at the specified index.</summary>
      <param name="index">The location of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> object to retrieve.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is equal to or greater than the length of the array.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.Item(System.String)">
      <summary>Gets the first <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> object whose value or friendly name is specified by an object identifier (OID).</summary>
      <param name="oid">The object identifier (OID) of the extension to retrieve.</param>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509Extension" /> object.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionCollection.SyncRoot">
      <summary>Gets an object that you can use to synchronize access to the <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> object.</summary>
      <returns>An object that you can use to synchronize access to the <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" /> object.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator">
      <summary>Supports a simple iteration over a <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.MoveNext">
      <summary>Advances the enumerator to the next element in the <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created.</exception>
      <returns>
        <see langword="true" /> if the enumerator was successfully advanced to the next element; <see langword="false" /> if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</summary>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.System#IDisposable#Dispose">
      <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.Current">
      <summary>Gets the current element in the <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</summary>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element.</exception>
      <returns>The current element in the <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509ExtensionEnumerator.System#Collections#IEnumerator#Current">
      <summary>Gets an object from a collection.</summary>
      <exception cref="T:System.InvalidOperationException">The enumerator is positioned before the first element of the collection or after the last element.</exception>
      <returns>The current element in the <see cref="T:System.Security.Cryptography.X509Certificates.X509ExtensionCollection" />.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509FindType">
      <summary>Specifies the type of value the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method searches for.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByApplicationPolicy">
      <summary>The <paramref name="findValue" /> parameter for the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method must be a string representing either the application policy friendly name or the object identifier (OID, or <see cref="T:System.Security.Cryptography.Oid" />) of the certificate. For example, "Encrypting File System" or "1.3.6.1.4.1.311.10.3.4" can be used. For an application that will be localized, the OID value must be used, because the friendly name is localized.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByCertificatePolicy">
      <summary>The <paramref name="findValue" /> parameter for the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method must be a string representing either the friendly name or the object identifier (OID, or <see cref="T:System.Security.Cryptography.Oid" />) of the certificate policy. The best practice is to use the OID, such as "1.3.6.1.4.1.311.10.3.4". For an application that will be localized, the OID must be used, because the friendly name is localized.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByExtension">
      <summary>The <paramref name="findValue" /> parameter for the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method must be a string describing the extension to find. The object identifier (OID) is most commonly used to direct the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method to search for all certificates that have an extension matching that OID value.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerDistinguishedName">
      <summary>The <paramref name="findValue" /> parameter for the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method must be a string representing the issuer distinguished name of the certificate. This is a more specific search than that provided by the <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerName" /> enumeration value. Using the <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerDistinguishedName" /> value, the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method performs a case-insensitive string comparison for the entire distinguished name. Searching by issuer name is a less precise search.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerName">
      <summary>The <paramref name="findValue" /> parameter for the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method must be a string representing the issuer name of the certificate. This is a less specific search than that provided by the <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerDistinguishedName" /> enumeration value. Using the <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByIssuerName" /> value, the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method performs a case-insensitive string comparison using the supplied value. For example, if you pass "MyCA" to the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method, it will find all certificates with the issuer name containing that string, regardless of other issuer values.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByKeyUsage">
      <summary>The <paramref name="findValue" /> parameter for the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method must be either a string representing the key usage or an integer representing a bit mask containing all the requested key usages. For the string value, only one key usage at a time can be specified, but the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method can be used in a cascading sequence to get the intersection of the requested usages. For example, the <paramref name="findValue" /> parameter can be set to "KeyEncipherment" or an integer (0x30 indicates "KeyEncipherment" and "DataEncipherment"). Values of the <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags" /> enumeration can also be used.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySerialNumber">
      <summary>The <paramref name="findValue" /> parameter for the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method must be a string that represents the serial number of the certificate as displayed by the certificate dialog box, but without the spaces, or as returned by the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate.GetSerialNumberString" /> method.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectDistinguishedName">
      <summary>The <paramref name="findValue" /> parameter for the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method must be a string representing the subject distinguished name of the certificate. This is a more specific search than that provided by the <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectName" /> enumeration value. Using the <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectDistinguishedName" /> value, the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method performs a case-insensitive string comparison for the entire distinguished name. Searching by subject name is a less precise search.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectKeyIdentifier">
      <summary>The <paramref name="findValue" /> parameter for the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method must be a string representing the subject key identifier in hexadecimal, such as "F3E815D45E83B8477B9284113C64EF208E897112", as displayed in the UI.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectName">
      <summary>The <paramref name="findValue" /> parameter for the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method must be a string representing the subject name of the certificate. This is a less specific search than that provided by the <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectDistinguishedName" /> enumeration value. Using the <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindBySubjectName" /> value, the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method performs a case-insensitive string comparison using the supplied value. For example, if you pass "MyCert" to the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method, it will find all certificates with the subject name containing that string, regardless of other subject values. Searching by distinguished name is a more precise search.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTemplateName">
      <summary>The <paramref name="findValue" /> parameter for the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method must be a string representing the template name of the certificate, such as "ClientAuth". A template name is an X509 version 3 extension that specifies the uses of the certificate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByThumbprint">
      <summary>The <paramref name="findValue" /> parameter for the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method must be a string representing the thumbprint of the certificate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeExpired">
      <summary>The <paramref name="findValue" /> parameter for the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method must be a <see cref="T:System.DateTime" /> value in local time. For example, you can find all the certificates that will be valid until the end of the year by eliminating the results of a <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> operation for <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeExpired" /> of the last day of the year from the results of a <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> operation for <see cref="P:System.DateTime.Now" />.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeNotYetValid">
      <summary>The <paramref name="findValue" /> parameter for the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method must be a <see cref="T:System.DateTime" /> value in local time. The value does not have to be in the future. For example, you can use <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeNotYetValid" /> to find certificates that became valid in the current year by taking the intersection of the results of a <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> operation for <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeNotYetValid" /> for the last day of last year with the results of a <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> operation for <see cref="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeValid" /> of <see cref="P:System.DateTime.Now" />.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509FindType.FindByTimeValid">
      <summary>The <paramref name="findValue" /> parameter for the <see cref="M:System.Security.Cryptography.X509Certificates.X509Certificate2Collection.Find(System.Security.Cryptography.X509Certificates.X509FindType,System.Object,System.Boolean)" /> method must be a <see cref="T:System.DateTime" /> value in local time. You can use <see cref="P:System.DateTime.Now" /> to find all the currently valid certificates.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509IncludeOption">
      <summary>Specifies how much of the X.509 certificate chain should be included in the X.509 data.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509IncludeOption.EndCertOnly">
      <summary>Only the end certificate is included in the X.509 chain information.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509IncludeOption.ExcludeRoot">
      <summary>The entire X.509 chain is included except for the root certificate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509IncludeOption.None">
      <summary>No X.509 chain information is included.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509IncludeOption.WholeChain">
      <summary>The entire X.509 chain is included.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags">
      <summary>Defines where and how to import the private key of an X.509 certificate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.DefaultKeySet">
      <summary>The default key set is used.  The user key set is usually the default.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.EphemeralKeySet">
      <summary>The key associated with a PFX file is created in memory and not persisted on disk when importing a certificate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.Exportable">
      <summary>Imported keys are marked as exportable.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.MachineKeySet">
      <summary>Private keys are stored in the local computer store rather than the current user store.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.PersistKeySet">
      <summary>The key associated with a PFX file is persisted when importing a certificate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.UserKeySet">
      <summary>Private keys are stored in the current user store rather than the local computer store. This occurs even if the certificate specifies that the keys should go in the local computer store.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyStorageFlags.UserProtected">
      <summary>Notify the user through a dialog box or other method that the key is accessed.  The Cryptographic Service Provider (CSP) in use defines the precise behavior.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension">
      <summary>Defines the usage of a key contained within an X.509 certificate.  This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> class using an <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object and a value that identifies whether the extension is critical.</summary>
      <param name="encodedKeyUsage">The encoded data to use to create the extension.</param>
      <param name="critical">
        <see langword="true" /> if the extension is critical; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.#ctor(System.Security.Cryptography.X509Certificates.X509KeyUsageFlags,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> class using the specified <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags" /> value and a value that identifies whether the extension is critical.</summary>
      <param name="keyUsages">One of the <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags" /> values that describes how to use the key.</param>
      <param name="critical">
        <see langword="true" /> if the extension is critical; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension" /> class using an <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object.</summary>
      <param name="asnEncodedData">The encoded data to use to create the extension.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.KeyUsages">
      <summary>Gets the key usage flag associated with the certificate.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The extension cannot be decoded.</exception>
      <returns>One of the <see cref="P:System.Security.Cryptography.X509Certificates.X509KeyUsageExtension.KeyUsages" /> values.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags">
      <summary>Defines how the certificate key can be used. If this value is not defined, the key can be used for any purpose.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.CrlSign">
      <summary>The key can be used to sign a certificate revocation list (CRL).</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.DataEncipherment">
      <summary>The key can be used for data encryption.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.DecipherOnly">
      <summary>The key can be used for decryption only.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.DigitalSignature">
      <summary>The key can be used as a digital signature.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.EncipherOnly">
      <summary>The key can be used for encryption only.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.KeyAgreement">
      <summary>The key can be used to determine key agreement, such as a key created using the Diffie-Hellman key agreement algorithm.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.KeyCertSign">
      <summary>The key can be used to sign certificates.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.KeyEncipherment">
      <summary>The key can be used for key encryption.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.None">
      <summary>No key usage parameters.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509KeyUsageFlags.NonRepudiation">
      <summary>The key can be used for authentication.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509NameType">
      <summary>Specifies the type of name the X509 certificate contains.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.DnsFromAlternativeName">
      <summary>The DNS name associated with the alternative name of either the subject or the issuer of an X.509 certificate.  This value is equivalent to the <see cref="F:System.Security.Cryptography.X509Certificates.X509NameType.DnsName" /> value.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.DnsName">
      <summary>The DNS name associated with the alternative name of either the subject or issuer of an X509 certificate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.EmailName">
      <summary>The email address of the subject or issuer associated of an X509 certificate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.SimpleName">
      <summary>The simple name of a subject or issuer of an X509 certificate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.UpnName">
      <summary>The UPN name of the subject or issuer of an X509 certificate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509NameType.UrlName">
      <summary>The URL address associated with the alternative name of either the subject or issuer of an X509 certificate.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509RevocationFlag">
      <summary>Specifies which X509 certificates in the chain should be checked for revocation.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationFlag.EndCertificateOnly">
      <summary>Only the end certificate is checked for revocation.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationFlag.EntireChain">
      <summary>The entire chain of certificates is checked for revocation.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationFlag.ExcludeRoot">
      <summary>The entire chain, except the root certificate, is checked for revocation.</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509RevocationMode">
      <summary>Specifies the mode used to check for X509 certificate revocation.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationMode.NoCheck">
      <summary>No revocation check is performed on the certificate.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationMode.Offline">
      <summary>A revocation check is made using a cached certificate revocation list (CRL).</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509RevocationMode.Online">
      <summary>A revocation check is made using an online certificate revocation list (CRL).</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509SignatureGenerator">
      <summary>Base class for building encoded signatures as needed for X.509 certificates.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SignatureGenerator.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509SignatureGenerator" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SignatureGenerator.BuildPublicKey">
      <summary>When overridden in a derived class, produces the certificate's public key that has the correctly encoded <see cref="T:System.Security.Cryptography.Oid" />, public key parameters and public key values.</summary>
      <returns>The certificate's public key.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SignatureGenerator.CreateForECDsa(System.Security.Cryptography.ECDsa)">
      <summary>Creates an <see cref="T:System.Security.Cryptography.X509Certificates.X509SignatureGenerator" /> object for ECDSA signatures using the specified private key.</summary>
      <param name="key">The private key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509SignatureGenerator" /> object for ECDSA signatures.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SignatureGenerator.CreateForRSA(System.Security.Cryptography.RSA,System.Security.Cryptography.RSASignaturePadding)">
      <summary>Creates an <see cref="T:System.Security.Cryptography.X509Certificates.X509SignatureGenerator" /> object for RSA signatures using the specified private key and padding mode.</summary>
      <param name="key">The private key.</param>
      <param name="signaturePadding">The padding mode.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> is <see langword="null" />.  
  
 -or-  
  
 <paramref name="signaturePadding" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">The specified padding mode is not valid for this algorithm.</exception>
      <returns>An <see cref="T:System.Security.Cryptography.X509Certificates.X509SignatureGenerator" /> object for RSA signatures.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SignatureGenerator.GetSignatureAlgorithmIdentifier(System.Security.Cryptography.HashAlgorithmName)">
      <summary>When overridden in a derived class, encodes the X.509 algorithm identifier for this signature.</summary>
      <param name="hashAlgorithm">The hash algorithm to use for encoding.</param>
      <returns>The encoded value for the X.509 algorithm identifier.</returns>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SignatureGenerator.SignData(System.Byte[],System.Security.Cryptography.HashAlgorithmName)">
      <summary>When overridden in a derived class, produces a signature for the specified data using the specified hash algorithm and encodes the results appropriately for X.509 signature values.</summary>
      <param name="data">The input data for which to produce the signature.</param>
      <param name="hashAlgorithm">The hash algorithm to use to produce the signature.</param>
      <returns>The X.509 signature for the specified data.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509SignatureGenerator.PublicKey">
      <summary>Gets the public key associated with the private key with which signatures are being generated.</summary>
      <returns>The public key associated with the private key with which signatures are being generated.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509Store">
      <summary>Represents an X.509 store, which is a physical store where certificates are persisted and managed. This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> class using the personal certificates store of the current user.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor(System.IntPtr)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> class using an Intptr handle to an <see langword="HCERTSTORE" /> store.</summary>
      <param name="storeHandle">A handle to an <see langword="HCERTSTORE" /> store.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="storeHandle" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The <paramref name="storeHandle" /> parameter points to an invalid context.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor(System.Security.Cryptography.X509Certificates.StoreLocation)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> class using the personal certificate store from the specified store location value.</summary>
      <param name="storeLocation">One of the enumeration values that specifies the location of the X.509 certificate store.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor(System.Security.Cryptography.X509Certificates.StoreName)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> class using the specified store name from the current user's certificate stores.</summary>
      <param name="storeName">One of the enumeration values that specifies the name of the X.509 certificate store.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="storeName" /> is not a valid name.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor(System.Security.Cryptography.X509Certificates.StoreName,System.Security.Cryptography.X509Certificates.StoreLocation)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> class using the specified <see cref="T:System.Security.Cryptography.X509Certificates.StoreName" /> and <see cref="T:System.Security.Cryptography.X509Certificates.StoreLocation" /> values.</summary>
      <param name="storeName">One of the enumeration values that specifies the name of the X.509 certificate store.</param>
      <param name="storeLocation">One of the enumeration values that specifies the location of the X.509 certificate store.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="storeLocation" /> is not a valid location or <paramref name="storeName" /> is not a valid name.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor(System.Security.Cryptography.X509Certificates.StoreName,System.Security.Cryptography.X509Certificates.StoreLocation,System.Security.Cryptography.X509Certificates.OpenFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> class using the specified store name and store location values, then opens it using the specified flags.</summary>
      <param name="storeName">One of the enumeration values that specifies the name of the X.509 certificate store.</param>
      <param name="storeLocation">One of the enumeration values that specifies the location of the X.509 certificate store.</param>
      <param name="flags">A bitwise combination of enumeration values that specifies the way to open the X.509 certificate store.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="storeLocation" /> is not a valid location or <paramref name="storeName" /> is not a valid name.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The store cannot be opened.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> class using the specified store name.</summary>
      <param name="storeName">The name of the certificate store.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor(System.String,System.Security.Cryptography.X509Certificates.StoreLocation)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> class using a specified store name and store location.</summary>
      <param name="storeName">The name of the certificate store.</param>
      <param name="storeLocation">One of the enumeration values that specifies the location of the X.509 certificate store.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="storeLocation" /> contains invalid values.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.#ctor(System.String,System.Security.Cryptography.X509Certificates.StoreLocation,System.Security.Cryptography.X509Certificates.OpenFlags)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" /> class using the specified store name and store location values, then opens it using the specified flags.</summary>
      <param name="storeName">The name of the certificate store.</param>
      <param name="storeLocation">One of the enumeration values that specifies the location of the X.509 certificate store.</param>
      <param name="flags">A bitwise combination of enumeration values that specifies the way to open the X.509 certificate store.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="storeLocation" /> is not a valid location.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Add(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Adds a certificate to an X.509 certificate store.</summary>
      <param name="certificate">The certificate to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The certificate could not be added to the store.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.AddRange(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>Adds a collection of certificates to an X.509 certificate store.</summary>
      <param name="certificates">The collection of certificates to add.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Close">
      <summary>Closes an X.509 certificate store.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Dispose">
      <summary>Releases the resources used by this <see cref="T:System.Security.Cryptography.X509Certificates.X509Store" />.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Open(System.Security.Cryptography.X509Certificates.OpenFlags)">
      <summary>Opens an X.509 certificate store or creates a new store, depending on <see cref="T:System.Security.Cryptography.X509Certificates.OpenFlags" /> flag settings.</summary>
      <param name="flags">A bitwise combination of enumeration values that specifies the way to open the X.509 certificate store.</param>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The store cannot be opened as requested.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
      <exception cref="T:System.ArgumentException">The store contains invalid values.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.Remove(System.Security.Cryptography.X509Certificates.X509Certificate2)">
      <summary>Removes a certificate from an X.509 certificate store.</summary>
      <param name="certificate">The certificate to remove.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificate" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509Store.RemoveRange(System.Security.Cryptography.X509Certificates.X509Certificate2Collection)">
      <summary>Removes a range of certificates from an X.509 certificate store.</summary>
      <param name="certificates">A range of certificates to remove.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="certificates" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the required permission.</exception>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.Certificates">
      <summary>Returns a collection of certificates located in an X.509 certificate store.</summary>
      <returns>A collection of certificates.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.IsOpen">
      <summary>Gets a value that indicates whether the instance is connected to an open certificate store.</summary>
      <returns>
        <see langword="true" /> if the instance is connected to an open certificate store; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.Location">
      <summary>Gets the location of the X.509 certificate store.</summary>
      <returns>The location of the certificate store.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.Name">
      <summary>Gets the name of the X.509 certificate store.</summary>
      <returns>The name of the certificate store.</returns>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509Store.StoreHandle">
      <summary>Gets an <see cref="T:System.IntPtr" /> handle to an <see langword="HCERTSTORE" /> store.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The store is not open.</exception>
      <returns>A handle to an <see langword="HCERTSTORE" /> store.</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension">
      <summary>Defines a string that identifies a certificate's subject key identifier (SKI). This class cannot be inherited.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> class.</summary>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Byte[],System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> class using a byte array and a value that identifies whether the extension is critical.</summary>
      <param name="subjectKeyIdentifier">A byte array that represents data to use to create the extension.</param>
      <param name="critical">
        <see langword="true" /> if the extension is critical; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.ReadOnlySpan{System.Byte},System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> class using a read-only span of bytes and a value that identifies whether the extension is critical.</summary>
      <param name="subjectKeyIdentifier">A read-only span of bytes that represents data to use to create the extension.</param>
      <param name="critical">
        <see langword="true" /> if the extension is critical; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Security.Cryptography.AsnEncodedData,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> class using encoded data and a value that identifies whether the extension is critical.</summary>
      <param name="encodedSubjectKeyIdentifier">The <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object to use to create the extension.</param>
      <param name="critical">
        <see langword="true" /> if the extension is critical; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Security.Cryptography.X509Certificates.PublicKey,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> class using a public key and a value indicating whether the extension is critical.</summary>
      <param name="key">A <see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" /> object to create a subject key identifier (SKI) from.</param>
      <param name="critical">
        <see langword="true" /> if the extension is critical; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.Security.Cryptography.X509Certificates.PublicKey,System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> class using a public key, a hash algorithm identifier, and a value indicating whether the extension is critical.</summary>
      <param name="key">A <see cref="T:System.Security.Cryptography.X509Certificates.PublicKey" /> object to create a subject key identifier (SKI) from.</param>
      <param name="algorithm">One of the <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm" /> values that identifies which hash algorithm to use.</param>
      <param name="critical">
        <see langword="true" /> if the extension is critical; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.#ctor(System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> class using a string and a value that identifies whether the extension is critical.</summary>
      <param name="subjectKeyIdentifier">A string, encoded in hexadecimal format, that represents the subject key identifier (SKI) for a certificate.</param>
      <param name="critical">
        <see langword="true" /> if the extension is critical; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.CopyFrom(System.Security.Cryptography.AsnEncodedData)">
      <summary>Creates a new instance of the <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> class by copying information from encoded data.</summary>
      <param name="asnEncodedData">The <see cref="T:System.Security.Cryptography.AsnEncodedData" /> object to use to create the extension.</param>
    </member>
    <member name="P:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension.SubjectKeyIdentifier">
      <summary>Gets a string that represents the subject key identifier (SKI) for a certificate.</summary>
      <exception cref="T:System.Security.Cryptography.CryptographicException">The extension cannot be decoded.</exception>
      <returns>A string, encoded in hexadecimal format, that represents the subject key identifier (SKI).</returns>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm">
      <summary>Defines the type of hash algorithm to use with the <see cref="T:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierExtension" /> class.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm.CapiSha1">
      <summary>The subject key identifier (SKI) is composed of a 160-bit SHA-1 hash of the encoded public key (including the tag, length, and number of unused bits).</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm.Sha1">
      <summary>The SKI is composed of the 160-bit SHA-1 hash of the value of the public key (excluding the tag, length, and number of unused bits).</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509SubjectKeyIdentifierHashAlgorithm.ShortSha1">
      <summary>The SKI is composed of a four-bit type field with the value 0100, followed by the least significant 60 bits of the SHA-1 hash of the value of the public key (excluding the tag, length, and number of unused bit string bits)</summary>
    </member>
    <member name="T:System.Security.Cryptography.X509Certificates.X509VerificationFlags">
      <summary>Specifies conditions under which verification of certificates in the X509 chain should be conducted.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.AllFlags">
      <summary>All flags pertaining to verification are included.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.AllowUnknownCertificateAuthority">
      <summary>Ignore that the chain cannot be verified due to an unknown certificate authority (CA) or partial chains.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreCertificateAuthorityRevocationUnknown">
      <summary>Ignore that the certificate authority revocation is unknown when determining certificate verification.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreCtlNotTimeValid">
      <summary>Ignore that the certificate trust list (CTL) is not valid, for reasons such as the CTL has expired, when determining certificate verification.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreCtlSignerRevocationUnknown">
      <summary>Ignore that the certificate trust list (CTL) signer revocation is unknown when determining certificate verification.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreEndRevocationUnknown">
      <summary>Ignore that the end certificate (the user certificate) revocation is unknown when determining certificate verification.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreInvalidBasicConstraints">
      <summary>Ignore that the basic constraints are not valid when determining certificate verification.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreInvalidName">
      <summary>Ignore that the certificate has an invalid name when determining certificate verification.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreInvalidPolicy">
      <summary>Ignore that the certificate has invalid policy when determining certificate verification.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreNotTimeNested">
      <summary>Ignore that the CA (certificate authority) certificate and the issued certificate have validity periods that are not nested when verifying the certificate. For example, the CA cert can be valid from January 1 to December 1 and the issued certificate from January 2 to December 2, which would mean the validity periods are not nested.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreNotTimeValid">
      <summary>Ignore certificates in the chain that are not valid either because they have expired or they are not yet in effect when determining certificate validity.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreRootRevocationUnknown">
      <summary>Ignore that the root revocation is unknown when determining certificate verification.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.IgnoreWrongUsage">
      <summary>Ignore that the certificate was not issued for the current use when determining certificate verification.</summary>
    </member>
    <member name="F:System.Security.Cryptography.X509Certificates.X509VerificationFlags.NoFlag">
      <summary>No flags pertaining to verification are included.</summary>
    </member>
  </members>
</doc>